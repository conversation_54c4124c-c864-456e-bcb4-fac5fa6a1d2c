<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Carbon\Carbon;

class Estimate extends Model
{
    protected $fillable = [
        'estimate_number',
        'customer_id',
        'location_id',
        'estimate_date',
        'valid_until',
        'status',
        'subtotal',
        'discount_amount',
        'cgst_amount',
        'sgst_amount',
        'igst_amount',
        'total_gst',
        'total_amount',
        'notes',
        'terms_conditions',
        'status_notes',
        'status_updated_at',
        'status_updated_by',
        'converted_to_invoice_id',
        'converted_at',
        'converted_by',
        'extended_at',
        'extended_by',
        'extension_notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'estimate_date' => 'date',
        'valid_until' => 'date',
        'status_updated_at' => 'datetime',
        'converted_at' => 'datetime',
        'extended_at' => 'datetime',
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'cgst_amount' => 'decimal:2',
        'sgst_amount' => 'decimal:2',
        'igst_amount' => 'decimal:2',
        'total_gst' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(EstimateItem::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function statusUpdatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'status_updated_by');
    }

    public function convertedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'converted_by');
    }

    public function extendedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'extended_by');
    }

    public function convertedToInvoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'converted_to_invoice_id');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeConverted($query)
    {
        return $query->where('status', 'converted');
    }

    public function scopeValid($query)
    {
        return $query->where('valid_until', '>=', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('valid_until', '<', now());
    }

    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->whereBetween('valid_until', [now(), now()->addDays($days)]);
    }

    // Accessors
    public function getIsValidAttribute()
    {
        return $this->valid_until >= now();
    }

    public function getIsExpiredAttribute()
    {
        return $this->valid_until < now();
    }

    public function getIsExpiringSoonAttribute()
    {
        return $this->valid_until >= now() && $this->valid_until <= now()->addDays(7);
    }

    public function getDaysUntilExpiryAttribute()
    {
        return now()->diffInDays($this->valid_until, false);
    }

    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'converted' => 'Converted',
            'cancelled' => 'Cancelled',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'yellow',
            'approved' => 'green',
            'rejected' => 'red',
            'converted' => 'blue',
            'cancelled' => 'gray',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getFormattedTotalAttribute()
    {
        return '₹' . number_format($this->total_amount, 2);
    }

    // Methods
    public function convertToInvoice()
    {
        $invoice = Invoice::create([
            'invoice_number' => app(\App\Services\InvoiceNumberService::class)->generateInvoiceNumber($this->location_id),
            'customer_id' => $this->customer_id,
            'location_id' => $this->location_id,
            'estimate_id' => $this->id,
            'invoice_date' => now(),
            'due_date' => now()->addDays(30),
            'subtotal' => $this->subtotal,
            'discount_amount' => $this->discount_amount,
            'cgst_amount' => $this->cgst_amount,
            'sgst_amount' => $this->sgst_amount,
            'igst_amount' => $this->igst_amount,
            'total_gst' => $this->total_gst,
            'total_amount' => $this->total_amount,
            'status' => 'pending',
            'payment_status' => 'pending',
            'notes' => $this->notes,
            'created_by' => auth()->id(),
        ]);

        // Copy estimate items to invoice items
        foreach ($this->items as $estimateItem) {
            $invoice->items()->create([
                'product_id' => $estimateItem->product_id,
                'quantity' => $estimateItem->quantity,
                'unit_price' => $estimateItem->unit_price,
                'discount_percentage' => $estimateItem->discount_percentage,
                'discount_amount' => $estimateItem->discount_amount,
                'making_charges' => $estimateItem->making_charges,
                'stone_charges' => $estimateItem->stone_charges,
                'taxable_amount' => $estimateItem->taxable_amount,
                'gst_rate' => $estimateItem->gst_rate,
                'cgst_amount' => $estimateItem->cgst_amount,
                'sgst_amount' => $estimateItem->sgst_amount,
                'igst_amount' => $estimateItem->igst_amount,
                'total_amount' => $estimateItem->total_amount,
            ]);

            // Update inventory if product exists
            if ($estimateItem->product_id) {
                $inventory = Inventory::where('product_id', $estimateItem->product_id)
                                   ->where('location_id', $this->location_id)
                                   ->first();

                if ($inventory) {
                    $inventory->adjustStock(-$estimateItem->quantity, 'sale', "Sale - Invoice #{$invoice->invoice_number} (Converted from Estimate #{$this->estimate_number})", auth()->id());
                }
            }
        }

        return $invoice;
    }

    public function extendValidity($newDate, $notes = null)
    {
        $this->update([
            'valid_until' => $newDate,
            'extension_notes' => $notes,
            'extended_at' => now(),
            'extended_by' => auth()->id(),
        ]);

        return $this;
    }

    public function approve($notes = null)
    {
        $this->update([
            'status' => 'approved',
            'status_notes' => $notes,
            'status_updated_at' => now(),
            'status_updated_by' => auth()->id(),
        ]);

        return $this;
    }

    public function reject($notes = null)
    {
        $this->update([
            'status' => 'rejected',
            'status_notes' => $notes,
            'status_updated_at' => now(),
            'status_updated_by' => auth()->id(),
        ]);

        return $this;
    }

    public function cancel($notes = null)
    {
        $this->update([
            'status' => 'cancelled',
            'status_notes' => $notes,
            'status_updated_at' => now(),
            'status_updated_by' => auth()->id(),
        ]);

        return $this;
    }
}
