<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

class Product extends Model
{

    protected $fillable = [
        'name',
        'slug',
        'sku',
        'barcode',
        'huid_number',
        'tag_number',
        'category_id',
        'metal_id',
        'hsn_code_id',
        'description',
        'gross_weight',
        'net_weight',
        'stone_weight',
        'stone_pieces',
        'wastage_percentage',
        'making_charge_per_gram',
        'making_charge_fixed',
        'other_charges',
        'size_options',
        'customization_options',
        'is_hallmarked',
        'hallmark_number',
        'hallmark_date',
        'is_active',
        'is_featured',
        'view_count',
        'seo_meta',
        'purity',
        'total_amount',
        'stone_charges',
        'labour_charges',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'gross_weight' => 'decimal:3',
        'net_weight' => 'decimal:3',
        'stone_weight' => 'decimal:3',
        'wastage_percentage' => 'decimal:2',
        'making_charge_per_gram' => 'decimal:2',
        'making_charge_fixed' => 'decimal:2',
        'other_charges' => 'decimal:2',
        'size_options' => 'array',
        'customization_options' => 'array',
        'is_hallmarked' => 'boolean',
        'hallmark_date' => 'date',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'view_count' => 'integer',
        'seo_meta' => 'array',
        'total_amount' => 'decimal:2',
        'stone_charges' => 'decimal:2',
        'labour_charges' => 'decimal:2',
        'purity' => 'decimal:2',
    ];

    // Relationships
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function metal(): BelongsTo
    {
        return $this->belongsTo(Metal::class);
    }

    public function hsnCode(): BelongsTo
    {
        return $this->belongsTo(HsnCode::class);
    }

    public function images(): HasMany
    {
        return $this->hasMany(ProductImage::class);
    }

    public function stones(): HasMany
    {
        return $this->hasMany(ProductStone::class);
    }

    public function inventory(): HasOne
    {
        return $this->hasOne(Inventory::class);
    }

    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    // Jewelry-specific calculation methods
    public function calculatePrice(float $metalRatePerGram = null, array $options = []): array
    {
        $metalRate = $metalRatePerGram ?? $this->metal->current_rate_per_gram;

        // Base metal value calculation
        $metalValue = $this->net_weight * $metalRate;

        // Wastage calculation
        $wastageAmount = $metalValue * ($this->wastage_percentage / 100);

        // Making charges calculation
        $makingCharges = $this->making_charge_fixed;
        if ($this->making_charge_per_gram > 0) {
            $makingCharges += ($this->net_weight * $this->making_charge_per_gram);
        }

        // Stone charges calculation
        $stoneCharges = $this->stones->sum(function ($stone) {
            return $stone->weight * $stone->rate_per_unit;
        });

        // Subtotal before taxes
        $subtotal = $metalValue + $wastageAmount + $makingCharges + $stoneCharges + $this->other_charges;

        // Tax calculation (using HSN code rates)
        $taxRates = $this->hsnCode ? [
            'cgst' => $this->hsnCode->cgst_rate,
            'sgst' => $this->hsnCode->sgst_rate,
            'igst' => $this->hsnCode->igst_rate,
        ] : ['cgst' => 0, 'sgst' => 0, 'igst' => 0];

        $cgstAmount = $subtotal * ($taxRates['cgst'] / 100);
        $sgstAmount = $subtotal * ($taxRates['sgst'] / 100);
        $igstAmount = $subtotal * ($taxRates['igst'] / 100);
        $totalTax = $cgstAmount + $sgstAmount + $igstAmount;

        $totalPrice = $subtotal + $totalTax;

        return [
            'metal_value' => round($metalValue, 2),
            'wastage_amount' => round($wastageAmount, 2),
            'making_charges' => round($makingCharges, 2),
            'stone_charges' => round($stoneCharges, 2),
            'other_charges' => round($this->other_charges, 2),
            'subtotal' => round($subtotal, 2),
            'cgst_amount' => round($cgstAmount, 2),
            'sgst_amount' => round($sgstAmount, 2),
            'igst_amount' => round($igstAmount, 2),
            'total_tax' => round($totalTax, 2),
            'total_price' => round($totalPrice, 2),
            'metal_rate_used' => $metalRate,
        ];
    }

    public function getPrimaryImageAttribute(): ?string
    {
        $primaryImage = $this->images()->where('is_primary', true)->first();
        return $primaryImage ? $primaryImage->image_path : null;
    }

    public function getFormattedWeightAttribute(): string
    {
        return number_format($this->gross_weight, 3) . 'g';
    }

    public function getFormattedNetWeightAttribute(): string
    {
        return number_format($this->net_weight, 3) . 'g';
    }

    // Auto-generate SKU and slug on creation
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->sku)) {
                $product->sku = static::generateSku($product);
            }
            if (empty($product->tag_number)) {
                $product->tag_number = static::generateTagNumber();
            }
            if (empty($product->slug)) {
                $product->slug = $product->generateSlug();
            }
            if (empty($product->barcode)) {
                $product->barcode = static::generateBarcode();
            }
            if (empty($product->huid_number) && $product->metal?->name === 'Gold') {
                $product->huid_number = static::generateHUID();
            }

            // Set created_by
            $product->created_by = auth()->id();
        });

        static::updating(function ($product) {
            // Set updated_by
            $product->updated_by = auth()->id();
        });

        static::created(function ($product) {
            // Create initial inventory record if it doesn't exist
            if (!$product->inventory()->exists()) {
                $product->inventory()->create([
                    'location_id' => auth()->user()?->location_id ?? 1,
                    'quantity' => 1,
                    'reserved_quantity' => 0,
                    'created_by' => auth()->id(),
                ]);
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && !$product->isDirty('slug')) {
                $product->slug = $product->generateSlug();
            }
        });
    }

    private static function generateSku(Product $product): string
    {
        $categoryCode = $product->category ? Str::upper(Str::substr($product->category->name, 0, 3)) : 'GEN';
        $metalCode = $product->metal ? Str::upper(Str::substr($product->metal->purity, 0, 3)) : 'MET';
        $timestamp = now()->format('ymd');
        $random = Str::upper(Str::random(3));

        return "{$categoryCode}-{$metalCode}-{$timestamp}-{$random}";
    }

    private static function generateTagNumber(): string
    {
        $prefix = 'TAG';
        $timestamp = now()->format('ymdHis');
        $random = rand(100, 999);

        return "{$prefix}{$timestamp}{$random}";
    }

    /**
     * Website-specific attributes and methods
     */
    public function getSellingPriceAttribute(): float
    {
        $pricing = $this->calculatePrice();
        return $pricing['total_price'];
    }

    public function getFormattedSellingPriceAttribute(): string
    {
        return '₹' . number_format($this->selling_price, 2);
    }

    public function getStockQuantityAttribute(): int
    {
        return $this->inventory ? $this->inventory->quantity_available : 0;
    }

    public function getMetalTypeAttribute(): ?string
    {
        return $this->metal ? $this->metal->name : null;
    }

    public function getMetalPurityAttribute(): ?string
    {
        return $this->metal ? $this->metal->purity : null;
    }

    public function getWeightAttribute(): float
    {
        return $this->gross_weight;
    }

    public function getIsCustomizableAttribute(): bool
    {
        return !empty($this->customization_options);
    }

    public function getViewCountAttribute(): int
    {
        // This would be stored in a separate table or cache
        return 0; // Placeholder
    }

    /**
     * Generate slug from name
     */
    public function generateSlug(): string
    {
        $slug = Str::slug($this->name);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug'; // Using slug for SEO-friendly URLs
    }

    /**
     * Scopes for website
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeInStock($query)
    {
        return $query->whereHas('inventory', function ($q) {
            $q->where('quantity_available', '>', 0);
        });
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeByMetal($query, $metalType)
    {
        return $query->whereHas('metal', function ($q) use ($metalType) {
            $q->where('name', $metalType);
        });
    }

    public function scopePriceRange($query, $minPrice, $maxPrice)
    {
        // This is a simplified version - in reality, you'd need to calculate prices
        return $query->whereBetween('making_charge_fixed', [$minPrice, $maxPrice]);
    }

    /**
     * Get product specifications for display
     */
    public function getSpecificationsAttribute(): array
    {
        $specs = [
            'SKU' => $this->sku,
            'Gross Weight' => $this->formatted_weight,
            'Net Weight' => $this->formatted_net_weight,
        ];

        if ($this->metal) {
            $specs['Metal'] = $this->metal->name;
            $specs['Purity'] = $this->metal->purity;
        }

        if ($this->stone_pieces > 0) {
            $specs['Stone Pieces'] = $this->stone_pieces;
            $specs['Stone Weight'] = number_format($this->stone_weight, 3) . 'g';
        }

        if ($this->is_hallmarked) {
            $specs['Hallmarked'] = 'Yes';
            if ($this->hallmark_number) {
                $specs['Hallmark Number'] = $this->hallmark_number;
            }
        }

        return $specs;
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'sku' => $this->sku,
            'category' => $this->category->name,
            'selling_price' => $this->selling_price,
            'formatted_price' => $this->formatted_selling_price,
            'stock_quantity' => $this->stock_quantity,
            'is_active' => $this->is_active,
            'metal_type' => $this->metal_type,
            'metal_purity' => $this->metal_purity,
            'weight' => $this->weight,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Calculate current price based on metal rates
     */
    public function calculateCurrentPrice(): float
    {
        if (!$this->metal || !$this->net_weight) {
            return $this->total_amount ?? 0;
        }

        $metalRate = $this->metal->current_rate_per_gram ?? 0;
        $metalValue = $this->net_weight * $metalRate;

        // Add wastage
        $wastageAmount = $metalValue * ($this->wastage_percentage / 100);

        // Add making charges
        $makingCharges = $this->making_charge_per_gram
            ? ($this->net_weight * $this->making_charge_per_gram)
            : ($this->making_charge_fixed ?? 0);

        // Add stone charges and other charges
        $stoneCharges = $this->stone_charges ?? 0;
        $otherCharges = $this->other_charges ?? 0;
        $labourCharges = $this->labour_charges ?? 0;

        return $metalValue + $wastageAmount + $makingCharges + $stoneCharges + $otherCharges + $labourCharges;
    }



    /**
     * Generate unique barcode
     */
    public static function generateBarcode(): string
    {
        do {
            $barcode = '8901' . str_pad(mt_rand(1, 99999999), 8, '0', STR_PAD_LEFT);
        } while (static::where('barcode', $barcode)->exists());

        return $barcode;
    }

    /**
     * Generate HUID number
     */
    public static function generateHUID(): string
    {
        $prefix = 'HUID';
        $year = now()->format('Y');
        $sequence = static::whereYear('created_at', now()->year)->count() + 1;

        return $prefix . $year . str_pad($sequence, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get inventory status
     */
    public function getInventoryStatusAttribute(): string
    {
        $quantity = $this->inventory_count ?? 0;

        if ($quantity <= 0) {
            return 'out_of_stock';
        } elseif ($quantity <= 5) {
            return 'low_stock';
        } else {
            return 'in_stock';
        }
    }

    /**
     * Get inventory status label
     */
    public function getInventoryStatusLabelAttribute(): string
    {
        return match($this->inventory_status) {
            'out_of_stock' => 'Out of Stock',
            'low_stock' => 'Low Stock',
            'in_stock' => 'In Stock',
            default => 'Unknown'
        };
    }

    /**
     * Get age in days
     */
    public function getAgeInDaysAttribute(): int
    {
        return $this->created_at->diffInDays(now());
    }

    /**
     * Check if product is old (more than 365 days)
     */
    public function getIsOldAttribute(): bool
    {
        return $this->age_in_days > 365;
    }


}
