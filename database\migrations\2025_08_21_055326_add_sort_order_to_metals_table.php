<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('metals', function (Blueprint $table) {
            $table->integer('sort_order')->default(0)->after('is_active');
        });

        // Set default sort order values for existing metals
        DB::statement("UPDATE metals SET sort_order = id WHERE sort_order = 0");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('metals', function (Blueprint $table) {
            $table->dropColumn('sort_order');
        });
    }
};
