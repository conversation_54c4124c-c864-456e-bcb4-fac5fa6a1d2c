<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PrintTemplate;

class PrintTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'name' => 'Standard Invoice Template',
                'code' => 'invoice_standard',
                'template_type' => 'invoice',
                'description' => 'Standard GST compliant invoice template for thermal printers',
                'template_content' => $this->getInvoiceTemplate(),
                'paper_width_mm' => 80,
                'is_system_template' => true,
                'is_active' => true,
                'is_default' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Standard Receipt Template',
                'code' => 'receipt_standard',
                'template_type' => 'receipt',
                'description' => 'Standard receipt template for thermal printers',
                'template_content' => $this->getReceiptTemplate(),
                'paper_width_mm' => 80,
                'is_system_template' => true,
                'is_active' => true,
                'is_default' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Standard Estimate Template',
                'code' => 'estimate_standard',
                'template_type' => 'estimate',
                'description' => 'Standard estimate/quotation template for thermal printers',
                'template_content' => $this->getEstimateTemplate(),
                'paper_width_mm' => 80,
                'is_system_template' => true,
                'is_active' => true,
                'is_default' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Jewelry Tag Template',
                'code' => 'tag_jewelry',
                'template_type' => 'tag',
                'description' => 'Jewelry price tag template with barcode and QR code',
                'template_content' => $this->getJewelryTagTemplate(),
                'paper_width_mm' => 58,
                'is_system_template' => true,
                'is_active' => true,
                'is_default' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Barcode Label Template',
                'code' => 'barcode_standard',
                'template_type' => 'barcode',
                'description' => 'Standard barcode label template',
                'template_content' => $this->getBarcodeTemplate(),
                'paper_width_mm' => 58,
                'is_system_template' => true,
                'is_active' => true,
                'is_default' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'QR Code Label Template',
                'code' => 'qrcode_standard',
                'template_type' => 'qr_code',
                'description' => 'Standard QR code label template',
                'template_content' => $this->getQRCodeTemplate(),
                'paper_width_mm' => 58,
                'is_system_template' => true,
                'is_active' => true,
                'is_default' => true,
                'created_by' => 1,
            ],
        ];

        foreach ($templates as $template) {
            PrintTemplate::updateOrCreate(
                ['code' => $template['code']],
                $template
            );
        }
    }

    private function getInvoiceTemplate()
    {
        return "|CENTER||BOLD|{{company_name}}|BOLD|
|CENTER|{{company_address}}
|CENTER|Ph: {{company_phone}}
|CENTER|Email: {{company_email}}
|CENTER|GSTIN: {{company_gstin}}
[SEPARATOR]
|CENTER||BOLD|TAX INVOICE|BOLD|
[SEPARATOR]
Invoice No: {{invoice_number}}
Date: {{invoice_date}}
Due Date: {{due_date}}

Bill To:
{{customer_name}}
{{customer_phone}}
{{customer_address}}
GSTIN: {{customer_gstin}}
[SEPARATOR]
|BOLD|ITEM DETAILS|BOLD|
[SEPARATOR]
{{#items}}
{{product_name}}
HSN: {{hsn_code}}
Qty: {{quantity}} x ₹{{unit_price}}
Tax ({{tax_rate}}%): ₹{{tax_amount}}
|RIGHT|₹{{total_price}}
[SEPARATOR]
{{/items}}

|RIGHT|Subtotal: ₹{{subtotal}}
|RIGHT|Total Tax: ₹{{total_tax}}
|RIGHT||BOLD|TOTAL: ₹{{total_amount}}|BOLD|

Amount in Words:
{{amount_in_words}}

Payment Status: {{payment_status}}

{{notes}}

Terms & Conditions:
{{terms_conditions}}

|CENTER|Thank you for your business!
|CENTER|Printed on {{print_time}}
|CENTER|By: {{printed_by}}
[FEED:3]";
    }

    private function getReceiptTemplate()
    {
        return "|CENTER||BOLD|{{company_name}}|BOLD|
|CENTER||BOLD|RECEIPT|BOLD|
[SEPARATOR]
Receipt No: {{receipt_number}}
Date: {{date}}
Customer: {{customer_name}}
Phone: {{customer_phone}}
Cashier: {{cashier}}
[SEPARATOR]
{{#items}}
{{name}}
{{qty}} x ₹{{price}} = ₹{{total}}
{{/items}}
[SEPARATOR]
|RIGHT|Subtotal: ₹{{subtotal}}
|RIGHT|Tax: ₹{{tax}}
|RIGHT||BOLD|TOTAL: ₹{{total}}|BOLD|

Payment: {{payment_method}}
[SEPARATOR]
|CENTER|{{thank_you_message}}
|CENTER|Visit us again!
[FEED:2]";
    }

    private function getEstimateTemplate()
    {
        return "|CENTER||BOLD|{{company_name}}|BOLD|
|CENTER|{{company_address}}
|CENTER|Ph: {{company_phone}}
[SEPARATOR]
|CENTER||BOLD|ESTIMATE|BOLD|
[SEPARATOR]
Estimate No: {{estimate_number}}
Date: {{estimate_date}}
Valid Until: {{valid_until}}

Customer: {{customer_name}}
Phone: {{customer_phone}}
[SEPARATOR]
|BOLD|ITEM DETAILS|BOLD|
[SEPARATOR]
{{#items}}
{{product_name}}
Qty: {{quantity}} x ₹{{unit_price}}
|RIGHT|₹{{total_price}}
{{/items}}
[SEPARATOR]
|RIGHT|Subtotal: ₹{{subtotal}}
|RIGHT|Tax: ₹{{total_tax}}
|RIGHT||BOLD|TOTAL: ₹{{total_amount}}|BOLD|

{{notes}}

Prepared by: {{prepared_by}}

|CENTER|This is an estimate only
|CENTER|Prices subject to change
[FEED:3]";
    }

    private function getJewelryTagTemplate()
    {
        return "|CENTER||BOLD|{{product_name}}|BOLD|
|CENTER|{{category}} - {{metal}}
[SEPARATOR]
Tag: {{unique_tag}}
Weight: {{weight}}
Purity: {{purity}}
HUID: {{huid}}

|CENTER||LARGE|{{price}}|LARGE|

[BARCODE:{{barcode}}]

[QRCODE:{{qr_data}}]

|CENTER|{{location}}
|CENTER|{{created_date}}
[FEED:2]";
    }

    private function getBarcodeTemplate()
    {
        return "|CENTER|{{product_name}}
|CENTER|{{unique_tag}}

[BARCODE:{{barcode}}]

|CENTER|{{price}}
[FEED:2]";
    }

    private function getQRCodeTemplate()
    {
        return "|CENTER|{{product_name}}
|CENTER|{{unique_tag}}

[QRCODE:{{qr_data}}]

|CENTER|{{price}}
[FEED:2]";
    }
}
