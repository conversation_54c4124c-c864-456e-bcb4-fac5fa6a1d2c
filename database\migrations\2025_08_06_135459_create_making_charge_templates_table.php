<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('making_charge_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('metal_id')->constrained()->onDelete('cascade');
            $table->string('product_type')->nullable(); // ring, necklace, bracelet, etc.
            $table->decimal('percentage_charge', 5, 2)->default(0); // Making charge percentage
            $table->decimal('fixed_charge', 10, 2)->default(0); // Fixed making charge
            $table->decimal('min_charge', 10, 2)->default(0); // Minimum making charge
            $table->decimal('max_charge', 10, 2)->nullable(); // Maximum making charge
            $table->decimal('wastage_percentage', 5, 2)->default(0); // Wastage percentage
            $table->decimal('weight_range_min', 8, 3)->nullable(); // Minimum weight for this template
            $table->decimal('weight_range_max', 8, 3)->nullable(); // Maximum weight for this template
            $table->json('additional_charges')->nullable(); // Stone setting, polishing, etc.
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->foreignId('location_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['metal_id', 'product_type', 'is_active']);
            $table->index(['location_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('making_charge_templates');
    }
};
