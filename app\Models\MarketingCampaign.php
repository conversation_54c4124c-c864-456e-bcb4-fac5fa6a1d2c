<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class MarketingCampaign extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'campaign_type',
        'channel',
        'target_audience',
        'message_template',
        'subject_line',
        'scheduled_at',
        'started_at',
        'completed_at',
        'status',
        'budget',
        'cost_per_contact',
        'target_criteria',
        'success_metrics',
        'created_by',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'budget' => 'decimal:2',
        'cost_per_contact' => 'decimal:2',
        'target_criteria' => 'array',
        'success_metrics' => 'array',
    ];

    /**
     * Relationships
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function customers(): BelongsToMany
    {
        return $this->belongsToMany(Customer::class, 'campaign_customers')
                    ->withPivot(['sent_at', 'opened_at', 'clicked_at', 'responded_at', 'status'])
                    ->withTimestamps();
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePaused($query)
    {
        return $query->where('status', 'paused');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('campaign_type', $type);
    }

    public function scopeByChannel($query, $channel)
    {
        return $query->where('channel', $channel);
    }

    public function scopeRunning($query)
    {
        return $query->whereIn('status', ['active', 'scheduled'])
                    ->where(function ($q) {
                        $q->whereNull('completed_at')
                          ->orWhere('completed_at', '>', now());
                    });
    }
}
