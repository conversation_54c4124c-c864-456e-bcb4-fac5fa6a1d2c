<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use Carbon\Carbon;

class InventoryItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'location_id',
        'unique_tag',
        'barcode',
        'qr_code',
        'huid_number',
        'certificate_number',
        'actual_weight',
        'stone_weight',
        'net_metal_weight',
        'purity',
        'dimensions',
        'cost_price',
        'making_charges',
        'stone_charges',
        'other_charges',
        'total_cost',
        'selling_price',
        'mrp',
        'status',
        'condition',
        'condition_notes',
        'shelf_location',
        'display_location',
        'is_on_display',
        'photos',
        'certificates',
        'appraisal_documents',
        'purchase_order_id',
        'invoice_id',
        'purchase_date',
        'sale_date',
        'sold_to_customer_id',
        'insurance_value',
        'insurance_policy_number',
        'warranty_start_date',
        'warranty_end_date',
        'warranty_terms',
        'notes',
        'custom_fields',
        'last_verified_at',
        'last_verified_by',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'actual_weight' => 'decimal:3',
        'stone_weight' => 'decimal:3',
        'net_metal_weight' => 'decimal:3',
        'dimensions' => 'array',
        'cost_price' => 'decimal:2',
        'making_charges' => 'decimal:2',
        'stone_charges' => 'decimal:2',
        'other_charges' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'mrp' => 'decimal:2',
        'insurance_value' => 'decimal:2',
        'is_on_display' => 'boolean',
        'photos' => 'array',
        'certificates' => 'array',
        'appraisal_documents' => 'array',
        'custom_fields' => 'array',
        'purchase_date' => 'date',
        'sale_date' => 'date',
        'warranty_start_date' => 'date',
        'warranty_end_date' => 'date',
        'last_verified_at' => 'datetime',
    ];

    /**
     * Boot method to generate unique tag
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($item) {
            if (!$item->unique_tag) {
                $item->unique_tag = static::generateUniqueTag();
            }

            // Calculate total cost if not provided
            if (!$item->total_cost) {
                $item->total_cost = $item->cost_price + $item->making_charges + $item->stone_charges + $item->other_charges;
            }
        });

        static::updating(function ($item) {
            $item->updated_by = auth()->id();

            // Recalculate total cost if component costs change
            if ($item->isDirty(['cost_price', 'making_charges', 'stone_charges', 'other_charges'])) {
                $item->total_cost = $item->cost_price + $item->making_charges + $item->stone_charges + $item->other_charges;
            }
        });
    }

    /**
     * Relationships
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function soldToCustomer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'sold_to_customer_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function lastVerifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'last_verified_by');
    }

    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class, 'inventory_item_id');
    }

    /**
     * Scopes
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    public function scopeReserved($query)
    {
        return $query->where('status', 'reserved');
    }

    public function scopeSold($query)
    {
        return $query->where('status', 'sold');
    }

    public function scopeOnDisplay($query)
    {
        return $query->where('is_on_display', true);
    }

    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByCondition($query, $condition)
    {
        return $query->where('condition', $condition);
    }

    public function scopeWithWarranty($query)
    {
        return $query->whereNotNull('warranty_end_date')
                    ->where('warranty_end_date', '>=', now());
    }

    public function scopeWarrantyExpiring($query, $days = 30)
    {
        return $query->whereNotNull('warranty_end_date')
                    ->whereBetween('warranty_end_date', [now(), now()->addDays($days)]);
    }

    public function scopeNeedsVerification($query, $days = 90)
    {
        return $query->where(function ($q) use ($days) {
            $q->whereNull('last_verified_at')
              ->orWhere('last_verified_at', '<', now()->subDays($days));
        });
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'available' => 'Available',
            'reserved' => 'Reserved',
            'sold' => 'Sold',
            'damaged' => 'Damaged',
            'repair' => 'Under Repair',
            'display' => 'On Display',
        ];

        return $statuses[$this->status] ?? 'Unknown';
    }

    public function getConditionDisplayAttribute()
    {
        $conditions = [
            'new' => 'New',
            'excellent' => 'Excellent',
            'good' => 'Good',
            'fair' => 'Fair',
            'poor' => 'Poor',
        ];

        return $conditions[$this->condition] ?? 'Unknown';
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'available' => 'green',
            'reserved' => 'yellow',
            'sold' => 'blue',
            'damaged' => 'red',
            'repair' => 'orange',
            'display' => 'purple',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getIsWarrantyValidAttribute()
    {
        return $this->warranty_end_date && $this->warranty_end_date >= now();
    }

    public function getWarrantyDaysRemainingAttribute()
    {
        if (!$this->warranty_end_date) {
            return null;
        }

        return max(0, now()->diffInDays($this->warranty_end_date, false));
    }

    public function getProfitMarginAttribute()
    {
        if ($this->total_cost <= 0) {
            return 0;
        }

        return (($this->selling_price - $this->total_cost) / $this->total_cost) * 100;
    }

    public function getProfitAmountAttribute()
    {
        return $this->selling_price - $this->total_cost;
    }

    public function getFormattedDimensionsAttribute()
    {
        if (!$this->dimensions) {
            return 'Not specified';
        }

        $dims = [];
        if (isset($this->dimensions['length'])) {
            $dims[] = "L: {$this->dimensions['length']}mm";
        }
        if (isset($this->dimensions['width'])) {
            $dims[] = "W: {$this->dimensions['width']}mm";
        }
        if (isset($this->dimensions['height'])) {
            $dims[] = "H: {$this->dimensions['height']}mm";
        }
        if (isset($this->dimensions['diameter'])) {
            $dims[] = "D: {$this->dimensions['diameter']}mm";
        }

        return implode(', ', $dims) ?: 'Not specified';
    }

    /**
     * Generate unique tag
     */
    public static function generateUniqueTag($prefix = null)
    {
        $prefix = $prefix ?: 'IT';
        $timestamp = now()->format('ymd');
        $random = strtoupper(Str::random(4));

        do {
            $tag = "{$prefix}{$timestamp}{$random}";
            $random = strtoupper(Str::random(4));
        } while (static::where('unique_tag', $tag)->exists());

        return $tag;
    }

    /**
     * Generate barcode
     */
    public function generateBarcode()
    {
        $barcode = '8901' . str_pad($this->id, 8, '0', STR_PAD_LEFT);

        // Calculate check digit using EAN-13 algorithm
        $checkDigit = $this->calculateEAN13CheckDigit($barcode);
        $barcode .= $checkDigit;

        $this->update(['barcode' => $barcode]);

        return $barcode;
    }

    /**
     * Generate QR code data
     */
    public function generateQRCode()
    {
        $qrData = [
            'type' => 'inventory_item',
            'id' => $this->id,
            'tag' => $this->unique_tag,
            'product' => $this->product->name,
            'location' => $this->location->name,
            'price' => $this->selling_price,
            'url' => route('admin.inventory-items.show', $this),
        ];

        $qrCode = base64_encode(json_encode($qrData));
        $this->update(['qr_code' => $qrCode]);

        return $qrCode;
    }

    /**
     * Calculate EAN-13 check digit
     */
    private function calculateEAN13CheckDigit($barcode)
    {
        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $sum += $barcode[$i] * (($i % 2 === 0) ? 1 : 3);
        }

        return (10 - ($sum % 10)) % 10;
    }

    /**
     * Mark as sold
     */
    public function markAsSold($customerId, $invoiceId, $saleDate = null)
    {
        $this->update([
            'status' => 'sold',
            'sold_to_customer_id' => $customerId,
            'invoice_id' => $invoiceId,
            'sale_date' => $saleDate ?: now(),
            'is_on_display' => false,
        ]);

        // Create stock movement
        $this->stockMovements()->create([
            'movement_type' => 'sale',
            'quantity' => 1,
            'reference_type' => 'invoice',
            'reference_id' => $invoiceId,
            'notes' => "Sold to customer ID: {$customerId}",
            'created_by' => auth()->id(),
        ]);

        return $this;
    }

    /**
     * Reserve item
     */
    public function reserve($notes = null)
    {
        $this->update([
            'status' => 'reserved',
            'condition_notes' => $notes,
        ]);

        return $this;
    }

    /**
     * Make available
     */
    public function makeAvailable()
    {
        $this->update([
            'status' => 'available',
            'sold_to_customer_id' => null,
            'invoice_id' => null,
            'sale_date' => null,
        ]);

        return $this;
    }

    /**
     * Update location
     */
    public function updateLocation($locationId, $shelfLocation = null, $displayLocation = null)
    {
        $oldLocationId = $this->location_id;

        $this->update([
            'location_id' => $locationId,
            'shelf_location' => $shelfLocation,
            'display_location' => $displayLocation,
        ]);

        // Create stock movement for transfer
        if ($oldLocationId !== $locationId) {
            $this->stockMovements()->create([
                'movement_type' => 'transfer',
                'quantity' => 1,
                'reference_type' => 'location_transfer',
                'reference_id' => $locationId,
                'notes' => "Transferred from location {$oldLocationId} to {$locationId}",
                'created_by' => auth()->id(),
            ]);
        }

        return $this;
    }

    /**
     * Verify item
     */
    public function verify($notes = null)
    {
        $this->update([
            'last_verified_at' => now(),
            'last_verified_by' => auth()->id(),
            'notes' => $notes ? ($this->notes ? $this->notes . "\n\n" . $notes : $notes) : $this->notes,
        ]);

        return $this;
    }

    /**
     * Add photo
     */
    public function addPhoto($photoPath, $description = null)
    {
        $photos = $this->photos ?: [];
        $photos[] = [
            'path' => $photoPath,
            'description' => $description,
            'uploaded_at' => now()->toISOString(),
            'uploaded_by' => auth()->id(),
        ];

        $this->update(['photos' => $photos]);

        return $this;
    }

    /**
     * Remove photo
     */
    public function removePhoto($index)
    {
        $photos = $this->photos ?: [];

        if (isset($photos[$index])) {
            // Delete file if it exists
            $photoPath = $photos[$index]['path'];
            if (file_exists(storage_path('app/public/' . $photoPath))) {
                unlink(storage_path('app/public/' . $photoPath));
            }

            unset($photos[$index]);
            $this->update(['photos' => array_values($photos)]);
        }

        return $this;
    }

    /**
     * Get primary photo
     */
    public function getPrimaryPhotoAttribute()
    {
        $photos = $this->photos;
        return $photos && count($photos) > 0 ? $photos[0] : null;
    }

    /**
     * Check if item needs verification
     */
    public function needsVerification($days = 90)
    {
        return !$this->last_verified_at || $this->last_verified_at < now()->subDays($days);
    }

    /**
     * Get age in days
     */
    public function getAgeInDaysAttribute()
    {
        return $this->created_at->diffInDays(now());
    }

    /**
     * Get days since last verification
     */
    public function getDaysSinceVerificationAttribute()
    {
        return $this->last_verified_at ? $this->last_verified_at->diffInDays(now()) : null;
    }
}
