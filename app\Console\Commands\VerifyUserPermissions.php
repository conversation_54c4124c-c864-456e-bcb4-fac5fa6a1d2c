<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class VerifyUserPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jewelsoft:verify-permissions
                            {--user= : Specific user email to check}
                            {--fix : Fix common permission issues}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify user permissions and fix common issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 JewelSoft Permission Verification');
        $this->info('===================================');

        $userEmail = $this->option('user');
        $fix = $this->option('fix');

        if ($userEmail) {
            $this->verifySpecificUser($userEmail, $fix);
        } else {
            $this->verifyAllUsers($fix);
        }

        return 0;
    }

    private function verifySpecificUser(string $email, bool $fix = false): void
    {
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("❌ User with email '{$email}' not found.");
            return;
        }

        $this->info("👤 Checking user: {$user->name} ({$user->email})");
        $this->checkUser($user, $fix);
    }

    private function verifyAllUsers(bool $fix = false): void
    {
        $users = User::with('roles')->get();
        $this->info("👥 Found {$users->count()} users to verify");
        $this->newLine();

        foreach ($users as $user) {
            $this->checkUser($user, $fix);
            $this->newLine();
        }
    }

    private function checkUser(User $user, bool $fix = false): void
    {
        $issues = [];
        $fixes = [];

        // Check if user is active
        if (!$user->is_active) {
            $issues[] = "❌ User is inactive";
        } else {
            $this->line("✅ User is active");
        }

        // Check email verification
        if (!$user->email_verified_at) {
            $issues[] = "❌ Email not verified";
            if ($fix) {
                $user->update(['email_verified_at' => now()]);
                $fixes[] = "✅ Fixed: Email verified";
            }
        } else {
            $this->line("✅ Email verified: {$user->email_verified_at}");
        }

        // Check role assignment
        $roles = $user->getRoleNames();
        if ($roles->isEmpty()) {
            $issues[] = "❌ No role assigned";
            if ($fix) {
                $user->assignRole('Customer');
                $user->update(['role' => 'Customer']);
                $fixes[] = "✅ Fixed: Assigned Customer role";
            }
        } else {
            $this->line("✅ Role: {$roles->first()}");
        }

        // Check role column sync
        $spatieRole = $user->getRoleNames()->first();
        if ($spatieRole !== $user->role) {
            $issues[] = "❌ Role column out of sync (Column: {$user->role}, Spatie: {$spatieRole})";
            if ($fix) {
                $user->update(['role' => $spatieRole]);
                $fixes[] = "✅ Fixed: Synced role column";
            }
        } else {
            $this->line("✅ Role column synced");
        }

        // Check permissions
        $permissions = $user->getAllPermissions();
        if ($permissions->isEmpty()) {
            $issues[] = "❌ No permissions assigned";
        } else {
            $this->line("✅ Permissions: {$permissions->count()}");
        }

        // Check specific dashboard permission
        if (!$user->can('view_dashboard')) {
            $issues[] = "❌ Cannot view dashboard";
        } else {
            $this->line("✅ Can view dashboard");
        }

        // Check location assignment
        if (!$user->location_id) {
            $issues[] = "❌ No location assigned";
            if ($fix && \App\Models\Location::count() > 0) {
                $user->update(['location_id' => \App\Models\Location::first()->id]);
                $fixes[] = "✅ Fixed: Assigned to first location";
            }
        } else {
            $locationName = $user->location ? $user->location->name : 'Unknown';
            $this->line("✅ Location: {$locationName}");
        }

        // Display results
        if (empty($issues) && empty($fixes)) {
            $this->info("✅ {$user->name}: All checks passed");
        } else {
            if (!empty($issues)) {
                $this->warn("⚠️  {$user->name}: Issues found:");
                foreach ($issues as $issue) {
                    $this->line("   {$issue}");
                }
            }
            if (!empty($fixes)) {
                $this->info("🔧 {$user->name}: Fixes applied:");
                foreach ($fixes as $fix) {
                    $this->line("   {$fix}");
                }
            }
        }
    }
}
