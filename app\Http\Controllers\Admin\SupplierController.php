<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SupplierController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_suppliers')->only(['index', 'show']);
        $this->middleware('permission:create_suppliers')->only(['create', 'store']);
        $this->middleware('permission:edit_suppliers')->only(['edit', 'update']);
        $this->middleware('permission:delete_suppliers')->only(['destroy']);
    }

    /**
     * Display a listing of suppliers
     */
    public function index(Request $request)
    {
        $query = Supplier::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('gst_number', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by city
        if ($request->filled('city')) {
            $query->where('city', $request->city);
        }

        // Filter by state
        if ($request->filled('state')) {
            $query->where('state', $request->state);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $suppliers = $query->paginate(20);

        // Get filter options
        $cities = Supplier::distinct()->pluck('city')->filter()->sort();
        $states = Supplier::distinct()->pluck('state')->filter()->sort();

        return view('admin.suppliers.index', compact('suppliers', 'cities', 'states'));
    }

    /**
     * Show the form for creating a new supplier
     */
    public function create()
    {
        return view('admin.suppliers.create');
    }

    /**
     * Store a newly created supplier
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'gst_number' => 'nullable|string|max:15|unique:suppliers,gst_number',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'required|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'pincode' => 'required|string|max:10',
            'contact_person' => 'nullable|string|max:255',
            'credit_limit' => 'nullable|numeric|min:0',
            'credit_days' => 'nullable|integer|min:0',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $supplier = Supplier::create($validated);

            DB::commit();

            return redirect()
                ->route('admin.suppliers.show', $supplier)
                ->with('success', 'Supplier created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Failed to create supplier: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified supplier
     */
    public function show(Supplier $supplier)
    {
        $supplier->load([
            'purchaseOrders' => function ($query) {
                $query->latest()->limit(10);
            },
            'goodsReceiptNotes' => function ($query) {
                $query->latest()->limit(5);
            },
            'supplierPayments' => function ($query) {
                $query->latest()->limit(5);
            }
        ]);

        $performanceMetrics = $supplier->getPerformanceMetrics();
        $recentOrders = $supplier->getRecentOrders();
        $topProducts = $supplier->getTopProducts();

        return view('admin.suppliers.show', compact(
            'supplier',
            'performanceMetrics',
            'recentOrders',
            'topProducts'
        ));
    }

    /**
     * Show the form for editing the specified supplier
     */
    public function edit(Supplier $supplier)
    {
        return view('admin.suppliers.edit', compact('supplier'));
    }

    /**
     * Update the specified supplier
     */
    public function update(Request $request, Supplier $supplier)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'gst_number' => 'nullable|string|max:15|unique:suppliers,gst_number,' . $supplier->id,
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'required|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'pincode' => 'required|string|max:10',
            'contact_person' => 'nullable|string|max:255',
            'credit_limit' => 'nullable|numeric|min:0',
            'credit_days' => 'nullable|integer|min:0',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $supplier->update($validated);

            DB::commit();

            return redirect()
                ->route('admin.suppliers.show', $supplier)
                ->with('success', 'Supplier updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Failed to update supplier: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified supplier
     */
    public function destroy(Supplier $supplier)
    {
        try {
            // Check if supplier has any purchase orders
            if ($supplier->purchaseOrders()->exists()) {
                return back()->with('error', 'Cannot delete supplier with existing purchase orders.');
            }

            $supplier->delete();

            return redirect()
                ->route('admin.suppliers.index')
                ->with('success', 'Supplier deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete supplier: ' . $e->getMessage());
        }
    }

    /**
     * Toggle supplier status
     */
    public function toggleStatus(Supplier $supplier)
    {
        try {
            if ($supplier->is_active) {
                $supplier->deactivate('Deactivated via admin panel');
                $message = 'Supplier deactivated successfully.';
            } else {
                $supplier->activate('Activated via admin panel');
                $message = 'Supplier activated successfully.';
            }

            return back()->with('success', $message);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update supplier status: ' . $e->getMessage());
        }
    }

    /**
     * Update credit limit
     */
    public function updateCreditLimit(Request $request, Supplier $supplier)
    {
        $validated = $request->validate([
            'credit_limit' => 'required|numeric|min:0',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $supplier->updateCreditLimit(
                $validated['credit_limit'],
                $validated['reason'] ?? null
            );

            return back()->with('success', 'Credit limit updated successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update credit limit: ' . $e->getMessage());
        }
    }

    /**
     * Get supplier performance data for API
     */
    public function getPerformanceData(Supplier $supplier)
    {
        $days = request('days', 30);
        $metrics = $supplier->getPerformanceMetrics($days);

        return response()->json([
            'success' => true,
            'data' => $metrics,
        ]);
    }

    /**
     * Export suppliers to CSV
     */
    public function export(Request $request)
    {
        $query = Supplier::query();

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $suppliers = $query->get();

        $filename = 'suppliers_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($suppliers) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'Name', 'Company Name', 'GST Number', 'Phone', 'Email',
                'Address', 'City', 'State', 'Pincode', 'Contact Person',
                'Credit Limit', 'Credit Days', 'Status', 'Created At'
            ]);

            // CSV data
            foreach ($suppliers as $supplier) {
                fputcsv($file, [
                    $supplier->id,
                    $supplier->name,
                    $supplier->company_name,
                    $supplier->gst_number,
                    $supplier->phone,
                    $supplier->email,
                    $supplier->address,
                    $supplier->city,
                    $supplier->state,
                    $supplier->pincode,
                    $supplier->contact_person,
                    $supplier->credit_limit,
                    $supplier->credit_days,
                    $supplier->status_display,
                    $supplier->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get suppliers for API/AJAX
     */
    public function getSuppliers(Request $request)
    {
        $query = Supplier::active();

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%");
            });
        }

        $suppliers = $query->limit(20)->get(['id', 'name', 'company_name', 'phone', 'email']);

        return response()->json([
            'success' => true,
            'data' => $suppliers,
        ]);
    }
}
