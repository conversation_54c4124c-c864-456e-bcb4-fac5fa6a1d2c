<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\UniqueConstraintService;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class TestUniqueConstraintHandling extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:unique-constraints';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the unique constraint handling system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Unique Constraint Handling System');
        $this->newLine();

        // Test 1: Check if value would violate constraint
        $this->info('Test 1: Checking constraint violations');
        $testEmail = '<EMAIL>';
        
        // Create a test user first
        try {
            User::create([
                'name' => 'Test User',
                'email' => $testEmail,
                'password' => bcrypt('password'),
            ]);
            $this->info("✅ Created test user with email: {$testEmail}");
        } catch (\Exception $e) {
            $this->warn("User might already exist: " . $e->getMessage());
        }

        // Check if duplicate would violate
        $wouldViolate = UniqueConstraintService::wouldViolateConstraint('users', 'email', $testEmail);
        $this->info($wouldViolate ? "✅ Correctly detected constraint violation" : "❌ Failed to detect violation");

        // Test 2: Generate suggestions
        $this->info('Test 2: Generating suggestions for duplicate values');
        $suggestions = UniqueConstraintService::getSuggestions('users', 'email', $testEmail);
        $this->info("✅ Generated " . count($suggestions) . " suggestions:");
        foreach ($suggestions as $suggestion) {
            $this->line("   - {$suggestion}");
        }

        // Test 3: Validate unique fields
        $this->info('Test 3: Validating unique fields');
        $violations = UniqueConstraintService::validateUniqueFields(
            ['email' => $testEmail],
            'users',
            ['email']
        );
        
        if (!empty($violations)) {
            $this->info("✅ Correctly identified violations:");
            foreach ($violations as $field => $violation) {
                $this->line("   - {$field}: {$violation['message']}");
                if (!empty($violation['suggestions'])) {
                    $this->line("     Suggestions: " . implode(', ', $violation['suggestions']));
                }
            }
        } else {
            $this->warn("❌ No violations detected when there should be");
        }

        // Test 4: Generate unique value
        $this->info('Test 4: Generating unique values');
        $uniqueValue = UniqueConstraintService::generateUniqueValue('users', 'email', $testEmail);
        $this->info("✅ Generated unique value: {$uniqueValue}");
        
        $stillViolates = UniqueConstraintService::wouldViolateConstraint('users', 'email', $uniqueValue);
        $this->info($stillViolates ? "❌ Generated value still violates constraint" : "✅ Generated value is unique");

        // Test 5: Execute with retry
        $this->info('Test 5: Testing retry mechanism');
        try {
            $result = UniqueConstraintService::executeWithRetry(function () {
                // This should succeed without issues
                return 'success';
            });
            $this->info("✅ Retry mechanism works: {$result}");
        } catch (\Exception $e) {
            $this->error("❌ Retry mechanism failed: " . $e->getMessage());
        }

        $this->newLine();
        $this->info('🎉 Unique constraint handling system test completed!');
        
        // Cleanup
        $this->info('🧹 Cleaning up test data...');
        try {
            User::where('email', $testEmail)->delete();
            $this->info('✅ Test data cleaned up');
        } catch (\Exception $e) {
            $this->warn('Could not clean up test data: ' . $e->getMessage());
        }

        return 0;
    }
}