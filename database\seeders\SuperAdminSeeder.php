<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Location;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure we have a location
        $location = Location::firstOrCreate([
            'code' => 'MAIN'
        ], [
            'name' => 'Main Store',
            'address' => 'Main Office Address',
            'phone' => '+91 98765 43210',
            'is_active' => true,
            'is_main_branch' => true,
        ]);

        // Create or update SuperAdmin user
        $superAdmin = User::updateOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Super Administrator',
            'password' => Hash::make('Admin@123'),
            'phone' => '+91 98765 43210',
            'location_id' => $location->id,
            'is_active' => true,
            'email_verified_at' => now(),
            'employee_id' => 'EMP' . date('Y') . str_pad(1, 4, '0', STR_PAD_LEFT),
        ]);

        // Ensure email is verified for admin access
        if (!$superAdmin->email_verified_at) {
            $superAdmin->update(['email_verified_at' => now()]);
        }

        // Ensure SuperAdmin role exists
        $superAdminRole = Role::firstOrCreate(['name' => 'SuperAdmin']);

        // Assign SuperAdmin role
        if (!$superAdmin->hasRole('SuperAdmin')) {
            $superAdmin->assignRole('SuperAdmin');
        }

        $this->command->info('SuperAdmin user created/updated successfully!');
        $this->command->info('Email: ' . $superAdmin->email);
        $this->command->info('Password: Admin@123');
        $this->command->info('Employee ID: ' . $superAdmin->employee_id);
        $this->command->info('Role: ' . $superAdmin->getRoleNames()->first());
        $this->command->info('Permissions: ' . $superAdmin->getAllPermissions()->count());
    }
}
