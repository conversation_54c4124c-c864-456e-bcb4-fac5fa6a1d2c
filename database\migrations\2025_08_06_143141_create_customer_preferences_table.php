<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');

            // Metal preferences
            $table->json('preferred_metals')->nullable(); // Array of metal IDs
            $table->json('avoided_metals')->nullable(); // Array of metal IDs
            $table->string('preferred_purity')->nullable(); // 22K, 18K, etc.

            // Product preferences
            $table->json('preferred_categories')->nullable(); // Array of category IDs
            $table->json('preferred_product_types')->nullable(); // rings, necklaces, etc.
            $table->json('avoided_categories')->nullable(); // Array of category IDs
            $table->decimal('preferred_weight_min', 8, 3)->nullable(); // Minimum weight preference
            $table->decimal('preferred_weight_max', 8, 3)->nullable(); // Maximum weight preference

            // Style preferences
            $table->json('preferred_styles')->nullable(); // traditional, modern, antique, etc.
            $table->json('preferred_occasions')->nullable(); // wedding, daily wear, party, etc.
            $table->json('preferred_colors')->nullable(); // gold, silver, rose gold, etc.
            $table->json('preferred_stone_types')->nullable(); // Array of stone type IDs

            // Budget preferences
            $table->decimal('budget_min', 12, 2)->nullable();
            $table->decimal('budget_max', 12, 2)->nullable();
            $table->enum('budget_flexibility', ['strict', 'flexible', 'very_flexible'])->default('flexible');

            // Shopping behavior
            $table->json('preferred_shopping_times')->nullable(); // morning, afternoon, evening
            $table->json('preferred_contact_methods')->nullable(); // phone, email, sms, whatsapp
            $table->boolean('prefers_appointments')->default(false);
            $table->boolean('likes_new_arrivals_notifications')->default(true);
            $table->boolean('likes_discount_notifications')->default(true);
            $table->boolean('likes_event_reminders')->default(true);

            // Purchase patterns
            $table->enum('purchase_frequency', ['rare', 'occasional', 'regular', 'frequent'])->nullable();
            $table->json('seasonal_preferences')->nullable(); // festival seasons, months
            $table->boolean('prefers_custom_designs')->default(false);
            $table->boolean('prefers_ready_made')->default(true);

            // Communication preferences
            $table->string('preferred_language', 50)->default('english');
            $table->boolean('marketing_emails_consent')->default(true);
            $table->boolean('promotional_sms_consent')->default(true);
            $table->boolean('whatsapp_notifications_consent')->default(true);

            // Special requirements
            $table->text('allergies')->nullable(); // Metal allergies
            $table->text('special_requirements')->nullable(); // Custom requirements
            $table->text('notes')->nullable(); // Additional preference notes

            // Tracking fields
            $table->timestamp('last_updated_at')->nullable();
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Indexes
            $table->index(['customer_id']);
            $table->index(['budget_min', 'budget_max']);
            $table->index(['purchase_frequency']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_preferences');
    }
};
