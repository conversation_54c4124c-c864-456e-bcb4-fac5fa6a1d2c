<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('alternate_phone')->nullable()->after('phone');
            $table->string('address_line_1')->nullable()->after('gender');
            $table->string('address_line_2')->nullable()->after('address_line_1');
            $table->string('city')->nullable()->after('address_line_2');
            $table->string('state')->nullable()->after('city');
            $table->string('postal_code')->nullable()->after('state');
            $table->string('country')->default('India')->after('postal_code');
            $table->string('pan_number', 10)->nullable()->unique()->after('country');
            $table->string('aadhar_number', 12)->nullable()->unique()->after('pan_number');
            $table->string('gstin', 15)->nullable()->unique()->after('aadhar_number');
            $table->string('company_name')->nullable()->after('gstin');
            $table->string('designation')->nullable()->after('company_name');
            $table->decimal('discount_percentage', 5, 2)->default(0)->after('credit_days');
            $table->enum('kyc_status', ['pending', 'verified', 'rejected'])->default('pending')->after('discount_percentage');
            $table->timestamp('kyc_verified_at')->nullable()->after('kyc_status');
            $table->foreignId('kyc_verified_by')->nullable()->constrained('users')->after('kyc_verified_at');
            $table->text('kyc_notes')->nullable()->after('kyc_verified_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn([
                'alternate_phone',
                'address_line_1',
                'address_line_2',
                'city',
                'state',
                'postal_code',
                'country',
                'pan_number',
                'aadhar_number',
                'gstin',
                'company_name',
                'designation',
                'discount_percentage',
                'kyc_status',
                'kyc_verified_at',
                'kyc_verified_by',
                'kyc_notes',
            ]);
        });
    }
};
