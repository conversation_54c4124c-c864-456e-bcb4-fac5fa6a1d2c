<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add deleted_at to suppliers table
        Schema::table('suppliers', function (Blueprint $table) {
            if (!Schema::hasColumn('suppliers', 'deleted_at')) {
                $table->softDeletes();
            }
        });

        // Add deleted_at to journal_entries table (only if table exists)
        if (Schema::hasTable('journal_entries')) {
            Schema::table('journal_entries', function (Blueprint $table) {
                if (!Schema::hasColumn('journal_entries', 'deleted_at')) {
                    $table->softDeletes();
                }
            });
        }

        // Add deleted_at to repair_services table (only if table exists)
        if (Schema::hasTable('repair_services')) {
            Schema::table('repair_services', function (Blueprint $table) {
                if (!Schema::hasColumn('repair_services', 'deleted_at')) {
                    $table->softDeletes();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('suppliers', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        if (Schema::hasTable('journal_entries')) {
            Schema::table('journal_entries', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }

        if (Schema::hasTable('repair_services')) {
            Schema::table('repair_services', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }
    }
};
