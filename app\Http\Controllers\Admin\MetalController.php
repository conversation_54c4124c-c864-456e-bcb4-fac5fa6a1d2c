<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Metal;
use App\Models\MetalRate;
use App\Services\MetalRateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MetalController extends Controller
{
    protected $metalRateService;

    public function __construct(MetalRateService $metalRateService)
    {
        $this->metalRateService = $metalRateService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Metal::withCount('products');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('symbol', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $metals = $query->orderBy('sort_order')->orderBy('name')->paginate(20);

        // Get current rates for each metal
        $currentRates = $this->metalRateService->getCurrentRates();

        // Get statistics
        $stats = [
            'total_metals' => Metal::count(),
            'active_metals' => Metal::active()->count(),
            'metals_with_products' => Metal::has('products')->count(),
            'metals_with_rates' => Metal::whereHas('rates')->count(),
        ];

        return view('admin.metals.index', compact('metals', 'currentRates', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.metals.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:metals,name',
            'symbol' => 'required|string|max:10|unique:metals,symbol',
            'description' => 'nullable|string|max:1000',
            'purity' => 'nullable|string|max:50',
            'density' => 'nullable|numeric|min:0',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'current_rate' => 'nullable|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Set sort order if not provided
            if (!isset($validated['sort_order'])) {
                $validated['sort_order'] = (Metal::max('sort_order') ?? 0) + 1;
            }

            $metal = Metal::create([
                'name' => $validated['name'],
                'symbol' => strtoupper($validated['symbol']),
                'description' => $validated['description'],
                'purity' => $validated['purity'],
                'density' => $validated['density'],
                'sort_order' => $validated['sort_order'],
                'is_active' => $validated['is_active'] ?? true,
                'created_by' => auth()->id(),
            ]);

            // Add initial rate if provided
            if (isset($validated['current_rate']) && $validated['current_rate'] > 0) {
                $this->metalRateService->updateRate(
                    $metal->id,
                    $validated['current_rate'],
                    now(),
                    'manual'
                );
            }

            DB::commit();

            return redirect()->route('admin.metals.index')
                ->with('success', 'Metal created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error creating metal: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Metal $metal)
    {
        $metal->load(['products.inventory', 'rates' => function ($query) {
            $query->orderBy('effective_date', 'desc')->limit(10);
        }]);

        // Get metal statistics
        $stats = [
            'total_products' => $metal->products()->count(),
            'active_products' => $metal->products()->active()->count(),
            'current_rate' => $this->metalRateService->getCurrentRate($metal->id),
            'rate_change_percentage' => $this->metalRateService->calculateChangePercentage($metal->id),
            'total_inventory_value' => $metal->products()
                ->join('inventories', 'products.id', '=', 'inventories.product_id')
                ->sum(DB::raw('inventories.quantity_available * inventories.cost_price')),
        ];

        // Get rate history for chart
        $rateHistory = $this->metalRateService->getRateHistory($metal->id, 30);

        return view('admin.metals.show', compact('metal', 'stats', 'rateHistory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Metal $metal)
    {
        $currentRate = $this->metalRateService->getCurrentRate($metal->id);

        return view('admin.metals.edit', compact('metal', 'currentRate'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Metal $metal)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:metals,name,' . $metal->id,
            'symbol' => 'required|string|max:10|unique:metals,symbol,' . $metal->id,
            'description' => 'nullable|string|max:1000',
            'purity' => 'nullable|string|max:50',
            'density' => 'nullable|numeric|min:0',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'update_rate' => 'boolean',
            'new_rate' => 'nullable|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            $metal->update([
                'name' => $validated['name'],
                'symbol' => strtoupper($validated['symbol']),
                'description' => $validated['description'],
                'purity' => $validated['purity'],
                'density' => $validated['density'],
                'sort_order' => $validated['sort_order'],
                'is_active' => $validated['is_active'] ?? true,
                'updated_by' => auth()->id(),
            ]);

            // Update rate if requested
            if ($validated['update_rate'] && isset($validated['new_rate']) && $validated['new_rate'] > 0) {
                $this->metalRateService->updateRate(
                    $metal->id,
                    $validated['new_rate'],
                    now(),
                    'manual'
                );
            }

            DB::commit();

            return redirect()->route('admin.metals.index')
                ->with('success', 'Metal updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error updating metal: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Metal $metal)
    {
        // Check if metal has products
        if ($metal->products()->count() > 0) {
            return back()->with('error', 'Cannot delete metal with existing products. Please reassign or delete products first.');
        }

        try {
            DB::beginTransaction();

            // Delete all rates for this metal
            $metal->rates()->delete();

            // Delete metal
            $metal->delete();

            DB::commit();

            return redirect()->route('admin.metals.index')
                ->with('success', 'Metal deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting metal: ' . $e->getMessage());
        }
    }

    /**
     * Update metal rate
     */
    public function updateRate(Request $request, Metal $metal)
    {
        $validated = $request->validate([
            'rate' => 'required|numeric|min:0',
            'effective_date' => 'nullable|date',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $effectiveDate = $validated['effective_date'] ?? now();

            $metalRate = $this->metalRateService->updateRate(
                $metal->id,
                $validated['rate'],
                $effectiveDate,
                'manual'
            );

            if (isset($validated['notes'])) {
                $metalRate->update(['notes' => $validated['notes']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Metal rate updated successfully.',
                'rate' => $metalRate,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating metal rate: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get metal rate history
     */
    public function getRateHistory(Metal $metal, Request $request)
    {
        $days = $request->days ?? 30;
        $rateHistory = $this->metalRateService->getRateHistory($metal->id, $days);

        return response()->json($rateHistory);
    }

    /**
     * Bulk update rates from external source
     */
    public function updateRatesFromExternal()
    {
        try {
            $success = $this->metalRateService->updateAllRatesFromExternal();

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Metal rates updated from external source successfully.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch rates from external source.',
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating rates: ' . $e->getMessage(),
            ]);
        }
    }
}
