<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Location;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class CreateSuperAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jewelsoft:create-superadmin
                            {--name= : Name of the SuperAdmin user}
                            {--email= : Email address of the SuperAdmin user}
                            {--password= : Password for the SuperAdmin user}
                            {--phone= : Phone number of the SuperAdmin user}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a SuperAdmin user with all permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 JewelSoft SuperAdmin User Creation');
        $this->info('=====================================');

        // Get user input
        $name = $this->option('name') ?: $this->ask('Enter SuperAdmin name', 'Super Administrator');
        $email = $this->option('email') ?: $this->ask('Enter email address', '<EMAIL>');
        $password = $this->option('password') ?: $this->secret('Enter password (min 8 characters)');
        $phone = $this->option('phone') ?: $this->ask('Enter phone number', '+91 98765 43210');

        // Validate input
        $validator = Validator::make([
            'name' => $name,
            'email' => $email,
            'password' => $password,
            'phone' => $phone,
        ], [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8',
            'phone' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            $this->error('Validation failed:');
            foreach ($validator->errors()->all() as $error) {
                $this->error('- ' . $error);
            }
            return 1;
        }

        try {
            // Ensure we have a location
            $location = Location::firstOrCreate([
                'code' => 'MAIN'
            ], [
                'name' => 'Main Store',
                'address' => 'Main Office Address',
                'phone' => $phone,
                'is_active' => true,
                'is_main_branch' => true,
            ]);

            // Generate employee ID
            $lastUser = User::orderBy('id', 'desc')->first();
            $nextId = $lastUser ? $lastUser->id + 1 : 1;
            $employeeId = 'EMP' . date('Y') . str_pad($nextId, 4, '0', STR_PAD_LEFT);

            // Create SuperAdmin user
            $superAdmin = User::create([
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'phone' => $phone,
                'location_id' => $location->id,
                'is_active' => true,
                'email_verified_at' => now(),
                'employee_id' => $employeeId,
            ]);

            // Ensure email is verified for admin access
            if (!$superAdmin->email_verified_at) {
                $superAdmin->update(['email_verified_at' => now()]);
            }

            // Ensure SuperAdmin role exists
            $superAdminRole = Role::firstOrCreate(['name' => 'SuperAdmin']);

            // Assign SuperAdmin role
            $superAdmin->assignRole('SuperAdmin');

            $this->info('✅ SuperAdmin user created successfully!');
            $this->info('');
            $this->info('📋 User Details:');
            $this->info('Name: ' . $superAdmin->name);
            $this->info('Email: ' . $superAdmin->email);
            $this->info('Employee ID: ' . $superAdmin->employee_id);
            $this->info('Phone: ' . $superAdmin->phone);
            $this->info('Role: ' . $superAdmin->getRoleNames()->first());
            $this->info('Permissions: ' . $superAdmin->getAllPermissions()->count());
            $this->info('Location: ' . $location->name);
            $this->info('');
            $this->info('🔐 Login Credentials:');
            $this->info('Email: ' . $superAdmin->email);
            $this->info('Password: [Hidden for security]');
            $this->info('');
            $this->info('🌐 You can now login at: /login');

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Failed to create SuperAdmin user:');
            $this->error($e->getMessage());
            return 1;
        }
    }
}
