<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\UniqueConstraintService;

class MonitorUniqueConstraints extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'constraints:monitor 
                            {--table= : Specific table to check}
                            {--fix : Automatically fix found duplicates}
                            {--report : Generate a detailed report}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor and report unique constraint violations across the database';

    /**
     * Tables and their unique columns to monitor
     */
    protected array $monitoredTables = [
        'users' => ['email'],
        'customers' => ['email', 'phone', 'customer_code'],
        'saving_scheme_plans' => ['plan_code'],
        'saving_schemes' => ['scheme_number'],
        'scheme_payments' => ['payment_number', 'receipt_number'],
        'scheme_transactions' => ['transaction_number'],
        'products' => ['sku', 'barcode'],
        'invoices' => ['invoice_number'],
        'estimates' => ['estimate_number'],
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Monitoring unique constraint violations...');
        
        $specificTable = $this->option('table');
        $shouldFix = $this->option('fix');
        $shouldReport = $this->option('report');
        
        $tablesToCheck = $specificTable 
            ? [$specificTable => $this->monitoredTables[$specificTable] ?? []]
            : $this->monitoredTables;
        
        $allViolations = [];
        $totalViolations = 0;
        
        foreach ($tablesToCheck as $table => $columns) {
            if (!$this->tableExists($table)) {
                $this->warn("⚠️  Table '{$table}' does not exist, skipping...");
                continue;
            }
            
            $this->info("Checking table: {$table}");
            
            foreach ($columns as $column) {
                if (!$this->columnExists($table, $column)) {
                    $this->warn("⚠️  Column '{$column}' does not exist in table '{$table}', skipping...");
                    continue;
                }
                
                $violations = $this->checkTableColumn($table, $column, $shouldFix);
                
                if (!empty($violations)) {
                    $allViolations[$table][$column] = $violations;
                    $totalViolations += count($violations);
                    
                    foreach ($violations as $violation) {
                        $this->error("❌ Found {$violation['count']} duplicates for {$table}.{$column} = '{$violation['value']}'");
                    }
                } else {
                    $this->info("✅ No violations found for {$table}.{$column}");
                }
            }
        }
        
        // Summary
        $this->newLine();
        if ($totalViolations > 0) {
            $this->error("🚨 Total violations found: {$totalViolations}");
            
            if ($shouldFix) {
                $this->info("🔧 Violations have been automatically fixed.");
            } else {
                $this->info("💡 Run with --fix to automatically resolve duplicates.");
            }
        } else {
            $this->info("🎉 No unique constraint violations found!");
        }
        
        // Generate report if requested
        if ($shouldReport && !empty($allViolations)) {
            $this->generateReport($allViolations);
        }
        
        return $totalViolations > 0 ? 1 : 0;
    }
    
    /**
     * Check a specific table column for violations
     */
    protected function checkTableColumn(string $table, string $column, bool $shouldFix): array
    {
        $violations = [];
        
        try {
            // Find duplicates
            $duplicates = DB::select("
                SELECT {$column}, COUNT(*) as count, MIN(id) as first_id, MAX(id) as last_id
                FROM {$table}
                WHERE {$column} IS NOT NULL AND {$column} != ''
                GROUP BY {$column}
                HAVING COUNT(*) > 1
                ORDER BY count DESC
            ");
            
            foreach ($duplicates as $duplicate) {
                $violation = [
                    'value' => $duplicate->{$column},
                    'count' => $duplicate->count,
                    'first_id' => $duplicate->first_id,
                    'last_id' => $duplicate->last_id,
                ];
                
                $violations[] = $violation;
                
                // Fix if requested
                if ($shouldFix) {
                    $this->fixDuplicate($table, $column, $duplicate->{$column});
                }
            }
            
        } catch (\Exception $e) {
            $this->error("Error checking {$table}.{$column}: " . $e->getMessage());
            Log::error("Error in MonitorUniqueConstraints", [
                'table' => $table,
                'column' => $column,
                'error' => $e->getMessage()
            ]);
        }
        
        return $violations;
    }
    
    /**
     * Fix duplicate values in a table column
     */
    protected function fixDuplicate(string $table, string $column, $duplicateValue): void
    {
        try {
            // Get all records with the duplicate value
            $records = DB::table($table)
                ->where($column, $duplicateValue)
                ->orderBy('id')
                ->get();
            
            // Keep the first record, update others
            foreach ($records->skip(1) as $record) {
                $newValue = UniqueConstraintService::generateUniqueValue($table, $column, $duplicateValue);
                
                DB::table($table)
                    ->where('id', $record->id)
                    ->update([$column => $newValue]);
                
                $this->info("🔧 Fixed: Updated record ID {$record->id} from '{$duplicateValue}' to '{$newValue}'");
                
                Log::info("Fixed duplicate constraint violation", [
                    'table' => $table,
                    'column' => $column,
                    'record_id' => $record->id,
                    'old_value' => $duplicateValue,
                    'new_value' => $newValue
                ]);
            }
            
        } catch (\Exception $e) {
            $this->error("Failed to fix duplicate in {$table}.{$column}: " . $e->getMessage());
        }
    }
    
    /**
     * Generate a detailed report
     */
    protected function generateReport(array $violations): void
    {
        $reportPath = storage_path('logs/unique_constraints_report_' . date('Y-m-d_H-i-s') . '.json');
        
        $report = [
            'timestamp' => now()->toISOString(),
            'summary' => [
                'total_tables_checked' => count($violations),
                'total_violations' => array_sum(array_map(function($table) {
                    return array_sum(array_map('count', $table));
                }, $violations)),
            ],
            'violations' => $violations,
        ];
        
        file_put_contents($reportPath, json_encode($report, JSON_PRETTY_PRINT));
        
        $this->info("📊 Detailed report saved to: {$reportPath}");
    }
    
    /**
     * Check if a table exists
     */
    protected function tableExists(string $table): bool
    {
        try {
            return DB::getSchemaBuilder()->hasTable($table);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Check if a column exists in a table
     */
    protected function columnExists(string $table, string $column): bool
    {
        try {
            return DB::getSchemaBuilder()->hasColumn($table, $column);
        } catch (\Exception $e) {
            return false;
        }
    }
}