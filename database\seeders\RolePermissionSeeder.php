<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions for jewelry business operations
        $permissions = [
            // Dashboard & Analytics
            'view_dashboard',
            'view_analytics',
            'view_reports',
            'export_reports',

            // Product Management
            'view_products',
            'create_products',
            'edit_products',
            'delete_products',
            'manage_categories',
            'upload_product_images',

            // Inventory Management
            'view_inventory',
            'manage_inventory',
            'stock_adjustments',
            'view_stock_movements',
            'manage_suppliers',
            'create_purchase_orders',

            // Customer Management
            'view_customers',
            'create_customers',
            'edit_customers',
            'delete_customers',
            'manage_customer_documents',
            'view_customer_history',

            // Sales & Billing
            'create_estimates',
            'edit_estimates',
            'convert_estimates',
            'create_invoices',
            'edit_invoices',
            'cancel_invoices',
            'process_payments',
            'handle_returns',
            'manage_old_gold',

            // Metal Rates & Pricing
            'view_metal_rates',
            'edit_metal_rates',
            'manage_making_charges',
            'approve_discounts',

            // Service Management
            'manage_repairs',
            'assign_repair_jobs',
            'update_repair_status',
            'manage_saving_schemes',

            // System Administration
            'manage_users',
            'manage_roles',
            'manage_permissions',
            'system_settings',
            'backup_restore',
            'view_activity_logs',

            // Notifications & Communication
            'send_notifications',
            'manage_templates',
            'whatsapp_integration',

            // Print & Export
            'print_invoices',
            'print_estimates',
            'print_tags',
            'thermal_printing',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles with specific permissions
        $roles = [
            'Super Admin' => Permission::all(),
            'Store Manager' => [
                'view_dashboard', 'view_analytics', 'view_reports', 'export_reports',
                'view_products', 'create_products', 'edit_products', 'manage_categories',
                'view_inventory', 'manage_inventory', 'stock_adjustments', 'view_stock_movements',
                'view_customers', 'create_customers', 'edit_customers', 'view_customer_history',
                'create_estimates', 'edit_estimates', 'convert_estimates', 'create_invoices', 'edit_invoices',
                'process_payments', 'handle_returns', 'manage_old_gold',
                'view_metal_rates', 'edit_metal_rates', 'approve_discounts',
                'manage_repairs', 'assign_repair_jobs', 'update_repair_status',
                'print_invoices', 'print_estimates', 'print_tags',
            ],
            'Sales Staff' => [
                'view_dashboard',
                'view_products', 'view_inventory',
                'view_customers', 'create_customers', 'edit_customers', 'view_customer_history',
                'create_estimates', 'edit_estimates', 'convert_estimates', 'create_invoices',
                'process_payments', 'manage_old_gold',
                'view_metal_rates',
                'print_invoices', 'print_estimates', 'print_tags',
            ],
            'Cashier' => [
                'view_dashboard',
                'view_customers', 'view_customer_history',
                'create_invoices', 'process_payments', 'handle_returns',
                'view_metal_rates',
                'print_invoices', 'print_estimates',
            ],
            'Inventory Manager' => [
                'view_dashboard',
                'view_products', 'create_products', 'edit_products', 'manage_categories', 'upload_product_images',
                'view_inventory', 'manage_inventory', 'stock_adjustments', 'view_stock_movements',
                'manage_suppliers', 'create_purchase_orders',
                'view_metal_rates',
            ],
            'Repair Technician' => [
                'view_dashboard',
                'manage_repairs', 'update_repair_status',
                'view_customers', 'view_customer_history',
            ],
            'Accountant' => [
                'view_dashboard', 'view_analytics', 'view_reports', 'export_reports',
                'view_customers', 'view_customer_history',
                'create_invoices', 'process_payments',
                'view_metal_rates',
                'manage_saving_schemes',
                'view_activity_logs',
            ],
        ];

        foreach ($roles as $roleName => $rolePermissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            $role->syncPermissions($rolePermissions);
        }
    }
}
