<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'repair_service_id',
        'item_name',
        'description',
        'quantity',
        'condition_before',
        'condition_after',
        'repair_notes',
        'before_photo_path',
        'after_photo_path',
        'created_by',
    ];

    protected $casts = [
        'quantity' => 'decimal:3',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($item) {
            $item->created_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function repairService(): BelongsTo
    {
        return $this->belongsTo(RepairService::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Accessors
     */
    public function getHasBeforePhotoAttribute()
    {
        return !empty($this->before_photo_path);
    }

    public function getHasAfterPhotoAttribute()
    {
        return !empty($this->after_photo_path);
    }

    /**
     * Business Logic Methods
     */
    public function uploadBeforePhoto($photoPath)
    {
        $this->update(['before_photo_path' => $photoPath]);
        return $this;
    }

    public function uploadAfterPhoto($photoPath)
    {
        $this->update(['after_photo_path' => $photoPath]);
        return $this;
    }

    public function updateCondition($conditionBefore = null, $conditionAfter = null, $repairNotes = null)
    {
        $updates = [];

        if ($conditionBefore !== null) {
            $updates['condition_before'] = $conditionBefore;
        }

        if ($conditionAfter !== null) {
            $updates['condition_after'] = $conditionAfter;
        }

        if ($repairNotes !== null) {
            $updates['repair_notes'] = $repairNotes;
        }

        if (!empty($updates)) {
            $this->update($updates);
        }

        return $this;
    }

    public function getSummary()
    {
        return [
            'id' => $this->id,
            'item_name' => $this->item_name,
            'description' => $this->description,
            'quantity' => $this->quantity,
            'condition_before' => $this->condition_before,
            'condition_after' => $this->condition_after,
            'repair_notes' => $this->repair_notes,
            'has_before_photo' => $this->has_before_photo,
            'has_after_photo' => $this->has_after_photo,
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
