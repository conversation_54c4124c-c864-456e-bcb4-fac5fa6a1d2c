<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CustomerAnalyticsController extends Controller
{
    /**
     * Customer analytics dashboard
     */
    public function index(Request $request)
    {
        $dateRange = $request->get('date_range', '30'); // Default 30 days
        $startDate = now()->subDays($dateRange);

        // Customer Statistics
        $stats = [
            'total_customers' => Customer::count(),
            'new_customers' => Customer::where('created_at', '>=', $startDate)->count(),
            'active_customers' => Customer::whereHas('invoices', function ($q) use ($startDate) {
                $q->where('created_at', '>=', $startDate);
            })->count(),
            'vip_customers' => Customer::whereRaw('total_purchase_amount >= 100000')->count(),
            'average_order_value' => Invoice::where('status', 'paid')
                                          ->where('created_at', '>=', $startDate)
                                          ->avg('total_amount'),
            'customer_lifetime_value' => Customer::avg('total_purchase_amount'),
        ];

        // Customer Segments
        $segments = $this->getCustomerSegments();

        // Top Customers by Value
        $topCustomers = Customer::orderByDesc('total_purchase_amount')
                               ->limit(10)
                               ->get();

        // Customer Growth Chart Data
        $growthData = $this->getCustomerGrowthData($dateRange);

        // Purchase Behavior Analysis
        $behaviorData = $this->getPurchaseBehaviorData($dateRange);

        // Upcoming Events (Birthdays/Anniversaries)
        $upcomingEvents = $this->getUpcomingEvents();

        return view('admin.customers.analytics', compact(
            'stats', 'segments', 'topCustomers', 'growthData', 
            'behaviorData', 'upcomingEvents', 'dateRange'
        ));
    }

    /**
     * Customer segmentation analysis
     */
    public function segmentation()
    {
        $segments = [
            'VIP' => Customer::whereRaw('total_purchase_amount >= 100000')->get(),
            'Loyal' => Customer::whereRaw('total_purchase_amount >= 50000 AND total_purchase_amount < 100000')->get(),
            'Regular' => Customer::whereRaw('total_purchase_amount >= 20000 AND total_purchase_amount < 50000')->get(),
            'New' => Customer::where('created_at', '>=', now()->subDays(30))->get(),
            'At Risk' => Customer::where('last_purchase_date', '<=', now()->subDays(180))
                                ->whereNotNull('last_purchase_date')
                                ->get(),
            'Occasional' => Customer::whereRaw('total_purchase_amount < 20000')
                                  ->where('created_at', '<=', now()->subDays(30))
                                  ->get(),
        ];

        return view('admin.customers.segmentation', compact('segments'));
    }

    /**
     * Customer loyalty program dashboard
     */
    public function loyalty()
    {
        $loyaltyStats = [
            'total_points_issued' => Customer::sum('loyalty_points'),
            'active_members' => Customer::where('loyalty_points', '>', 0)->count(),
            'points_redeemed' => 0, // This would come from a redemption table
            'average_points_per_customer' => Customer::where('loyalty_points', '>', 0)->avg('loyalty_points'),
        ];

        $topLoyaltyCustomers = Customer::orderByDesc('loyalty_points')
                                     ->where('loyalty_points', '>', 0)
                                     ->limit(20)
                                     ->get();

        $pointsDistribution = Customer::selectRaw('
            CASE 
                WHEN loyalty_points = 0 THEN "No Points"
                WHEN loyalty_points BETWEEN 1 AND 100 THEN "1-100"
                WHEN loyalty_points BETWEEN 101 AND 500 THEN "101-500"
                WHEN loyalty_points BETWEEN 501 AND 1000 THEN "501-1000"
                WHEN loyalty_points BETWEEN 1001 AND 5000 THEN "1001-5000"
                ELSE "5000+"
            END as points_range,
            COUNT(*) as customer_count
        ')
        ->groupBy('points_range')
        ->get();

        return view('admin.customers.loyalty', compact(
            'loyaltyStats', 'topLoyaltyCustomers', 'pointsDistribution'
        ));
    }

    /**
     * Customer purchase history analysis
     */
    public function purchaseHistory(Customer $customer)
    {
        $invoices = $customer->invoices()
                            ->with(['items.product.category'])
                            ->orderByDesc('created_at')
                            ->paginate(20);

        $purchaseStats = [
            'total_orders' => $customer->invoices()->count(),
            'total_spent' => $customer->invoices()->where('status', 'paid')->sum('total_amount'),
            'average_order_value' => $customer->invoices()->where('status', 'paid')->avg('total_amount'),
            'first_purchase' => $customer->invoices()->oldest()->first()?->created_at,
            'last_purchase' => $customer->invoices()->latest()->first()?->created_at,
        ];

        $categoryPreferences = $customer->invoices()
                                      ->join('invoice_items', 'invoices.id', '=', 'invoice_items.invoice_id')
                                      ->join('products', 'invoice_items.product_id', '=', 'products.id')
                                      ->join('categories', 'products.category_id', '=', 'categories.id')
                                      ->selectRaw('categories.name, SUM(invoice_items.total_amount) as total_spent, COUNT(*) as items_bought')
                                      ->groupBy('categories.id', 'categories.name')
                                      ->orderByDesc('total_spent')
                                      ->get();

        $monthlySpending = $customer->invoices()
                                  ->where('status', 'paid')
                                  ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(total_amount) as total')
                                  ->groupBy('year', 'month')
                                  ->orderBy('year', 'desc')
                                  ->orderBy('month', 'desc')
                                  ->limit(12)
                                  ->get();

        return view('admin.customers.purchase-history', compact(
            'customer', 'invoices', 'purchaseStats', 'categoryPreferences', 'monthlySpending'
        ));
    }

    /**
     * Birthday and anniversary reminders
     */
    public function events()
    {
        $upcomingBirthdays = Customer::whereNotNull('date_of_birth')
                                   ->whereRaw('DATE_FORMAT(date_of_birth, "%m-%d") BETWEEN DATE_FORMAT(NOW(), "%m-%d") AND DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 30 DAY), "%m-%d")')
                                   ->orderByRaw('DATE_FORMAT(date_of_birth, "%m-%d")')
                                   ->get();

        $upcomingAnniversaries = Customer::whereNotNull('anniversary_date')
                                        ->whereRaw('DATE_FORMAT(anniversary_date, "%m-%d") BETWEEN DATE_FORMAT(NOW(), "%m-%d") AND DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 30 DAY), "%m-%d")')
                                        ->orderByRaw('DATE_FORMAT(anniversary_date, "%m-%d")')
                                        ->get();

        $todaysBirthdays = Customer::whereNotNull('date_of_birth')
                                 ->whereRaw('DATE_FORMAT(date_of_birth, "%m-%d") = DATE_FORMAT(NOW(), "%m-%d")')
                                 ->get();

        $todaysAnniversaries = Customer::whereNotNull('anniversary_date')
                                     ->whereRaw('DATE_FORMAT(anniversary_date, "%m-%d") = DATE_FORMAT(NOW(), "%m-%d")')
                                     ->get();

        return view('admin.customers.events', compact(
            'upcomingBirthdays', 'upcomingAnniversaries', 'todaysBirthdays', 'todaysAnniversaries'
        ));
    }

    /**
     * Get customer segments data
     */
    private function getCustomerSegments(): array
    {
        return [
            'VIP' => Customer::whereRaw('total_purchase_amount >= 100000')->count(),
            'Loyal' => Customer::whereRaw('total_purchase_amount >= 50000 AND total_purchase_amount < 100000')->count(),
            'Regular' => Customer::whereRaw('total_purchase_amount >= 20000 AND total_purchase_amount < 50000')->count(),
            'New' => Customer::where('created_at', '>=', now()->subDays(30))->count(),
            'At Risk' => Customer::where('last_purchase_date', '<=', now()->subDays(180))
                               ->whereNotNull('last_purchase_date')
                               ->count(),
            'Occasional' => Customer::whereRaw('total_purchase_amount < 20000')
                                  ->where('created_at', '<=', now()->subDays(30))
                                  ->count(),
        ];
    }

    /**
     * Get customer growth data for charts
     */
    private function getCustomerGrowthData(int $days): array
    {
        $data = Customer::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                       ->where('created_at', '>=', now()->subDays($days))
                       ->groupBy('date')
                       ->orderBy('date')
                       ->get();

        return [
            'labels' => $data->pluck('date')->map(function ($date) {
                return Carbon::parse($date)->format('M d');
            })->toArray(),
            'data' => $data->pluck('count')->toArray(),
        ];
    }

    /**
     * Get purchase behavior data
     */
    private function getPurchaseBehaviorData(int $days): array
    {
        $startDate = now()->subDays($days);

        return [
            'repeat_customers' => Customer::whereHas('invoices', function ($q) use ($startDate) {
                $q->where('created_at', '>=', $startDate);
            }, '>=', 2)->count(),
            
            'one_time_customers' => Customer::whereHas('invoices', function ($q) use ($startDate) {
                $q->where('created_at', '>=', $startDate);
            }, '=', 1)->count(),
            
            'average_days_between_purchases' => $this->getAverageDaysBetweenPurchases(),
        ];
    }

    /**
     * Get upcoming events (birthdays/anniversaries)
     */
    private function getUpcomingEvents(): array
    {
        $events = [];

        // Birthdays in next 7 days
        $birthdays = Customer::whereNotNull('date_of_birth')
                           ->whereRaw('DATE_FORMAT(date_of_birth, "%m-%d") BETWEEN DATE_FORMAT(NOW(), "%m-%d") AND DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 7 DAY), "%m-%d")')
                           ->get();

        foreach ($birthdays as $customer) {
            $events[] = [
                'type' => 'birthday',
                'customer' => $customer,
                'date' => $customer->date_of_birth->setYear(now()->year),
                'days_until' => now()->diffInDays($customer->date_of_birth->setYear(now()->year)),
            ];
        }

        // Anniversaries in next 7 days
        $anniversaries = Customer::whereNotNull('anniversary_date')
                               ->whereRaw('DATE_FORMAT(anniversary_date, "%m-%d") BETWEEN DATE_FORMAT(NOW(), "%m-%d") AND DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 7 DAY), "%m-%d")')
                               ->get();

        foreach ($anniversaries as $customer) {
            $events[] = [
                'type' => 'anniversary',
                'customer' => $customer,
                'date' => $customer->anniversary_date->setYear(now()->year),
                'days_until' => now()->diffInDays($customer->anniversary_date->setYear(now()->year)),
            ];
        }

        return collect($events)->sortBy('days_until')->take(10)->toArray();
    }

    /**
     * Calculate average days between purchases
     */
    private function getAverageDaysBetweenPurchases(): float
    {
        $customers = Customer::whereHas('invoices', function ($q) {
            $q->where('status', 'paid');
        }, '>=', 2)->get();

        $totalDays = 0;
        $totalGaps = 0;

        foreach ($customers as $customer) {
            $invoices = $customer->invoices()
                                ->where('status', 'paid')
                                ->orderBy('created_at')
                                ->get();

            for ($i = 1; $i < $invoices->count(); $i++) {
                $totalDays += $invoices[$i]->created_at->diffInDays($invoices[$i-1]->created_at);
                $totalGaps++;
            }
        }

        return $totalGaps > 0 ? $totalDays / $totalGaps : 0;
    }
}
