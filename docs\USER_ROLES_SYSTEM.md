# JewelSoft User Roles System

## Overview

JewelSoft uses a hybrid role system that combines **<PERSON>tie Laravel Permission** package with a direct `role` column in the users table for optimal performance and flexibility.

## Architecture

### Dual Role Storage
- **Spatie Tables**: `roles`, `permissions`, `model_has_roles`, `role_has_permissions` - Full role/permission management
- **Users.role Column**: Direct role storage for fast queries and simple access

### Automatic Synchronization
- Role column is automatically synced with Spatie roles via:
  - UserObserver (on model events)
  - UserController (on role assignments)
  - Manual sync command

## Available Roles

| Role | Display Name | Permissions | Description |
|------|-------------|-------------|-------------|
| `SuperAdmin` | Super Admin | 117 (All) | Complete system access |
| `Manager` | Manager | 20+ | Store management operations |
| `Cashier` | Cashier | 12+ | POS and sales operations |
| `SalesStaff` | Sales Staff | 15+ | Sales and customer management |
| `Accountant` | Accountant | 15+ | Financial and reporting access |
| `Customer` | Customer | 5+ | Limited customer portal access |

## Usage Examples

### Checking User Roles

```php
// Using Spatie methods (more features)
$user->hasRole('SuperAdmin');
$user->getRoleNames(); // Collection of role names
$user->getAllPermissions(); // All permissions

// Using direct column (faster for simple checks)
$user->role; // Direct role string
$user->getPrimaryRole(); // Helper method
$user->getDisplayRole(); // Formatted display name
```

### Assigning Roles

```php
// In controllers (automatically syncs role column)
$user->assignRole('Manager');
$user->syncRoles(['Manager']);

// Direct assignment (not recommended)
$user->update(['role' => 'Manager']); // Won't sync Spatie roles
```

### Querying by Role

```php
// Fast queries using role column
User::where('role', 'SuperAdmin')->get();
User::whereIn('role', ['Manager', 'SuperAdmin'])->get();

// Complex queries using Spatie
User::role('SuperAdmin')->get();
User::permission('manage_users')->get();
```

## Commands

### Create SuperAdmin User
```bash
php artisan jewelsoft:create-superadmin
php artisan jewelsoft:create-superadmin --name="John Admin" --email="<EMAIL>"
```

### Sync Role Columns
```bash
# Check what would be synced
php artisan jewelsoft:sync-user-roles --dry-run

# Perform sync
php artisan jewelsoft:sync-user-roles

# Force sync all users
php artisan jewelsoft:sync-user-roles --force
```

### Seed Roles and Permissions
```bash
php artisan db:seed --class=RolesAndPermissionsSeeder
php artisan db:seed --class=SuperAdminSeeder
```

## Permission Categories

### Core System (SuperAdmin Only)
- `system_administration`
- `view_system_logs`
- `manage_system_health`
- `view_security_dashboard`
- `manage_user_access`

### Management (Manager+)
- `view_analytics`
- `view_business_intelligence`
- `export_analytics`
- `manage_inventory`
- `view_dashboard_analytics`

### Sales Operations (SalesStaff+)
- `process_sales`
- `view_customers`
- `create_customers`
- `manage_customer_interactions`
- `access_pos`

### Financial (Accountant+)
- `view_financial_reports`
- `manage_expenses`
- `view_suppliers`
- `manage_purchase_orders`
- `export_reports`

## Best Practices

### Role Assignment
1. Always use Spatie methods for role assignment
2. Let the system automatically sync the role column
3. Use the sync commands if inconsistencies occur

### Performance Optimization
1. Use `$user->role` for simple role checks
2. Use Spatie methods for complex permission logic
3. Index the role column for fast queries

### Security
1. Never assign SuperAdmin role programmatically without validation
2. Always check permissions before sensitive operations
3. Use middleware for route protection

## Troubleshooting

### Role Column Out of Sync
```bash
php artisan jewelsoft:sync-user-roles --dry-run
php artisan jewelsoft:sync-user-roles
```

### Missing Permissions
```bash
php artisan db:seed --class=RolesAndPermissionsSeeder
```

### User Without Role
```php
$user->assignRole('Customer'); // Assigns default role
```

## Migration Notes

The role column was added via migration `2025_08_20_213642_add_role_column_to_users_table.php` which:
1. Adds indexed `role` column to users table
2. Populates existing users with their Spatie roles
3. Maintains backward compatibility

## Files Modified

- `app/Models/User.php` - Added role to fillable, helper methods
- `app/Http/Controllers/Admin/UserController.php` - Role column sync
- `app/Observers/UserObserver.php` - Automatic synchronization
- `app/Providers/AppServiceProvider.php` - Observer registration
- `database/migrations/*_add_role_column_to_users_table.php` - Database schema
