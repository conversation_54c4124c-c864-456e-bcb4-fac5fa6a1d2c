<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class LoyaltyRedemption extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'points_redeemed',
        'value_redeemed',
        'reason',
        'reference_type',
        'reference_id',
        'status',
        'processed_by',
        'processed_at',
        'notes',
    ];

    protected $casts = [
        'points_redeemed' => 'integer',
        'value_redeemed' => 'decimal:2',
        'processed_at' => 'datetime',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($redemption) {
            $redemption->processed_by = auth()->id();
            $redemption->processed_at = now();
            $redemption->value_redeemed = $redemption->points_redeemed; // 1 point = ₹1
        });
    }

    /**
     * Relationships
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scopes
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('processed_at', today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('processed_at', now()->month)
                    ->whereYear('processed_at', now()->year);
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute(): string
    {
        $statuses = [
            'pending' => 'Pending',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'failed' => 'Failed',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute(): string
    {
        $colors = [
            'pending' => 'yellow',
            'completed' => 'green',
            'cancelled' => 'red',
            'failed' => 'red',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getIsPendingAttribute(): bool
    {
        return $this->status === 'pending';
    }

    public function getCanCancelAttribute(): bool
    {
        return in_array($this->status, ['pending']);
    }

    /**
     * Business Logic Methods
     */
    public function complete($notes = null): self
    {
        $this->update([
            'status' => 'completed',
            'notes' => $notes ? ($this->notes . "\n\nCompleted: " . $notes) : $this->notes,
        ]);

        return $this;
    }

    public function cancel($reason = null): self
    {
        if (!$this->can_cancel) {
            throw new \Exception('This redemption cannot be cancelled');
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? ($this->notes . "\n\nCancelled: " . $reason) : $this->notes,
        ]);

        // Restore points to customer
        $this->customer->loyaltyPoints()->create([
            'points' => $this->points_redeemed,
            'transaction_type' => 'earned',
            'reason' => 'Redemption cancelled - points restored',
            'reference_type' => get_class($this),
            'reference_id' => $this->id,
            'status' => 'active',
            'expires_at' => now()->addYear(),
        ]);

        return $this;
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'points_redeemed' => $this->points_redeemed,
            'value_redeemed' => $this->value_redeemed,
            'reason' => $this->reason,
            'status' => $this->status_display,
            'processed_at' => $this->processed_at->format('Y-m-d H:i:s'),
            'processed_by' => $this->processedBy->name,
            'notes' => $this->notes,
        ];
    }
}
