<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Website\HomeController;
use Illuminate\Http\Request;

class TestHomeController extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:home-controller';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the HomeController directly';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing HomeController directly');
        $this->newLine();

        try {
            $controller = new HomeController();
            $request = new Request();
            
            $this->info('Calling HomeController@index...');
            $response = $controller->index();
            
            $this->info("✅ HomeController@index executed successfully!");
            $this->line("   Response type: " . get_class($response));
            
            if (method_exists($response, 'getData')) {
                $data = $response->getData();
                $this->line("   View data keys: " . implode(', ', array_keys($data)));
            }
            
        } catch (\Exception $e) {
            $this->error("❌ HomeController@index failed: " . $e->getMessage());
            $this->error("   File: " . $e->getFile() . ":" . $e->getLine());
            
            // Show the stack trace for debugging
            $this->newLine();
            $this->error("Stack trace:");
            $trace = $e->getTrace();
            foreach (array_slice($trace, 0, 5) as $i => $frame) {
                $file = $frame['file'] ?? 'unknown';
                $line = $frame['line'] ?? 'unknown';
                $function = $frame['function'] ?? 'unknown';
                $this->error("  #{$i} {$file}:{$line} {$function}()");
            }
        }

        $this->newLine();
        $this->info('🎉 HomeController test completed!');
        
        return 0;
    }
}