<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'employee_id',
        'location_id',
        'role',
        'is_active',
        'last_login_at',
        'last_login_ip',
        'profile_photo_path',
        'two_factor_enabled',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'password_changed_at',
        'failed_login_attempts',
        'locked_until',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
            'password_changed_at' => 'datetime',
            'locked_until' => 'datetime',
            'two_factor_enabled' => 'boolean',
            'two_factor_recovery_codes' => 'array',
            'failed_login_attempts' => 'integer',
        ];
    }

    // Relationships
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    // Helper methods
    /**
     * Get the user's primary role name (from role column for performance)
     */
    public function getPrimaryRole(): ?string
    {
        return $this->role;
    }

    /**
     * Get the user's display role name with proper formatting
     */
    public function getDisplayRole(): string
    {
        $role = $this->getPrimaryRole();

        return match($role) {
            'SuperAdmin' => 'Super Admin',
            'SalesStaff' => 'Sales Staff',
            'Inventory Manager' => 'Inventory Manager',
            'Repair Technician' => 'Repair Technician',
            default => $role ?? 'No Role'
        };
    }

    public function createdInvoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'created_by');
    }

    public function createdEstimates(): HasMany
    {
        return $this->hasMany(Estimate::class, 'created_by');
    }

    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class, 'created_by');
    }

    public function assignedRepairs(): HasMany
    {
        return $this->hasMany(RepairService::class, 'assigned_to');
    }

    // Website user relationships (when User acts as Customer)
    public function customerProfile(): HasOne
    {
        return $this->hasOne(Customer::class, 'email', 'email');
    }

    // Helper method to get customer or create one if needed
    public function getCustomer(): ?Customer
    {
        return $this->customerProfile;
    }

    // Customer-like attributes for website users
    public function getTotalLoyaltyPointsAttribute(): int
    {
        $customer = $this->getCustomer();
        return $customer ? $customer->total_loyalty_points ?? 0 : 0;
    }

    // Business logic methods
    public function canEditRates(): bool
    {
        return $this->hasPermissionTo('edit_metal_rates') || $this->hasRole(['Super Admin', 'Manager']);
    }

    public function canAccessReports(): bool
    {
        return $this->hasPermissionTo('view_reports') || $this->hasRole(['Super Admin', 'Manager', 'Accountant']);
    }

    public function canManageCustomers(): bool
    {
        return $this->hasPermissionTo('manage_customers') || $this->hasRole(['Super Admin', 'Manager', 'Sales Staff']);
    }

    public function canProcessPayments(): bool
    {
        return $this->hasPermissionTo('process_payments') || $this->hasRole(['Super Admin', 'Manager', 'Cashier']);
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    public function getInitialsAttribute(): string
    {
        $names = explode(' ', $this->name);
        $initials = '';
        foreach ($names as $name) {
            $initials .= strtoupper(substr($name, 0, 1));
        }
        return substr($initials, 0, 2);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    // Update last login
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }
}
