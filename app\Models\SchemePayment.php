<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\GeneratesUniqueFields;

class SchemePayment extends Model
{
    use HasFactory, SoftDeletes, GeneratesUniqueFields;

    protected $fillable = [
        'payment_number',
        'scheme_id',
        'saving_scheme_id',
        'receipt_number',
        'payment_date',
        'due_date',
        'amount',
        'late_fee',
        'total_amount',
        'payment_method',
        'payment_method_id',
        'payment_reference',
        'reference_number',
        'status',
        'installment_number',
        'is_advance_payment',
        'notes',
        'received_by',
        'paid_at',
        'created_by',
    ];

    protected $casts = [
        'payment_date' => 'date',
        'due_date' => 'date',
        'paid_at' => 'datetime',
        'amount' => 'decimal:2',
        'late_fee' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'is_advance_payment' => 'boolean',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (!$payment->payment_number) {
                $payment->payment_number = static::generatePaymentNumber();
            }
            if (!$payment->created_by) {
                $payment->created_by = auth()->id() ?: 1; // Default to user ID 1 if no auth
            }
        });
    }

    /**
     * Relationships
     */
    public function scheme(): BelongsTo
    {
        return $this->belongsTo(SavingScheme::class, 'scheme_id');
    }

    public function receivedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'received_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Scopes
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue');
    }

    public function scopeByScheme($query, $schemeId)
    {
        return $query->where('scheme_id', $schemeId);
    }

    /**
     * Accessors
     */
    public function getFormattedAmountAttribute(): string
    {
        return '₹' . number_format($this->amount, 2);
    }

    public function getFormattedLateFeeAttribute(): string
    {
        return '₹' . number_format($this->late_fee, 2);
    }

    public function getFormattedTotalAmountAttribute(): string
    {
        return '₹' . number_format($this->total_amount, 2);
    }

    public function getStatusDisplayAttribute(): string
    {
        $statuses = [
            'pending' => 'Pending',
            'paid' => 'Paid',
            'overdue' => 'Overdue',
            'waived' => 'Waived',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute(): string
    {
        $colors = [
            'pending' => 'warning',
            'paid' => 'success',
            'overdue' => 'danger',
            'waived' => 'info',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    public function getPaymentMethodDisplayAttribute(): string
    {
        $methods = [
            'cash' => 'Cash',
            'card' => 'Card',
            'upi' => 'UPI',
            'bank_transfer' => 'Bank Transfer',
            'cheque' => 'Cheque',
        ];

        return $methods[$this->payment_method] ?? ucfirst($this->payment_method);
    }

    /**
     * Business Logic Methods
     */
    public static function generatePaymentNumber(): string
    {
        $maxAttempts = 10;
        $attempt = 0;

        while ($attempt < $maxAttempts) {
            $prefix = 'SP';

            // Use microseconds for better uniqueness
            $microtime = microtime(true);
            $timestamp = date('ymdHis', $microtime);
            $microseconds = sprintf('%03d', ($microtime - floor($microtime)) * 1000);

            // Add random component
            $random = rand(100000, 999999);

            $number = $prefix . $timestamp . $microseconds . $random;

            // Check if this number already exists
            if (!static::where('payment_number', $number)->exists()) {
                return $number;
            }

            $attempt++;
            // Small delay to avoid rapid collisions
            usleep(1000); // 1ms
        }

        // Fallback: use UUID-based approach if all attempts failed
        $uuid = str_replace('-', '', \Illuminate\Support\Str::uuid());
        $prefix = 'SP';
        $timestamp = now()->format('ymdHis');

        return $prefix . $timestamp . substr($uuid, 0, 8);
    }

    public function markAsPaid($receivedBy = null): self
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'received_by' => $receivedBy ?: auth()->id(),
        ]);

        return $this;
    }

    public function markAsOverdue(): self
    {
        $this->update(['status' => 'overdue']);
        return $this;
    }

    public function waive($reason = null): self
    {
        $this->update([
            'status' => 'waived',
            'notes' => $reason ? ($this->notes . "\n\nWaived: " . $reason) : $this->notes,
        ]);

        return $this;
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'payment_number' => $this->payment_number,
            'scheme_number' => $this->scheme->scheme_number,
            'customer_name' => $this->scheme->customer->full_name,
            'payment_date' => $this->payment_date->format('Y-m-d'),
            'due_date' => $this->due_date->format('Y-m-d'),
            'amount' => $this->formatted_amount,
            'late_fee' => $this->formatted_late_fee,
            'total_amount' => $this->formatted_total_amount,
            'payment_method' => $this->payment_method_display,
            'status' => $this->status_display,
            'status_color' => $this->status_color,
            'installment_number' => $this->installment_number,
            'is_advance_payment' => $this->is_advance_payment,
            'received_by' => $this->receivedBy?->name,
            'paid_at' => $this->paid_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
