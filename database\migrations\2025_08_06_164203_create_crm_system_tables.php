<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Customer Interactions
        if (!Schema::hasTable('customer_interactions')) {
            Schema::create('customer_interactions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('customer_id')->constrained()->onDelete('cascade');
                $table->enum('interaction_type', ['call', 'email', 'sms', 'whatsapp', 'visit', 'inquiry', 'complaint', 'feedback', 'follow_up', 'appointment', 'consultation', 'service', 'other']);
                $table->enum('channel', ['phone', 'email', 'sms', 'whatsapp', 'in_person', 'website', 'social_media', 'referral', 'walk_in']);
                $table->string('subject');
                $table->text('description')->nullable();
                $table->text('outcome')->nullable();
                $table->boolean('follow_up_required')->default(false);
                $table->datetime('follow_up_date')->nullable();
                $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
                $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled', 'follow_up_scheduled'])->default('pending');
                $table->integer('duration_minutes')->nullable();
                $table->json('metadata')->nullable();
                $table->datetime('interaction_date');
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['customer_id']);
                $table->index(['interaction_type']);
                $table->index(['status']);
                $table->index(['priority']);
                $table->index(['interaction_date']);
                $table->index(['follow_up_date']);
            });
        }

        // Loyalty Points
        if (!Schema::hasTable('loyalty_points')) {
            Schema::create('loyalty_points', function (Blueprint $table) {
                $table->id();
                $table->foreignId('customer_id')->constrained()->onDelete('cascade');
                $table->integer('points');
                $table->enum('transaction_type', ['earned', 'redeemed', 'expired', 'bonus', 'adjustment']);
                $table->string('reason');
                $table->string('reference_type')->nullable();
                $table->unsignedBigInteger('reference_id')->nullable();
                $table->enum('status', ['active', 'redeemed', 'expired', 'cancelled'])->default('active');
                $table->datetime('expires_at');
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();

                $table->index(['customer_id']);
                $table->index(['status']);
                $table->index(['transaction_type']);
                $table->index(['expires_at']);
                $table->index(['reference_type', 'reference_id']);
            });
        }

        // Loyalty Redemptions
        if (!Schema::hasTable('loyalty_redemptions')) {
            Schema::create('loyalty_redemptions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('customer_id')->constrained()->onDelete('cascade');
                $table->integer('points_redeemed');
                $table->decimal('value_redeemed', 10, 2);
                $table->string('reason');
                $table->string('reference_type')->nullable();
                $table->unsignedBigInteger('reference_id')->nullable();
                $table->enum('status', ['pending', 'completed', 'cancelled', 'failed'])->default('pending');
                $table->foreignId('processed_by')->constrained('users')->onDelete('cascade');
                $table->datetime('processed_at');
                $table->text('notes')->nullable();
                $table->timestamps();

                $table->index(['customer_id']);
                $table->index(['status']);
                $table->index(['processed_at']);
                $table->index(['reference_type', 'reference_id']);
            });
        }

        // Marketing Campaigns
        if (!Schema::hasTable('marketing_campaigns')) {
            Schema::create('marketing_campaigns', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->text('description')->nullable();
                $table->enum('campaign_type', ['promotional', 'informational', 'birthday', 'anniversary', 'new_arrival', 'discount', 'loyalty', 'feedback', 'reminder', 'follow_up']);
                $table->enum('channel', ['email', 'sms', 'whatsapp', 'phone', 'push', 'in_app']);
                $table->string('target_audience')->nullable();
                $table->text('message_template');
                $table->string('subject_line')->nullable();
                $table->datetime('scheduled_at')->nullable();
                $table->datetime('started_at')->nullable();
                $table->datetime('completed_at')->nullable();
                $table->enum('status', ['draft', 'scheduled', 'active', 'paused', 'completed', 'cancelled'])->default('draft');
                $table->decimal('budget', 10, 2)->nullable();
                $table->decimal('cost_per_contact', 5, 2)->nullable();
                $table->json('target_criteria')->nullable();
                $table->json('success_metrics')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['status']);
                $table->index(['campaign_type']);
                $table->index(['channel']);
                $table->index(['scheduled_at']);
                $table->index(['started_at']);
            });
        }

        // Campaign Customers (Pivot Table)
        if (!Schema::hasTable('campaign_customers')) {
            Schema::create('campaign_customers', function (Blueprint $table) {
                $table->id();
                $table->foreignId('marketing_campaign_id')->constrained()->onDelete('cascade');
                $table->foreignId('customer_id')->constrained()->onDelete('cascade');
                $table->datetime('sent_at')->nullable();
                $table->datetime('opened_at')->nullable();
                $table->datetime('clicked_at')->nullable();
                $table->enum('status', ['pending', 'sent', 'delivered', 'opened', 'clicked', 'failed'])->default('pending');
                $table->text('response')->nullable();
                $table->timestamps();

                $table->unique(['marketing_campaign_id', 'customer_id']);
                $table->index(['marketing_campaign_id']);
                $table->index(['customer_id']);
                $table->index(['status']);
                $table->index(['sent_at']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_customers');
        Schema::dropIfExists('marketing_campaigns');
        Schema::dropIfExists('loyalty_redemptions');
        Schema::dropIfExists('loyalty_points');
        Schema::dropIfExists('customer_interactions');
    }
};
