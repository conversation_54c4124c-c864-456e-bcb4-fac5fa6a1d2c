<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\SavingSchemePlan;
use App\Models\Customer;
use App\Models\CustomerInteraction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SavingSchemeController extends Controller
{
    /**
     * Display saving schemes overview
     */
    public function index()
    {
        $plans = Cache::remember('active_saving_plans', 3600, function () {
            return SavingSchemePlan::active()
                                  ->orderBy('plan_type')
                                  ->orderBy('duration_months')
                                  ->get();
        });

        $benefits = [
            [
                'icon' => 'fas fa-piggy-bank',
                'title' => 'Systematic Savings',
                'description' => 'Build a disciplined saving habit with regular monthly contributions towards your dream jewelry.'
            ],
            [
                'icon' => 'fas fa-percentage',
                'title' => 'Attractive Returns',
                'description' => 'Earn competitive interest rates and bonus amounts on your savings at maturity.'
            ],
            [
                'icon' => 'fas fa-gem',
                'title' => 'Jewelry Purchase',
                'description' => 'Use your matured amount to purchase beautiful jewelry from our exclusive collection.'
            ],
            [
                'icon' => 'fas fa-shield-alt',
                'title' => 'Secure Investment',
                'description' => 'Your savings are safe and secure with transparent terms and conditions.'
            ],
            [
                'icon' => 'fas fa-calendar-check',
                'title' => 'Flexible Plans',
                'description' => 'Choose from monthly, quarterly, or yearly payment plans that suit your budget.'
            ],
            [
                'icon' => 'fas fa-gift',
                'title' => 'Bonus Benefits',
                'description' => 'Get additional bonus amounts on maturity and special discounts on jewelry purchases.'
            ]
        ];

        return view('website.saving-schemes.index', compact('plans', 'benefits'));
    }

    /**
     * Display available plans
     */
    public function plans()
    {
        $plans = SavingSchemePlan::active()
                                ->orderBy('plan_type')
                                ->orderBy('duration_months')
                                ->get();

        return view('website.saving-schemes.plans', compact('plans'));
    }

    /**
     * Display savings calculator
     */
    public function calculator()
    {
        $plans = SavingSchemePlan::active()
                                ->orderBy('plan_type')
                                ->orderBy('duration_months')
                                ->get();

        return view('website.saving-schemes.calculator', compact('plans'));
    }

    /**
     * Calculate savings projection
     */
    public function calculate(Request $request)
    {
        $validated = $request->validate([
            'plan_id' => 'required|exists:saving_scheme_plans,id',
            'monthly_amount' => 'required|numeric|min:1',
        ]);

        $plan = SavingSchemePlan::findOrFail($validated['plan_id']);

        // Validate amount against plan limits
        if ($validated['monthly_amount'] < $plan->minimum_amount) {
            return response()->json([
                'success' => false,
                'error' => "Minimum amount for this plan is {$plan->formatted_minimum_amount}",
            ], 400);
        }

        if ($plan->maximum_amount && $validated['monthly_amount'] > $plan->maximum_amount) {
            return response()->json([
                'success' => false,
                'error' => "Maximum amount for this plan is {$plan->formatted_maximum_amount}",
            ], 400);
        }

        // Calculate maturity details
        $totalInstallments = $plan->getTotalInstallments();
        $totalPrincipal = $validated['monthly_amount'] * $totalInstallments;
        $maturityCalculation = $plan->calculateMaturityAmount($totalPrincipal);

        // Generate payment schedule
        $paymentSchedule = [];
        $currentDate = now();
        $frequency = $plan->getInstallmentFrequency();

        for ($i = 1; $i <= $totalInstallments; $i++) {
            $dueDate = $currentDate->copy()->addMonths($frequency * ($i - 1));
            $paymentSchedule[] = [
                'installment' => $i,
                'due_date' => $dueDate->format('M Y'),
                'amount' => $validated['monthly_amount'],
            ];
        }

        return response()->json([
            'success' => true,
            'calculation' => [
                'plan_name' => $plan->plan_name,
                'plan_type' => $plan->plan_type_display,
                'duration' => $plan->duration_display,
                'monthly_amount' => '₹' . number_format($validated['monthly_amount'], 2),
                'total_installments' => $totalInstallments,
                'total_principal' => '₹' . number_format($maturityCalculation['principal_amount'], 2),
                'interest_earned' => '₹' . number_format($maturityCalculation['interest_amount'], 2),
                'bonus_amount' => '₹' . number_format($maturityCalculation['bonus_amount'], 2),
                'maturity_amount' => '₹' . number_format($maturityCalculation['total_amount'], 2),
                'interest_rate' => $plan->interest_rate . '% per annum',
                'bonus_percentage' => $plan->bonus_percentage . '%',
                'payment_schedule' => array_slice($paymentSchedule, 0, 6), // Show first 6 payments
            ]
        ]);
    }

    /**
     * Show application form
     */
    public function apply()
    {
        $plans = SavingSchemePlan::active()
                                ->orderBy('plan_type')
                                ->orderBy('duration_months')
                                ->get();

        return view('website.saving-schemes.apply', compact('plans'));
    }

    /**
     * Submit application
     */
    public function submitApplication(Request $request)
    {
        $validated = $request->validate([
            'plan_id' => 'required|exists:saving_scheme_plans,id',
            'customer_name' => 'required|string|max:100',
            'email' => 'required|email|max:100',
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:500',
            'monthly_amount' => 'required|numeric|min:1',
            'preferred_start_date' => 'required|date|after:today',
            'scheme_name' => 'nullable|string|max:100',
            'terms_accepted' => 'required|accepted',
        ]);

        try {
            $plan = SavingSchemePlan::findOrFail($validated['plan_id']);

            // Validate amount against plan limits
            if ($validated['monthly_amount'] < $plan->minimum_amount) {
                return back()->withInput()
                            ->with('error', "Minimum amount for this plan is {$plan->formatted_minimum_amount}");
            }

            if ($plan->maximum_amount && $validated['monthly_amount'] > $plan->maximum_amount) {
                return back()->withInput()
                            ->with('error', "Maximum amount for this plan is {$plan->formatted_maximum_amount}");
            }

            // Find or create customer
            $customer = Customer::where('email', $validated['email'])->first();

            if (!$customer) {
                $customer = Customer::create([
                    'first_name' => $validated['customer_name'],
                    'email' => $validated['email'],
                    'phone' => $validated['phone'],
                    'billing_address' => $validated['address'],
                    'customer_type' => 'individual',
                    'source' => 'website_saving_scheme',
                ]);
            }

            // Create customer interaction for the application
            $applicationDetails = [
                'plan_name' => $plan->plan_name,
                'monthly_amount' => $validated['monthly_amount'],
                'preferred_start_date' => $validated['preferred_start_date'],
                'scheme_name' => $validated['scheme_name'],
                'source' => 'website_application',
            ];

            $customer->addInteraction(
                'inquiry',
                'Saving Scheme Application',
                "Customer applied for {$plan->plan_name} with monthly amount of ₹{$validated['monthly_amount']}",
                $applicationDetails
            );

            return redirect()->route('website.saving-schemes.index')
                           ->with('success', 'Your application has been submitted successfully! Our team will contact you soon.');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Failed to submit application. Please try again.');
        }
    }
}
