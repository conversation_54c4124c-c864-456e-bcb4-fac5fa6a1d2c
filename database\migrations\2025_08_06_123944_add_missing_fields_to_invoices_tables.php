<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            if (!Schema::hasColumn('invoices', 'shipping_charges')) {
                $table->decimal('shipping_charges', 10, 2)->default(0)->after('discount_amount');
            }
            if (!Schema::hasColumn('invoices', 'total_gst')) {
                $table->decimal('total_gst', 15, 2)->default(0)->after('igst_amount');
            }
            if (!Schema::hasColumn('invoices', 'payment_status')) {
                $table->enum('payment_status', ['pending', 'partial', 'paid', 'overdue'])->default('pending')->after('status');
            }
            if (!Schema::hasColumn('invoices', 'payment_method')) {
                $table->string('payment_method')->nullable()->after('payment_status');
            }
            if (!Schema::hasColumn('invoices', 'payment_reference')) {
                $table->string('payment_reference')->nullable()->after('payment_method');
            }
            if (!Schema::hasColumn('invoices', 'payment_notes')) {
                $table->text('payment_notes')->nullable()->after('payment_reference');
            }
            if (!Schema::hasColumn('invoices', 'paid_at')) {
                $table->timestamp('paid_at')->nullable()->after('payment_notes');
            }
            if (!Schema::hasColumn('invoices', 'cancelled_at')) {
                $table->timestamp('cancelled_at')->nullable()->after('paid_at');
            }
            if (!Schema::hasColumn('invoices', 'cancelled_by')) {
                $table->foreignId('cancelled_by')->nullable()->constrained('users')->after('cancelled_at');
            }
            if (!Schema::hasColumn('invoices', 'updated_by')) {
                $table->foreignId('updated_by')->nullable()->constrained('users')->after('created_by');
            }
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            if (!Schema::hasColumn('invoice_items', 'making_charges')) {
                $table->decimal('making_charges', 10, 2)->default(0)->after('discount_amount');
            }
            if (!Schema::hasColumn('invoice_items', 'stone_charges')) {
                $table->decimal('stone_charges', 10, 2)->default(0)->after('making_charges');
            }
            if (!Schema::hasColumn('invoice_items', 'gst_rate')) {
                $table->decimal('gst_rate', 5, 2)->default(0)->after('igst_amount');
            }
            if (!Schema::hasColumn('invoice_items', 'notes')) {
                $table->text('notes')->nullable()->after('total_amount');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn([
                'shipping_charges',
                'total_gst',
                'payment_status',
                'payment_method',
                'payment_reference',
                'payment_notes',
                'paid_at',
                'cancelled_at',
                'cancelled_by',
                'updated_by',
            ]);
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            $table->dropColumn([
                'making_charges',
                'stone_charges',
                'gst_rate',
                'notes',
            ]);
        });
    }
};
