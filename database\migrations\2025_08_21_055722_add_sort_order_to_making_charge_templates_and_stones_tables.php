<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add sort_order to making_charge_templates table
        if (Schema::hasTable('making_charge_templates') && !Schema::hasColumn('making_charge_templates', 'sort_order')) {
            Schema::table('making_charge_templates', function (Blueprint $table) {
                $table->integer('sort_order')->default(0)->after('is_active');
            });

            // Set default sort order values for existing templates
            DB::statement("UPDATE making_charge_templates SET sort_order = id WHERE sort_order = 0");
        }

        // Add sort_order to stones table
        if (Schema::hasTable('stones') && !Schema::hasColumn('stones', 'sort_order')) {
            Schema::table('stones', function (Blueprint $table) {
                $table->integer('sort_order')->default(0)->after('is_active');
            });

            // Set default sort order values for existing stones
            DB::statement("UPDATE stones SET sort_order = id WHERE sort_order = 0");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('making_charge_templates', 'sort_order')) {
            Schema::table('making_charge_templates', function (Blueprint $table) {
                $table->dropColumn('sort_order');
            });
        }

        if (Schema::hasColumn('stones', 'sort_order')) {
            Schema::table('stones', function (Blueprint $table) {
                $table->dropColumn('sort_order');
            });
        }
    }
};
