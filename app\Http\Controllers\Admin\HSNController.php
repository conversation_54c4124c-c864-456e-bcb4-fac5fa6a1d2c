<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HSNCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HSNController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = HSNCode::withCount(['products', 'categories']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by GST rate
        if ($request->filled('gst_rate')) {
            $rate = $request->gst_rate;
            $query->where(function($q) use ($rate) {
                $q->where('cgst_rate', $rate)
                  ->orWhere('sgst_rate', $rate)
                  ->orWhere('igst_rate', $rate);
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $hsnCodes = $query->orderBy('code')->paginate(20);

        // Get unique GST rates for filter (combine all GST rate columns)
        $cgstRates = HSNCode::distinct()->whereNotNull('cgst_rate')->pluck('cgst_rate');
        $sgstRates = HSNCode::distinct()->whereNotNull('sgst_rate')->pluck('sgst_rate');
        $igstRates = HSNCode::distinct()->whereNotNull('igst_rate')->pluck('igst_rate');
        $gstRates = $cgstRates->merge($sgstRates)->merge($igstRates)->unique()->filter()->sort();

        // Get statistics
        $stats = [
            'total_hsn_codes' => HSNCode::count(),
            'active_hsn_codes' => HSNCode::active()->count(),
            'hsn_with_products' => HSNCode::has('products')->count(),
            'hsn_with_categories' => HSNCode::has('categories')->count(),
            'unique_gst_rates' => $gstRates->count(),
        ];

        return view('admin.hsn.index', compact('hsnCodes', 'gstRates', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.hsn.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|string|max:20|unique:hsn_codes,code',
            'description' => 'required|string|max:500',
            'gst_rate' => 'required|numeric|min:0|max:100',
            'unit' => 'nullable|string|max:50',
            'is_active' => 'boolean',
        ]);

        try {
            HSNCode::create([
                'code' => $validated['code'],
                'description' => $validated['description'],
                'gst_rate' => $validated['gst_rate'],
                'unit' => $validated['unit'],
                'is_active' => $validated['is_active'] ?? true,
                'created_by' => auth()->id(),
            ]);

            return redirect()->route('admin.hsn.index')
                ->with('success', 'HSN code created successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Error creating HSN code: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(HSNCode $hsn)
    {
        $hsn->load(['products.inventory', 'categories']);

        // Get HSN statistics
        $stats = [
            'total_products' => $hsn->products()->count(),
            'active_products' => $hsn->products()->active()->count(),
            'total_categories' => $hsn->categories()->count(),
            'total_sales' => $hsn->products()
                ->join('invoice_items', 'products.id', '=', 'invoice_items.product_id')
                ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
                ->where('invoices.status', '!=', 'cancelled')
                ->sum('invoice_items.total_amount'),
            'total_gst_collected' => $hsn->products()
                ->join('invoice_items', 'products.id', '=', 'invoice_items.product_id')
                ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
                ->where('invoices.status', '!=', 'cancelled')
                ->sum(DB::raw('invoice_items.total_amount * ' . ($hsn->gst_rate / 100))),
        ];

        return view('admin.hsn.show', compact('hsn', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(HSNCode $hsn)
    {
        return view('admin.hsn.edit', compact('hsn'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, HSNCode $hsn)
    {
        $validated = $request->validate([
            'code' => 'required|string|max:20|unique:hsn_codes,code,' . $hsn->id,
            'description' => 'required|string|max:500',
            'gst_rate' => 'required|numeric|min:0|max:100',
            'unit' => 'nullable|string|max:50',
            'is_active' => 'boolean',
        ]);

        try {
            $validated['updated_by'] = auth()->id();

            $hsn->update($validated);

            return redirect()->route('admin.hsn.index')
                ->with('success', 'HSN code updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Error updating HSN code: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HSNCode $hsn)
    {
        // Check if HSN has products or categories
        if ($hsn->products()->count() > 0 || $hsn->categories()->count() > 0) {
            return back()->with('error', 'Cannot delete HSN code with existing products or categories. Please reassign them first.');
        }

        try {
            $hsn->delete();

            return redirect()->route('admin.hsn.index')
                ->with('success', 'HSN code deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Error deleting HSN code: ' . $e->getMessage());
        }
    }

    /**
     * Toggle HSN status
     */
    public function toggleStatus(HSNCode $hsn)
    {
        try {
            $hsn->update([
                'is_active' => !$hsn->is_active,
                'updated_by' => auth()->id(),
            ]);

            $status = $hsn->is_active ? 'activated' : 'deactivated';

            return response()->json([
                'success' => true,
                'message' => "HSN code {$status} successfully.",
                'is_active' => $hsn->is_active,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating HSN status: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get HSN codes for select dropdown
     */
    public function getForSelect(Request $request)
    {
        $query = HSNCode::active();

        if ($request->filled('gst_rate')) {
            $query->where('gst_rate', $request->gst_rate);
        }

        $hsnCodes = $query->orderBy('code')->get();

        return response()->json($hsnCodes);
    }

    /**
     * Import HSN codes from CSV
     */
    public function import(Request $request)
    {
        $validated = $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:2048',
        ]);

        try {
            $file = $request->file('csv_file');
            $csvData = array_map('str_getcsv', file($file->path()));

            // Remove header row
            $header = array_shift($csvData);

            DB::beginTransaction();

            $imported = 0;
            $errors = [];

            foreach ($csvData as $row) {
                if (count($row) < 3) continue; // Skip incomplete rows

                try {
                    HSNCode::updateOrCreate(
                        ['code' => $row[0]],
                        [
                            'description' => $row[1] ?? '',
                            'gst_rate' => $row[2] ?? 0,
                            'unit' => $row[3] ?? null,
                            'is_active' => true,
                            'created_by' => auth()->id(),
                        ]
                    );
                    $imported++;
                } catch (\Exception $e) {
                    $errors[] = "Row with code {$row[0]}: " . $e->getMessage();
                }
            }

            DB::commit();

            $message = "Successfully imported {$imported} HSN codes.";
            if (!empty($errors)) {
                $message .= " Errors: " . implode(', ', array_slice($errors, 0, 3));
            }

            return redirect()->route('admin.hsn.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error importing HSN codes: ' . $e->getMessage());
        }
    }

    /**
     * Export HSN codes to CSV
     */
    public function export()
    {
        $hsnCodes = HSNCode::orderBy('code')->get();

        $filename = 'hsn_codes_' . date('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($hsnCodes) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, ['HSN Code', 'Description', 'GST Rate (%)', 'Unit', 'Status', 'Products Count', 'Categories Count']);

            // CSV data
            foreach ($hsnCodes as $hsn) {
                fputcsv($file, [
                    $hsn->code,
                    $hsn->description,
                    $hsn->gst_rate,
                    $hsn->unit,
                    $hsn->is_active ? 'Active' : 'Inactive',
                    $hsn->products_count ?? 0,
                    $hsn->categories_count ?? 0,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
