<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CashMovement extends Model
{
    use HasFactory;

    protected $fillable = [
        'pos_session_id',
        'movement_type',
        'amount',
        'reason',
        'reference',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($movement) {
            $movement->created_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function posSession(): BelongsTo
    {
        return $this->belongsTo(POSSession::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scopes
     */
    public function scopeCashIn($query)
    {
        return $query->where('movement_type', 'cash_in');
    }

    public function scopeCashOut($query)
    {
        return $query->where('movement_type', 'cash_out');
    }

    public function scopeBySession($query, $sessionId)
    {
        return $query->where('pos_session_id', $sessionId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Accessors
     */
    public function getMovementTypeDisplayAttribute()
    {
        $types = [
            'cash_in' => 'Cash In',
            'cash_out' => 'Cash Out',
            'float_adjustment' => 'Float Adjustment',
            'till_count' => 'Till Count',
        ];

        return $types[$this->movement_type] ?? ucfirst(str_replace('_', ' ', $this->movement_type));
    }

    public function getMovementTypeColorAttribute()
    {
        $colors = [
            'cash_in' => 'green',
            'cash_out' => 'red',
            'float_adjustment' => 'blue',
            'till_count' => 'gray',
        ];

        return $colors[$this->movement_type] ?? 'gray';
    }

    public function getIsCashInAttribute()
    {
        return $this->movement_type === 'cash_in';
    }

    public function getIsCashOutAttribute()
    {
        return $this->movement_type === 'cash_out';
    }

    /**
     * Business Logic Methods
     */
    public static function recordCashIn($sessionId, $amount, $reason, $reference = null, $notes = null)
    {
        return static::create([
            'pos_session_id' => $sessionId,
            'movement_type' => 'cash_in',
            'amount' => $amount,
            'reason' => $reason,
            'reference' => $reference,
            'notes' => $notes,
        ]);
    }

    public static function recordCashOut($sessionId, $amount, $reason, $reference = null, $notes = null)
    {
        return static::create([
            'pos_session_id' => $sessionId,
            'movement_type' => 'cash_out',
            'amount' => $amount,
            'reason' => $reason,
            'reference' => $reference,
            'notes' => $notes,
        ]);
    }

    public static function recordFloatAdjustment($sessionId, $amount, $reason, $notes = null)
    {
        return static::create([
            'pos_session_id' => $sessionId,
            'movement_type' => 'float_adjustment',
            'amount' => $amount,
            'reason' => $reason,
            'notes' => $notes,
        ]);
    }

    public function getSummary()
    {
        return [
            'id' => $this->id,
            'movement_type' => $this->movement_type_display,
            'amount' => $this->amount,
            'reason' => $this->reason,
            'reference' => $this->reference,
            'notes' => $this->notes,
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
