<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\GeneratesUniqueFields;

class SchemeMaturity extends Model
{
    use HasFactory, GeneratesUniqueFields;

    protected $fillable = [
        'scheme_id',
        'maturity_date',
        'maturity_amount',
        'principal_amount',
        'interest_amount',
        'bonus_amount',
        'action_taken',
        'amount_withdrawn',
        'amount_used_for_purchase',
        'invoice_id',
        'action_date',
        'notes',
        'processed_by',
    ];

    protected $casts = [
        'maturity_date' => 'date',
        'action_date' => 'date',
        'maturity_amount' => 'decimal:2',
        'principal_amount' => 'decimal:2',
        'interest_amount' => 'decimal:2',
        'bonus_amount' => 'decimal:2',
        'amount_withdrawn' => 'decimal:2',
        'amount_used_for_purchase' => 'decimal:2',
    ];

    /**
     * Relationships
     */
    public function scheme(): BelongsTo
    {
        return $this->belongsTo(SavingScheme::class, 'scheme_id');
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'processed_by');
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('action_taken', 'pending');
    }

    public function scopeProcessed($query)
    {
        return $query->where('action_taken', '!=', 'pending');
    }

    /**
     * Accessors
     */
    public function getFormattedMaturityAmountAttribute(): string
    {
        return '₹' . number_format($this->maturity_amount, 2);
    }

    public function getFormattedPrincipalAmountAttribute(): string
    {
        return '₹' . number_format($this->principal_amount, 2);
    }

    public function getFormattedInterestAmountAttribute(): string
    {
        return '₹' . number_format($this->interest_amount, 2);
    }

    public function getFormattedBonusAmountAttribute(): string
    {
        return '₹' . number_format($this->bonus_amount, 2);
    }

    public function getActionTakenDisplayAttribute(): string
    {
        $actions = [
            'pending' => 'Pending Action',
            'cash_withdrawal' => 'Cash Withdrawal',
            'jewelry_purchase' => 'Jewelry Purchase',
            'scheme_renewal' => 'Scheme Renewal',
            'transfer' => 'Transfer to Another Scheme',
        ];

        return $actions[$this->action_taken] ?? ucfirst(str_replace('_', ' ', $this->action_taken));
    }

    public function getActionColorAttribute(): string
    {
        $colors = [
            'pending' => 'warning',
            'cash_withdrawal' => 'success',
            'jewelry_purchase' => 'primary',
            'scheme_renewal' => 'info',
            'transfer' => 'secondary',
        ];

        return $colors[$this->action_taken] ?? 'secondary';
    }

    public function getRemainingAmountAttribute(): float
    {
        return $this->maturity_amount - $this->amount_withdrawn - $this->amount_used_for_purchase;
    }

    public function getFormattedRemainingAmountAttribute(): string
    {
        return '₹' . number_format($this->remaining_amount, 2);
    }

    /**
     * Business Logic Methods
     */
    public function processCashWithdrawal($amount, $notes = null): self
    {
        if ($amount > $this->remaining_amount) {
            throw new \Exception('Withdrawal amount exceeds available balance');
        }

        $this->update([
            'action_taken' => 'cash_withdrawal',
            'amount_withdrawn' => $this->amount_withdrawn + $amount,
            'action_date' => now()->toDateString(),
            'notes' => $notes,
            'processed_by' => auth()->id(),
        ]);

        return $this;
    }

    public function processJewelryPurchase($invoiceId, $amount, $notes = null): self
    {
        if ($amount > $this->remaining_amount) {
            throw new \Exception('Purchase amount exceeds available balance');
        }

        $this->update([
            'action_taken' => 'jewelry_purchase',
            'amount_used_for_purchase' => $this->amount_used_for_purchase + $amount,
            'invoice_id' => $invoiceId,
            'action_date' => now()->toDateString(),
            'notes' => $notes,
            'processed_by' => auth()->id(),
        ]);

        return $this;
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'scheme_number' => $this->scheme->scheme_number,
            'customer_name' => $this->scheme->customer->full_name,
            'maturity_date' => $this->maturity_date->format('Y-m-d'),
            'maturity_amount' => $this->formatted_maturity_amount,
            'principal_amount' => $this->formatted_principal_amount,
            'interest_amount' => $this->formatted_interest_amount,
            'bonus_amount' => $this->formatted_bonus_amount,
            'action_taken' => $this->action_taken_display,
            'action_color' => $this->action_color,
            'remaining_amount' => $this->formatted_remaining_amount,
            'action_date' => $this->action_date?->format('Y-m-d'),
            'processed_by' => $this->processedBy?->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
