<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Metal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ProductController extends Controller
{
    /**
     * Display product catalog
     */
    public function index(Request $request)
    {
        $query = Product::with(['category', 'images', 'inventory'])
                       ->where('is_active', true)
                       ->whereHas('inventory', function ($q) {
                           $q->where('quantity_available', '>', 0);
                       });

        // Category filter
        if ($request->filled('category')) {
            $category = Category::where('slug', $request->category)->first();
            if ($category) {
                $categoryIds = [$category->id];
                // Include subcategories
                $subcategories = Category::where('parent_id', $category->id)->pluck('id');
                $categoryIds = array_merge($categoryIds, $subcategories->toArray());

                $query->whereIn('category_id', $categoryIds);
            }
        }

        // Price filter - using inventory table since selling_price is calculated
        if ($request->filled('min_price')) {
            $query->whereHas('inventory', function ($q) use ($request) {
                $q->where('selling_price', '>=', $request->min_price);
            });
        }
        if ($request->filled('max_price')) {
            $query->whereHas('inventory', function ($q) use ($request) {
                $q->where('selling_price', '<=', $request->max_price);
            });
        }

        // Metal filter
        if ($request->filled('metal')) {
            $query->where('metal_type', $request->metal);
        }

        // Purity filter
        if ($request->filled('purity')) {
            $query->where('metal_purity', $request->purity);
        }

        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort', 'name');
        switch ($sortBy) {
            case 'price_low':
                $query->join('inventory', 'products.id', '=', 'inventory.product_id')
                      ->orderBy('inventory.selling_price', 'asc')
                      ->select('products.*');
                break;
            case 'price_high':
                $query->join('inventory', 'products.id', '=', 'inventory.product_id')
                      ->orderBy('inventory.selling_price', 'desc')
                      ->select('products.*');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'popular':
                $query->orderBy('created_at', 'desc'); // Fallback to newest until view_count is available
                break;
            default:
                $query->orderBy('name', 'asc');
        }

        $products = $query->paginate(12);

        // Get filter options
        $categories = Category::where('is_active', true)
                             ->whereNull('parent_id')
                             ->orderBy('name')
                             ->get();

        $metals = Metal::where('is_active', true)
                       ->orderBy('name')
                       ->pluck('name', 'id');

        $purities = Metal::where('is_active', true)
                        ->whereNotNull('purity')
                        ->distinct()
                        ->pluck('purity')
                        ->filter()
                        ->sort();

        // Calculate price range from inventory table since selling_price is calculated dynamically
        $priceRange = [
            'min' => \DB::table('inventory')->min('selling_price') ?? 0,
            'max' => \DB::table('inventory')->max('selling_price') ?? 100000,
        ];

        return view('website.products.index', compact(
            'products',
            'categories',
            'metals',
            'purities',
            'priceRange'
        ));
    }

    /**
     * Display single product
     */
    public function show(Product $product)
    {
        if (!$product->is_active) {
            abort(404);
        }

        // View count functionality disabled until column is added
        // $product->increment('view_count');

        // Load relationships
        $product->load(['category', 'images', 'metal', 'inventory']);

        // Get related products
        $relatedProducts = Product::with(['category', 'images', 'inventory'])
                                 ->where('is_active', true)
                                 ->whereHas('inventory', function ($q) {
                                     $q->where('quantity_available', '>', 0);
                                 })
                                 ->where('category_id', $product->category_id)
                                 ->where('id', '!=', $product->id)
                                 ->limit(4)
                                 ->get();

        // Get recently viewed products from session
        $recentlyViewed = $this->getRecentlyViewedProducts($product->id);

        return view('website.products.show', compact(
            'product',
            'relatedProducts',
            'recentlyViewed'
        ));
    }

    /**
     * Display products by category
     */
    public function category(Category $category)
    {
        if (!$category->is_active) {
            abort(404);
        }

        // Get category and subcategory IDs
        $categoryIds = [$category->id];
        $subcategories = Category::where('parent_id', $category->id)->pluck('id');
        $categoryIds = array_merge($categoryIds, $subcategories->toArray());

        $products = Product::with(['category', 'images', 'inventory'])
                          ->where('is_active', true)
                          ->whereHas('inventory', function ($q) {
                              $q->where('quantity_available', '>', 0);
                          })
                          ->whereIn('category_id', $categoryIds)
                          ->orderBy('name')
                          ->paginate(12);

        return view('website.products.category', compact('category', 'products'));
    }

    /**
     * Quick view product (AJAX)
     */
    public function quickView($productId)
    {
        try {
            $product = Product::with(['category', 'images', 'inventory'])
                              ->where('id', $productId)
                              ->where('is_active', true)
                              ->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'error' => 'Product not found'
                ], 404);
            }

            if ($product->stock_quantity <= 0) {
                return response()->json([
                    'success' => false,
                    'error' => 'Product not available'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'description' => $product->description,
                    'selling_price' => $product->selling_price,
                    'formatted_price' => $product->formatted_selling_price,
                    'category' => $product->category->name,
                    'metal_type' => $product->metal_type,
                    'metal_purity' => $product->metal_purity,
                    'weight' => $product->weight,
                    'stock_quantity' => $product->stock_quantity,
                    'images' => $product->images->map(function ($image) {
                        return [
                            'url' => $image->image_url,
                            'alt' => $image->alt_text ?: $product->name,
                        ];
                    }),
                    'specifications' => $product->specifications,
                    'is_customizable' => $product->is_customizable,
                    'url' => route('website.products.show', $product),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to load product details',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recently viewed products
     */
    protected function getRecentlyViewedProducts($currentProductId)
    {
        $recentlyViewed = session()->get('recently_viewed', []);

        // Add current product to recently viewed
        $recentlyViewed = array_filter($recentlyViewed, function ($id) use ($currentProductId) {
            return $id != $currentProductId;
        });

        array_unshift($recentlyViewed, $currentProductId);
        $recentlyViewed = array_slice($recentlyViewed, 0, 10);

        session()->put('recently_viewed', $recentlyViewed);

        // Get products excluding current one
        $productIds = array_slice($recentlyViewed, 1, 4);

        if (empty($productIds)) {
            return collect();
        }

        return Product::with(['category', 'images', 'inventory'])
                     ->where('is_active', true)
                     ->whereHas('inventory', function ($q) {
                         $q->where('quantity_available', '>', 0);
                     })
                     ->whereIn('id', $productIds)
                     ->get();
    }

    /**
     * Compare products (AJAX)
     */
    public function compare(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array|min:1|max:3',
            'product_ids.*' => 'exists:products,id'
        ]);

        $products = Product::with(['category', 'images', 'metal', 'inventory'])
                          ->whereIn('id', $request->product_ids)
                          ->where('is_active', true)
                          ->get();

        $productsData = $products->map(function ($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'category' => $product->category->name,
                'formatted_selling_price' => $product->formatted_selling_price,
                'metal_type' => $product->metal ? $product->metal->name : 'N/A',
                'weight' => $product->gross_weight ? number_format($product->gross_weight, 3) . 'g' : 'N/A',
                'stock_quantity' => $product->inventory ? $product->inventory->quantity : 0,
                'image' => $product->images->first()?->image_url
            ];
        });

        return response()->json([
            'success' => true,
            'products' => $productsData
        ]);
    }

    /**
     * Get search suggestions (AJAX)
     */
    public function searchSuggestions(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([
                'success' => true,
                'suggestions' => []
            ]);
        }

        // Get product suggestions
        $products = Product::with(['category', 'images'])
                          ->where('is_active', true)
                          ->where(function ($q) use ($query) {
                              $q->where('name', 'LIKE', "%{$query}%")
                                ->orWhere('sku', 'LIKE', "%{$query}%")
                                ->orWhere('description', 'LIKE', "%{$query}%");
                          })
                          ->limit(8)
                          ->get();

        // Get category suggestions
        $categories = Category::where('is_active', true)
                            ->where('name', 'LIKE', "%{$query}%")
                            ->limit(5)
                            ->get();

        $suggestions = [
            'products' => $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'category' => $product->category->name,
                    'price' => $product->formatted_selling_price,
                    'image' => $product->images->first()?->image_url,
                    'url' => route('website.products.show', $product),
                    'type' => 'product'
                ];
            }),
            'categories' => $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'url' => route('website.products.category', $category),
                    'type' => 'category'
                ];
            })
        ];

        return response()->json([
            'success' => true,
            'suggestions' => $suggestions
        ]);
    }
}
