<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('print_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('code', 50)->unique(); // Template identifier
            $table->enum('template_type', [
                'invoice', 'receipt', 'estimate', 'tag', 'label',
                'barcode', 'qr_code', 'report', 'custom'
            ]);
            $table->text('description')->nullable();

            // Template content
            $table->longText('template_content'); // Template with placeholders
            $table->json('template_variables')->nullable(); // Available variables
            $table->json('required_fields')->nullable(); // Required data fields

            // Layout settings
            $table->integer('paper_width_mm')->default(80);
            $table->integer('paper_height_mm')->nullable(); // For fixed-size templates
            $table->integer('margin_left')->default(0);
            $table->integer('margin_right')->default(0);
            $table->integer('margin_top')->default(0);
            $table->integer('margin_bottom')->default(0);

            // Font and styling
            $table->enum('default_font_size', ['small', 'medium', 'large'])->default('medium');
            $table->boolean('supports_bold')->default(true);
            $table->boolean('supports_underline')->default(true);
            $table->boolean('supports_alignment')->default(true);

            // Content sections
            $table->json('header_settings')->nullable(); // Header configuration
            $table->json('body_settings')->nullable(); // Body configuration
            $table->json('footer_settings')->nullable(); // Footer configuration
            $table->boolean('include_logo')->default(false);
            $table->boolean('include_barcode')->default(false);
            $table->boolean('include_qr_code')->default(false);

            // Print behavior
            $table->boolean('auto_cut_after')->default(true);
            $table->integer('feed_lines_after')->default(3);
            $table->boolean('open_drawer_after')->default(false);
            $table->integer('copies_default')->default(1);

            // Usage and compatibility
            $table->json('compatible_printers')->nullable(); // Array of printer type/model
            $table->json('supported_paper_sizes')->nullable(); // Supported paper sizes
            $table->boolean('is_system_template')->default(false); // System vs custom template
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);

            // Version control
            $table->string('version', 20)->default('1.0');
            $table->text('changelog')->nullable();
            $table->foreignId('parent_template_id')->nullable()->constrained('print_templates')->onDelete('set null');

            // Usage statistics
            $table->integer('usage_count')->default(0);
            $table->timestamp('last_used_at')->nullable();
            $table->decimal('average_print_time_seconds', 8, 2)->nullable();

            // Audit fields
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Indexes
            $table->index(['template_type', 'is_active']);
            $table->index(['is_system_template', 'is_active']);
            $table->index(['is_default', 'template_type']);
            $table->index(['code']);
            $table->index(['usage_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('print_templates');
    }
};
