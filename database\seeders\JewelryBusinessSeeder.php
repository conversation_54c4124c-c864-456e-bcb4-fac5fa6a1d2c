<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class JewelryBusinessSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Company Settings
        DB::table('company_settings')->insert([
            'name' => 'JewelSoft Demo Store',
            'gst_number' => '22AAAAA0000A1Z5',
            'address' => '123 Jewelry Street, Gold Market, Mumbai - 400001',
            'phone' => '+91-9876543210',
            'email' => '<EMAIL>',
            'website' => 'https://jewelsoft.com',
            'default_wastage_percentage' => 8.00,
            'default_making_charge_per_gram' => 500.00,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Main Location
        DB::table('locations')->insert([
            'name' => 'Main Store',
            'code' => 'MAIN',
            'address' => '123 Jewelry Street, Gold Market, Mumbai - 400001',
            'phone' => '+91-9876543210',
            'manager_name' => 'Store Manager',
            'is_active' => true,
            'is_main_branch' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Metal Types
        $metals = [
            ['name' => 'Gold', 'purity' => '22K', 'symbol' => 'AU', 'current_rate_per_gram' => 6500.00],
            ['name' => 'Gold', 'purity' => '18K', 'symbol' => 'AU', 'current_rate_per_gram' => 5300.00],
            ['name' => 'Gold', 'purity' => '14K', 'symbol' => 'AU', 'current_rate_per_gram' => 4100.00],
            ['name' => 'Silver', 'purity' => '925', 'symbol' => 'AG', 'current_rate_per_gram' => 85.00],
            ['name' => 'Silver', 'purity' => '999', 'symbol' => 'AG', 'current_rate_per_gram' => 90.00],
            ['name' => 'Platinum', 'purity' => '950', 'symbol' => 'PT', 'current_rate_per_gram' => 3200.00],
        ];

        foreach ($metals as $metal) {
            DB::table('metals')->insert(array_merge($metal, [
                'rate_updated_at' => now(),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Stone Types
        $stones = [
            ['name' => 'Diamond', 'type' => 'Precious', 'unit' => 'Carat', 'base_rate_per_unit' => 50000.00],
            ['name' => 'Ruby', 'type' => 'Precious', 'unit' => 'Carat', 'base_rate_per_unit' => 25000.00],
            ['name' => 'Emerald', 'type' => 'Precious', 'unit' => 'Carat', 'base_rate_per_unit' => 30000.00],
            ['name' => 'Sapphire', 'type' => 'Precious', 'unit' => 'Carat', 'base_rate_per_unit' => 20000.00],
            ['name' => 'Pearl', 'type' => 'Semi-precious', 'unit' => 'Piece', 'base_rate_per_unit' => 5000.00],
            ['name' => 'Coral', 'type' => 'Semi-precious', 'unit' => 'Gram', 'base_rate_per_unit' => 1000.00],
        ];

        foreach ($stones as $stone) {
            DB::table('stones')->insert(array_merge($stone, [
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // HSN Codes
        $hsnCodes = [
            ['code' => '71131900', 'description' => 'Gold Jewelry', 'cgst_rate' => 1.50, 'sgst_rate' => 1.50, 'igst_rate' => 3.00],
            ['code' => '71141900', 'description' => 'Silver Jewelry', 'cgst_rate' => 1.50, 'sgst_rate' => 1.50, 'igst_rate' => 3.00],
            ['code' => '71161000', 'description' => 'Diamond Jewelry', 'cgst_rate' => 0.125, 'sgst_rate' => 0.125, 'igst_rate' => 0.25],
            ['code' => '71171900', 'description' => 'Imitation Jewelry', 'cgst_rate' => 6.00, 'sgst_rate' => 6.00, 'igst_rate' => 12.00],
        ];

        foreach ($hsnCodes as $hsn) {
            DB::table('hsn_codes')->insert(array_merge($hsn, [
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Product Categories
        $categories = [
            ['name' => 'Rings', 'slug' => 'rings', 'description' => 'Wedding rings, engagement rings, fashion rings', 'hsn_code_id' => 1],
            ['name' => 'Necklaces', 'slug' => 'necklaces', 'description' => 'Gold chains, pendant sets, chokers', 'hsn_code_id' => 1],
            ['name' => 'Earrings', 'slug' => 'earrings', 'description' => 'Studs, hoops, chandbali, jhumkas', 'hsn_code_id' => 1],
            ['name' => 'Bracelets', 'slug' => 'bracelets', 'description' => 'Gold bracelets, bangles, kada', 'hsn_code_id' => 1],
            ['name' => 'Pendants', 'slug' => 'pendants', 'description' => 'Religious pendants, fashion pendants', 'hsn_code_id' => 1],
            ['name' => 'Mangalsutra', 'slug' => 'mangalsutra', 'description' => 'Traditional mangalsutra designs', 'hsn_code_id' => 1],
            ['name' => 'Silver Items', 'slug' => 'silver-items', 'description' => 'Silver jewelry and artifacts', 'hsn_code_id' => 2],
        ];

        foreach ($categories as $index => $category) {
            DB::table('categories')->insert(array_merge($category, [
                'parent_id' => null,
                'sort_order' => $index + 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Payment Methods
        $paymentMethods = [
            ['name' => 'Cash', 'code' => 'CASH', 'description' => 'Cash payment'],
            ['name' => 'Card', 'code' => 'CARD', 'description' => 'Credit/Debit card payment'],
            ['name' => 'UPI', 'code' => 'UPI', 'description' => 'UPI payment'],
            ['name' => 'Bank Transfer', 'code' => 'BANK', 'description' => 'Bank transfer/NEFT/RTGS'],
            ['name' => 'Cheque', 'code' => 'CHEQUE', 'description' => 'Cheque payment'],
            ['name' => 'Old Gold', 'code' => 'OLD_GOLD', 'description' => 'Old gold exchange'],
        ];

        foreach ($paymentMethods as $method) {
            DB::table('payment_methods')->insert(array_merge($method, [
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Making Charge Templates
        $makingChargeTemplates = [
            ['name' => 'Standard Gold Ring', 'category_id' => 1, 'metal_id' => 1, 'charge_type' => 'per_gram', 'charge_value' => 800.00],
            ['name' => 'Heavy Gold Necklace', 'category_id' => 2, 'metal_id' => 1, 'charge_type' => 'per_gram', 'charge_value' => 600.00],
            ['name' => 'Light Gold Earrings', 'category_id' => 3, 'metal_id' => 1, 'charge_type' => 'per_gram', 'charge_value' => 1000.00],
            ['name' => 'Silver Items', 'category_id' => 7, 'metal_id' => 4, 'charge_type' => 'per_gram', 'charge_value' => 50.00],
        ];

        foreach ($makingChargeTemplates as $template) {
            DB::table('making_charge_templates')->insert(array_merge($template, [
                'description' => 'Standard making charges for ' . $template['name'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // System Settings
        $systemSettings = [
            ['key' => 'auto_backup_enabled', 'value' => 'true', 'type' => 'boolean', 'description' => 'Enable automatic database backups', 'group' => 'backup'],
            ['key' => 'backup_retention_days', 'value' => '30', 'type' => 'number', 'description' => 'Number of days to retain backups', 'group' => 'backup'],
            ['key' => 'sms_notifications_enabled', 'value' => 'true', 'type' => 'boolean', 'description' => 'Enable SMS notifications', 'group' => 'notifications'],
            ['key' => 'email_notifications_enabled', 'value' => 'true', 'type' => 'boolean', 'description' => 'Enable email notifications', 'group' => 'notifications'],
            ['key' => 'whatsapp_notifications_enabled', 'value' => 'false', 'type' => 'boolean', 'description' => 'Enable WhatsApp notifications', 'group' => 'notifications'],
            ['key' => 'invoice_prefix', 'value' => 'INV', 'type' => 'string', 'description' => 'Invoice number prefix', 'group' => 'billing'],
            ['key' => 'estimate_prefix', 'value' => 'EST', 'type' => 'string', 'description' => 'Estimate number prefix', 'group' => 'billing'],
            ['key' => 'default_invoice_due_days', 'value' => '30', 'type' => 'number', 'description' => 'Default invoice due days', 'group' => 'billing'],
        ];

        foreach ($systemSettings as $setting) {
            DB::table('system_settings')->insert(array_merge($setting, [
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
