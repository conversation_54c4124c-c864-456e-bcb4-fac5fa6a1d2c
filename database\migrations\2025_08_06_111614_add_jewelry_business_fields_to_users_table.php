<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->string('employee_id')->unique()->nullable()->after('phone');
            $table->foreignId('location_id')->nullable()->constrained()->onDelete('set null')->after('employee_id');
            $table->boolean('is_active')->default(true)->after('location_id');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
            $table->string('profile_photo_path')->nullable()->after('last_login_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'employee_id',
                'location_id',
                'is_active',
                'last_login_at',
                'profile_photo_path'
            ]);
        });
    }
};
