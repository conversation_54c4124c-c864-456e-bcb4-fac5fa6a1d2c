<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\CustomerPreference;
use App\Models\CustomerAnalytics;
use App\Models\CustomerInteraction;
use App\Models\CustomerSegment;
use App\Models\CustomerSegmentAssignment;
use App\Models\Product;
use App\Models\Invoice;
use App\Models\Estimate;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CustomerService
{
    /**
     * Create customer with preferences and analytics
     */
    public function createCustomer($customerData, $preferencesData = [])
    {
        DB::beginTransaction();
        
        try {
            // Create customer
            $customer = Customer::create($customerData);
            
            // Create preferences if provided
            if (!empty($preferencesData)) {
                $preferencesData['customer_id'] = $customer->id;
                CustomerPreference::create($preferencesData);
            }
            
            // Create analytics record
            CustomerAnalytics::create([
                'customer_id' => $customer->id,
                'customer_lifecycle_stage' => 'new',
                'value_segment' => 'low',
                'risk_level' => 'low',
                'last_calculated_at' => now(),
                'next_calculation_due' => now()->addDays(7),
            ]);
            
            // Auto-assign to segments
            $this->assignCustomerToSegments($customer);
            
            DB::commit();
            
            return $customer;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update customer analytics
     */
    public function updateCustomerAnalytics($customerId)
    {
        $customer = Customer::find($customerId);
        if (!$customer) {
            return false;
        }
        
        $analytics = $customer->analytics ?: new CustomerAnalytics(['customer_id' => $customerId]);
        $analytics->calculateAnalytics();
        
        // Re-assign to segments based on updated analytics
        $this->assignCustomerToSegments($customer);
        
        return $analytics;
    }

    /**
     * Get customer insights and recommendations
     */
    public function getCustomerInsights($customerId)
    {
        $customer = Customer::with(['preferences', 'analytics', 'interactions', 'segments'])
                           ->find($customerId);
        
        if (!$customer) {
            return null;
        }
        
        $analytics = $customer->analytics;
        $preferences = $customer->preferences;
        
        $insights = [
            'customer' => $customer,
            'analytics_summary' => $analytics ? $analytics->getSummary() : null,
            'lifecycle_stage' => $analytics ? $analytics->lifecycle_stage_display : 'New',
            'value_segment' => $analytics ? $analytics->value_segment_display : 'Low Value',
            'risk_level' => $analytics ? $analytics->risk_level_display : 'Low Risk',
            'engagement_level' => $analytics ? $analytics->engagement_level : 'low',
            'next_purchase_probability' => $analytics ? $analytics->next_purchase_probability : 0,
            'recommended_products' => $this->getRecommendedProducts($customer),
            'communication_preferences' => $this->getCommunicationPreferences($customer),
            'recent_interactions' => $this->getRecentInteractions($customer),
            'purchase_patterns' => $this->getPurchasePatterns($customer),
            'alerts' => $this->getCustomerAlerts($customer),
            'action_items' => $this->getActionItems($customer),
        ];
        
        return $insights;
    }

    /**
     * Get recommended products for customer
     */
    public function getRecommendedProducts($customer, $limit = 10)
    {
        $preferences = $customer->preferences;
        
        if (!$preferences) {
            // Return popular products if no preferences
            return Product::active()
                         ->orderBy('created_at', 'desc')
                         ->limit($limit)
                         ->get();
        }
        
        $query = Product::active()->with(['category', 'metal']);
        
        // Filter by preferred metals
        if ($preferences->preferred_metals) {
            $query->whereIn('metal_id', $preferences->preferred_metals);
        }
        
        // Filter by preferred categories
        if ($preferences->preferred_categories) {
            $query->whereIn('category_id', $preferences->preferred_categories);
        }
        
        // Filter by budget
        if ($preferences->budget_min || $preferences->budget_max) {
            if ($preferences->budget_min) {
                $query->where('selling_price', '>=', $preferences->budget_min);
            }
            if ($preferences->budget_max) {
                $query->where('selling_price', '<=', $preferences->budget_max);
            }
        }
        
        // Filter by weight preferences
        if ($preferences->preferred_weight_min || $preferences->preferred_weight_max) {
            if ($preferences->preferred_weight_min) {
                $query->where('weight', '>=', $preferences->preferred_weight_min);
            }
            if ($preferences->preferred_weight_max) {
                $query->where('weight', '<=', $preferences->preferred_weight_max);
            }
        }
        
        // Exclude avoided metals
        if ($preferences->avoided_metals) {
            $query->whereNotIn('metal_id', $preferences->avoided_metals);
        }
        
        // Exclude avoided categories
        if ($preferences->avoided_categories) {
            $query->whereNotIn('category_id', $preferences->avoided_categories);
        }
        
        $products = $query->limit($limit * 2)->get();
        
        // Score products based on preferences
        $scoredProducts = $products->map(function ($product) use ($preferences) {
            $score = $preferences->getProductPreferenceScore($product);
            $product->preference_score = $score;
            return $product;
        });
        
        // Sort by preference score and return top products
        return $scoredProducts->sortByDesc('preference_score')->take($limit);
    }

    /**
     * Get communication preferences
     */
    public function getCommunicationPreferences($customer)
    {
        $preferences = $customer->preferences;
        
        if (!$preferences) {
            return [
                'preferred_methods' => ['phone', 'email'],
                'marketing_consent' => true,
                'best_time_to_contact' => 'any',
                'language' => 'english',
            ];
        }
        
        return [
            'preferred_methods' => $preferences->preferred_contact_methods ?: ['phone', 'email'],
            'marketing_consent' => $preferences->hasMarketingConsent(),
            'email_consent' => $preferences->marketing_emails_consent,
            'sms_consent' => $preferences->promotional_sms_consent,
            'whatsapp_consent' => $preferences->whatsapp_notifications_consent,
            'best_time_to_contact' => $preferences->preferred_shopping_times ?: ['any'],
            'language' => $preferences->preferred_language ?: 'english',
            'prefers_appointments' => $preferences->prefers_appointments,
            'likes_notifications' => [
                'new_arrivals' => $preferences->likes_new_arrivals_notifications,
                'discounts' => $preferences->likes_discount_notifications,
                'events' => $preferences->likes_event_reminders,
            ],
        ];
    }

    /**
     * Get recent interactions
     */
    public function getRecentInteractions($customer, $limit = 5)
    {
        return $customer->interactions()
                       ->with(['user'])
                       ->orderBy('created_at', 'desc')
                       ->limit($limit)
                       ->get();
    }

    /**
     * Get purchase patterns
     */
    public function getPurchasePatterns($customer)
    {
        $invoices = $customer->invoices()->paid()->get();
        
        if ($invoices->isEmpty()) {
            return [
                'total_purchases' => 0,
                'average_order_value' => 0,
                'purchase_frequency' => 'No purchases yet',
                'seasonal_pattern' => [],
                'preferred_categories' => [],
                'preferred_metals' => [],
                'spending_trend' => 'stable',
            ];
        }
        
        // Calculate patterns
        $totalValue = $invoices->sum('total_amount');
        $averageValue = $totalValue / $invoices->count();
        
        // Seasonal pattern
        $seasonalPattern = $invoices->groupBy(function ($invoice) {
            return Carbon::parse($invoice->invoice_date)->format('M');
        })->map->count()->toArray();
        
        // Category preferences from purchases
        $categoryPurchases = [];
        foreach ($invoices as $invoice) {
            foreach ($invoice->items as $item) {
                $categoryId = $item->product->category_id;
                $categoryPurchases[$categoryId] = ($categoryPurchases[$categoryId] ?? 0) + 1;
            }
        }
        arsort($categoryPurchases);
        
        // Metal preferences from purchases
        $metalPurchases = [];
        foreach ($invoices as $invoice) {
            foreach ($invoice->items as $item) {
                $metalId = $item->product->metal_id;
                $metalPurchases[$metalId] = ($metalPurchases[$metalId] ?? 0) + 1;
            }
        }
        arsort($metalPurchases);
        
        // Purchase frequency
        $firstPurchase = $invoices->min('invoice_date');
        $lastPurchase = $invoices->max('invoice_date');
        $daysBetween = Carbon::parse($firstPurchase)->diffInDays($lastPurchase);
        $frequency = $invoices->count() > 1 ? $daysBetween / ($invoices->count() - 1) : 0;
        
        // Spending trend
        $recentInvoices = $invoices->where('invoice_date', '>=', now()->subMonths(6));
        $olderInvoices = $invoices->where('invoice_date', '<', now()->subMonths(6));
        
        $recentAvg = $recentInvoices->isNotEmpty() ? $recentInvoices->avg('total_amount') : 0;
        $olderAvg = $olderInvoices->isNotEmpty() ? $olderInvoices->avg('total_amount') : 0;
        
        $spendingTrend = 'stable';
        if ($recentAvg > $olderAvg * 1.2) {
            $spendingTrend = 'increasing';
        } elseif ($recentAvg < $olderAvg * 0.8) {
            $spendingTrend = 'decreasing';
        }
        
        return [
            'total_purchases' => $invoices->count(),
            'total_value' => $totalValue,
            'average_order_value' => $averageValue,
            'purchase_frequency_days' => round($frequency),
            'seasonal_pattern' => $seasonalPattern,
            'preferred_categories' => array_slice($categoryPurchases, 0, 3, true),
            'preferred_metals' => array_slice($metalPurchases, 0, 3, true),
            'spending_trend' => $spendingTrend,
            'last_purchase_date' => $lastPurchase,
            'days_since_last_purchase' => Carbon::parse($lastPurchase)->diffInDays(now()),
        ];
    }

    /**
     * Get customer alerts
     */
    public function getCustomerAlerts($customer)
    {
        $alerts = [];
        $analytics = $customer->analytics;
        
        if (!$analytics) {
            return $alerts;
        }
        
        // High risk customer
        if ($analytics->risk_level === 'high') {
            $alerts[] = [
                'type' => 'risk',
                'level' => 'high',
                'message' => 'High risk customer - review payment history and credit terms',
                'action' => 'Review credit terms',
            ];
        }
        
        // At risk of churning
        if (in_array($analytics->customer_lifecycle_stage, ['at_risk', 'dormant'])) {
            $alerts[] = [
                'type' => 'churn_risk',
                'level' => 'medium',
                'message' => 'Customer at risk of churning - consider re-engagement campaign',
                'action' => 'Schedule follow-up',
            ];
        }
        
        // High value customer
        if ($analytics->value_segment === 'vip') {
            $alerts[] = [
                'type' => 'vip',
                'level' => 'info',
                'message' => 'VIP customer - ensure premium service',
                'action' => 'Provide VIP treatment',
            ];
        }
        
        // Overdue follow-ups
        $overdueFollowUps = $customer->interactions()
                                   ->overdueFollowUp()
                                   ->count();
        
        if ($overdueFollowUps > 0) {
            $alerts[] = [
                'type' => 'follow_up',
                'level' => 'medium',
                'message' => "{$overdueFollowUps} overdue follow-up(s)",
                'action' => 'Complete follow-ups',
            ];
        }
        
        // Recent complaints
        $recentComplaints = $customer->interactions()
                                   ->byType('complaint')
                                   ->where('created_at', '>=', now()->subDays(30))
                                   ->count();
        
        if ($recentComplaints > 0) {
            $alerts[] = [
                'type' => 'complaint',
                'level' => 'high',
                'message' => "{$recentComplaints} recent complaint(s)",
                'action' => 'Address concerns',
            ];
        }
        
        return $alerts;
    }

    /**
     * Get action items for customer
     */
    public function getActionItems($customer)
    {
        $actionItems = [];
        $analytics = $customer->analytics;
        
        // Follow-up required
        $pendingFollowUps = $customer->interactions()
                                   ->requiresFollowUp()
                                   ->count();
        
        if ($pendingFollowUps > 0) {
            $actionItems[] = [
                'type' => 'follow_up',
                'priority' => 'high',
                'title' => 'Complete pending follow-ups',
                'description' => "{$pendingFollowUps} interaction(s) require follow-up",
                'due_date' => now()->addDays(1),
            ];
        }
        
        // Update preferences
        if (!$customer->preferences) {
            $actionItems[] = [
                'type' => 'preferences',
                'priority' => 'medium',
                'title' => 'Collect customer preferences',
                'description' => 'Customer preferences not recorded',
                'due_date' => now()->addDays(7),
            ];
        }
        
        // Re-engagement for dormant customers
        if ($analytics && $analytics->customer_lifecycle_stage === 'dormant') {
            $actionItems[] = [
                'type' => 'engagement',
                'priority' => 'high',
                'title' => 'Re-engage dormant customer',
                'description' => 'Customer has not purchased in over 6 months',
                'due_date' => now()->addDays(3),
            ];
        }
        
        // Birthday/anniversary follow-up
        if ($customer->date_of_birth && 
            Carbon::parse($customer->date_of_birth)->isBirthday()) {
            $actionItems[] = [
                'type' => 'birthday',
                'priority' => 'medium',
                'title' => 'Birthday wishes',
                'description' => 'Send birthday greetings and special offers',
                'due_date' => now(),
            ];
        }
        
        return $actionItems;
    }

    /**
     * Assign customer to segments
     */
    public function assignCustomerToSegments($customer)
    {
        $analytics = $customer->analytics;
        
        if (!$analytics) {
            return;
        }
        
        // Get all automatic segments
        $segments = CustomerSegment::where('is_automatic', true)
                                  ->where('is_active', true)
                                  ->get();
        
        foreach ($segments as $segment) {
            $shouldAssign = $this->evaluateSegmentCriteria($customer, $analytics, $segment);
            
            $existingAssignment = CustomerSegmentAssignment::where('customer_id', $customer->id)
                                                          ->where('customer_segment_id', $segment->id)
                                                          ->first();
            
            if ($shouldAssign && !$existingAssignment) {
                // Assign to segment
                CustomerSegmentAssignment::create([
                    'customer_id' => $customer->id,
                    'customer_segment_id' => $segment->id,
                    'assignment_type' => 'automatic',
                    'assigned_date' => now(),
                    'is_active' => true,
                    'assigned_by' => auth()->id() ?: 1,
                ]);
            } elseif (!$shouldAssign && $existingAssignment) {
                // Remove from segment
                $existingAssignment->delete();
            }
        }
    }

    /**
     * Evaluate segment criteria
     */
    private function evaluateSegmentCriteria($customer, $analytics, $segment)
    {
        // Check purchase value criteria
        if ($segment->min_purchase_value && $analytics->total_purchase_value < $segment->min_purchase_value) {
            return false;
        }
        
        if ($segment->max_purchase_value && $analytics->total_purchase_value > $segment->max_purchase_value) {
            return false;
        }
        
        // Check order count criteria
        if ($segment->min_orders && $analytics->total_orders < $segment->min_orders) {
            return false;
        }
        
        if ($segment->max_orders && $analytics->total_orders > $segment->max_orders) {
            return false;
        }
        
        // Check days since last purchase
        if ($segment->min_days_since_last_purchase && 
            ($analytics->days_since_last_purchase === null || 
             $analytics->days_since_last_purchase < $segment->min_days_since_last_purchase)) {
            return false;
        }
        
        if ($segment->max_days_since_last_purchase && 
            $analytics->days_since_last_purchase > $segment->max_days_since_last_purchase) {
            return false;
        }
        
        return true;
    }

    /**
     * Get customer segmentation analytics
     */
    public function getSegmentationAnalytics()
    {
        $segments = CustomerSegment::with(['assignments.customer'])->get();
        
        $analytics = [];
        
        foreach ($segments as $segment) {
            $customers = $segment->assignments->where('is_active', true)->pluck('customer');
            
            $analytics[] = [
                'segment' => $segment,
                'customer_count' => $customers->count(),
                'total_value' => $customers->sum(function ($customer) {
                    return $customer->analytics ? $customer->analytics->total_purchase_value : 0;
                }),
                'average_value' => $customers->count() > 0 ? 
                    $customers->avg(function ($customer) {
                        return $customer->analytics ? $customer->analytics->total_purchase_value : 0;
                    }) : 0,
                'lifecycle_distribution' => $customers->groupBy(function ($customer) {
                    return $customer->analytics ? $customer->analytics->customer_lifecycle_stage : 'new';
                })->map->count(),
            ];
        }
        
        return $analytics;
    }

    /**
     * Create customer interaction
     */
    public function createInteraction($customerId, $interactionData)
    {
        $interactionData['customer_id'] = $customerId;
        $interactionData['user_id'] = auth()->id();
        
        $interaction = CustomerInteraction::create($interactionData);
        
        // Update customer analytics if this is a significant interaction
        if (in_array($interaction->interaction_type, ['purchase', 'complaint', 'compliment'])) {
            $this->updateCustomerAnalytics($customerId);
        }
        
        return $interaction;
    }

    /**
     * Get customer communication history
     */
    public function getCommunicationHistory($customerId, $limit = 20)
    {
        return CustomerInteraction::where('customer_id', $customerId)
                                 ->with(['user'])
                                 ->orderBy('created_at', 'desc')
                                 ->limit($limit)
                                 ->get();
    }

    /**
     * Calculate customer satisfaction score
     */
    public function calculateSatisfactionScore($customerId)
    {
        $interactions = CustomerInteraction::where('customer_id', $customerId)
                                          ->whereNotNull('customer_satisfaction')
                                          ->get();
        
        if ($interactions->isEmpty()) {
            return null;
        }
        
        $satisfactionMap = [
            'very_satisfied' => 5,
            'satisfied' => 4,
            'neutral' => 3,
            'dissatisfied' => 2,
            'very_dissatisfied' => 1,
        ];
        
        $totalScore = 0;
        $count = 0;
        
        foreach ($interactions as $interaction) {
            if (isset($satisfactionMap[$interaction->customer_satisfaction])) {
                $totalScore += $satisfactionMap[$interaction->customer_satisfaction];
                $count++;
            }
        }
        
        return $count > 0 ? round($totalScore / $count, 2) : null;
    }

    /**
     * Get customers needing attention
     */
    public function getCustomersNeedingAttention()
    {
        return [
            'high_risk' => CustomerAnalytics::highRisk()->with('customer')->get(),
            'at_risk' => CustomerAnalytics::atRisk()->with('customer')->get(),
            'dormant' => CustomerAnalytics::dormant()->with('customer')->get(),
            'overdue_follow_ups' => Customer::whereHas('interactions', function ($query) {
                $query->overdueFollowUp();
            })->with(['interactions' => function ($query) {
                $query->overdueFollowUp();
            }])->get(),
            'recent_complaints' => Customer::whereHas('interactions', function ($query) {
                $query->byType('complaint')
                      ->where('created_at', '>=', now()->subDays(7));
            })->with(['interactions' => function ($query) {
                $query->byType('complaint')
                      ->where('created_at', '>=', now()->subDays(7));
            }])->get(),
        ];
    }
}
