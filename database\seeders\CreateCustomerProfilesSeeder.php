<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Customer;

class CreateCustomerProfilesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create customer profiles for existing users
        $users = User::whereDoesntHave('customerProfile')->get();
        
        foreach ($users as $user) {
            $nameParts = explode(' ', $user->name, 2);
            $firstName = $nameParts[0];
            $lastName = $nameParts[1] ?? '';

            Customer::create([
                'customer_code' => $this->generateCustomerCode(),
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $user->email,
                'phone' => $user->phone ?: '+91 98765 43210', // Default phone if null
                'customer_type' => 'individual',
                'customer_segment' => $user->email === '<EMAIL>' ? 'vip' : 'regular',
                'is_active' => true,
                'kyc_status' => $user->email === '<EMAIL>' ? 'verified' : 'pending',
            ]);

            $this->command->info("Created customer profile for: {$user->email}");
        }
    }

    /**
     * Generate unique customer code
     */
    private function generateCustomerCode(): string
    {
        $prefix = 'WEB';
        $year = date('Y');
        
        // Get the last customer code for this year
        $lastCustomer = Customer::where('customer_code', 'like', $prefix . $year . '%')
                               ->orderBy('customer_code', 'desc')
                               ->first();
        
        if ($lastCustomer) {
            $lastNumber = (int) substr($lastCustomer->customer_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
