<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\RepairService;
use App\Models\Customer;
use App\Models\Location;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class RepairServiceController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_repair_services')->only(['index', 'show']);
        $this->middleware('permission:create_repair_services')->only(['create', 'store']);
        $this->middleware('permission:edit_repair_services')->only(['edit', 'update']);
        $this->middleware('permission:delete_repair_services')->only(['destroy']);
    }

    /**
     * Display a listing of repair services
     */
    public function index(Request $request)
    {
        $query = RepairService::with(['customer', 'location', 'assignedTo', 'createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('job_number', 'like', "%{$search}%")
                  ->orWhere('item_name', 'like', "%{$search}%")
                  ->orWhere('repair_description', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($cq) use ($search) {
                      $cq->where('name', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter by assigned user
        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        // Filter by customer
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // Filter by location
        if ($request->filled('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('received_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('received_date', '<=', $request->date_to);
        }

        // Filter overdue jobs
        if ($request->filled('overdue') && $request->overdue) {
            $query->overdue();
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $repairServices = $query->paginate(20);

        // Get filter options
        $customers = Customer::orderBy('name')->get(['id', 'name', 'phone']);
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $technicians = User::role(['technician', 'admin'])->orderBy('name')->get(['id', 'name']);

        $statuses = [
            'received' => 'Received',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
        ];

        $priorities = [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'urgent' => 'Urgent',
        ];

        return view('admin.repair-services.index', compact(
            'repairServices',
            'customers',
            'locations',
            'technicians',
            'statuses',
            'priorities'
        ));
    }

    /**
     * Show the form for creating a new repair service
     */
    public function create()
    {
        $customers = Customer::orderBy('name')->get(['id', 'name', 'phone', 'email']);
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $technicians = User::role(['technician', 'admin'])->orderBy('name')->get(['id', 'name']);

        return view('admin.repair-services.create', compact('customers', 'locations', 'technicians'));
    }

    /**
     * Store a newly created repair service
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'location_id' => 'required|exists:locations,id',
            'item_name' => 'required|string|max:255',
            'item_description' => 'nullable|string',
            'repair_description' => 'required|string',
            'estimated_cost' => 'nullable|numeric|min:0',
            'received_date' => 'required|date',
            'promised_date' => 'nullable|date|after:received_date',
            'priority' => 'required|in:low,medium,high,urgent',
            'customer_instructions' => 'nullable|string',
            'notes' => 'nullable|string',
            'assigned_to' => 'nullable|exists:users,id',
            'before_photo' => 'nullable|image|max:2048',
            'items' => 'nullable|array',
            'items.*.item_name' => 'required_with:items|string|max:255',
            'items.*.description' => 'nullable|string',
            'items.*.quantity' => 'required_with:items|numeric|min:0.001',
            'items.*.condition_before' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Handle photo upload
            $beforePhotoPath = null;
            if ($request->hasFile('before_photo')) {
                $beforePhotoPath = $request->file('before_photo')->store('repair-photos', 'public');
            }

            // Create repair service
            $repairService = RepairService::create([
                'customer_id' => $validated['customer_id'],
                'location_id' => $validated['location_id'],
                'item_name' => $validated['item_name'],
                'item_description' => $validated['item_description'],
                'repair_description' => $validated['repair_description'],
                'estimated_cost' => $validated['estimated_cost'],
                'received_date' => $validated['received_date'],
                'promised_date' => $validated['promised_date'],
                'priority' => $validated['priority'],
                'customer_instructions' => $validated['customer_instructions'],
                'notes' => $validated['notes'],
                'assigned_to' => $validated['assigned_to'],
                'before_photo_path' => $beforePhotoPath,
                'status' => 'received',
            ]);

            // Add repair items if provided
            if (!empty($validated['items'])) {
                foreach ($validated['items'] as $itemData) {
                    $repairService->repairItems()->create([
                        'item_name' => $itemData['item_name'],
                        'description' => $itemData['description'] ?? null,
                        'quantity' => $itemData['quantity'],
                        'condition_before' => $itemData['condition_before'] ?? null,
                    ]);
                }
            }

            DB::commit();

            return redirect()
                ->route('admin.repair-services.show', $repairService)
                ->with('success', 'Repair service created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Failed to create repair service: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified repair service
     */
    public function show(RepairService $repairService)
    {
        $repairService->load([
            'customer',
            'location',
            'assignedTo',
            'createdBy',
            'statusHistory.updatedBy',
            'repairItems',
            'repairCharges.createdBy',
            'customerNotifications'
        ]);

        $timelineEvents = $repairService->getTimelineEvents();

        return view('admin.repair-services.show', compact('repairService', 'timelineEvents'));
    }

    /**
     * Show the form for editing the specified repair service
     */
    public function edit(RepairService $repairService)
    {
        if (!$repairService->can_edit) {
            return redirect()
                ->route('admin.repair-services.show', $repairService)
                ->with('error', 'This repair service cannot be edited.');
        }

        $repairService->load(['repairItems', 'repairCharges']);
        $customers = Customer::orderBy('name')->get(['id', 'name', 'phone', 'email']);
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $technicians = User::role(['technician', 'admin'])->orderBy('name')->get(['id', 'name']);

        return view('admin.repair-services.edit', compact(
            'repairService',
            'customers',
            'locations',
            'technicians'
        ));
    }

    /**
     * Update the specified repair service
     */
    public function update(Request $request, RepairService $repairService)
    {
        if (!$repairService->can_edit) {
            return back()->with('error', 'This repair service cannot be updated.');
        }

        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'location_id' => 'required|exists:locations,id',
            'item_name' => 'required|string|max:255',
            'item_description' => 'nullable|string',
            'repair_description' => 'required|string',
            'estimated_cost' => 'nullable|numeric|min:0',
            'received_date' => 'required|date',
            'promised_date' => 'nullable|date|after:received_date',
            'priority' => 'required|in:low,medium,high,urgent',
            'customer_instructions' => 'nullable|string',
            'notes' => 'nullable|string',
            'assigned_to' => 'nullable|exists:users,id',
            'before_photo' => 'nullable|image|max:2048',
            'after_photo' => 'nullable|image|max:2048',
        ]);

        try {
            DB::beginTransaction();

            // Handle photo uploads
            $updateData = $validated;
            unset($updateData['before_photo'], $updateData['after_photo']);

            if ($request->hasFile('before_photo')) {
                // Delete old photo if exists
                if ($repairService->before_photo_path) {
                    Storage::disk('public')->delete($repairService->before_photo_path);
                }
                $updateData['before_photo_path'] = $request->file('before_photo')->store('repair-photos', 'public');
            }

            if ($request->hasFile('after_photo')) {
                // Delete old photo if exists
                if ($repairService->after_photo_path) {
                    Storage::disk('public')->delete($repairService->after_photo_path);
                }
                $updateData['after_photo_path'] = $request->file('after_photo')->store('repair-photos', 'public');
            }

            $repairService->update($updateData);

            DB::commit();

            return redirect()
                ->route('admin.repair-services.show', $repairService)
                ->with('success', 'Repair service updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Failed to update repair service: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified repair service
     */
    public function destroy(RepairService $repairService)
    {
        if (!$repairService->can_cancel) {
            return back()->with('error', 'This repair service cannot be deleted.');
        }

        try {
            // Delete associated photos
            if ($repairService->before_photo_path) {
                Storage::disk('public')->delete($repairService->before_photo_path);
            }
            if ($repairService->after_photo_path) {
                Storage::disk('public')->delete($repairService->after_photo_path);
            }

            $repairService->delete();

            return redirect()
                ->route('admin.repair-services.index')
                ->with('success', 'Repair service deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete repair service: ' . $e->getMessage());
        }
    }

    /**
     * Start work on repair service
     */
    public function startWork(Request $request, RepairService $repairService)
    {
        $validated = $request->validate([
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $repairService->startWork($validated['notes'] ?? null);

            return back()->with('success', 'Work started on repair service.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to start work: ' . $e->getMessage());
        }
    }

    /**
     * Complete work on repair service
     */
    public function completeWork(Request $request, RepairService $repairService)
    {
        $validated = $request->validate([
            'actual_cost' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $repairService->completeWork(
                $validated['actual_cost'] ?? null,
                $validated['notes'] ?? null
            );

            return back()->with('success', 'Repair work completed successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to complete work: ' . $e->getMessage());
        }
    }

    /**
     * Deliver item to customer
     */
    public function deliverItem(Request $request, RepairService $repairService)
    {
        $validated = $request->validate([
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $repairService->deliverItem($validated['notes'] ?? null);

            return back()->with('success', 'Item delivered to customer successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to deliver item: ' . $e->getMessage());
        }
    }

    /**
     * Cancel repair service
     */
    public function cancel(Request $request, RepairService $repairService)
    {
        $validated = $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $repairService->cancelJob($validated['reason']);

            return back()->with('success', 'Repair service cancelled successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to cancel repair service: ' . $e->getMessage());
        }
    }

    /**
     * Assign repair service to technician
     */
    public function assign(Request $request, RepairService $repairService)
    {
        $validated = $request->validate([
            'assigned_to' => 'required|exists:users,id',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $repairService->assignTo($validated['assigned_to'], $validated['notes'] ?? null);

            return back()->with('success', 'Repair service assigned successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to assign repair service: ' . $e->getMessage());
        }
    }

    /**
     * Update estimate
     */
    public function updateEstimate(Request $request, RepairService $repairService)
    {
        $validated = $request->validate([
            'estimated_cost' => 'required|numeric|min:0',
            'promised_date' => 'nullable|date|after:today',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $repairService->updateEstimate(
                $validated['estimated_cost'],
                $validated['promised_date'] ?? null,
                $validated['notes'] ?? null
            );

            return back()->with('success', 'Estimate updated successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update estimate: ' . $e->getMessage());
        }
    }

    /**
     * Add charge to repair service
     */
    public function addCharge(Request $request, RepairService $repairService)
    {
        $validated = $request->validate([
            'description' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'charge_type' => 'required|in:labor,material,parts,other',
        ]);

        try {
            $repairService->addCharge(
                $validated['description'],
                $validated['amount'],
                $validated['charge_type']
            );

            return back()->with('success', 'Charge added successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to add charge: ' . $e->getMessage());
        }
    }

    /**
     * Upload photos
     */
    public function uploadPhoto(Request $request, RepairService $repairService)
    {
        $validated = $request->validate([
            'photo' => 'required|image|max:2048',
            'type' => 'required|in:before,after',
        ]);

        try {
            $photoPath = $request->file('photo')->store('repair-photos', 'public');
            $repairService->uploadPhoto($photoPath, $validated['type']);

            return back()->with('success', ucfirst($validated['type']) . ' photo uploaded successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to upload photo: ' . $e->getMessage());
        }
    }

    /**
     * Print repair job ticket
     */
    public function print(RepairService $repairService)
    {
        $repairService->load([
            'customer',
            'location',
            'assignedTo',
            'createdBy',
            'repairItems',
            'repairCharges'
        ]);

        return view('admin.repair-services.print', compact('repairService'));
    }

    /**
     * Export repair services to CSV
     */
    public function export(Request $request)
    {
        $query = RepairService::with(['customer', 'location', 'assignedTo']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('received_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('received_date', '<=', $request->date_to);
        }

        $repairServices = $query->get();

        $filename = 'repair_services_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($repairServices) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Job Number', 'Customer', 'Item Name', 'Repair Description',
                'Status', 'Priority', 'Estimated Cost', 'Actual Cost',
                'Received Date', 'Promised Date', 'Completed Date', 'Delivered Date',
                'Assigned To', 'Location', 'Created At'
            ]);

            // CSV data
            foreach ($repairServices as $service) {
                fputcsv($file, [
                    $service->job_number,
                    $service->customer->name,
                    $service->item_name,
                    $service->repair_description,
                    $service->status_display,
                    $service->priority_display,
                    $service->estimated_cost,
                    $service->actual_cost,
                    $service->received_date->format('Y-m-d'),
                    $service->promised_date?->format('Y-m-d'),
                    $service->completed_date?->format('Y-m-d'),
                    $service->delivered_date?->format('Y-m-d'),
                    $service->assignedTo?->name,
                    $service->location->name,
                    $service->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get repair service statistics
     */
    public function getStatistics(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = now()->subDays($days);

        $stats = [
            'total_jobs' => RepairService::where('created_at', '>=', $startDate)->count(),
            'pending_jobs' => RepairService::whereIn('status', ['received', 'in_progress'])->count(),
            'overdue_jobs' => RepairService::overdue()->count(),
            'completed_jobs' => RepairService::where('status', 'completed')
                                            ->where('created_at', '>=', $startDate)
                                            ->count(),
            'delivered_jobs' => RepairService::where('status', 'delivered')
                                            ->where('created_at', '>=', $startDate)
                                            ->count(),
            'jobs_by_status' => RepairService::where('created_at', '>=', $startDate)
                                            ->groupBy('status')
                                            ->selectRaw('status, count(*) as count')
                                            ->pluck('count', 'status'),
            'jobs_by_priority' => RepairService::where('created_at', '>=', $startDate)
                                              ->groupBy('priority')
                                              ->selectRaw('priority, count(*) as count')
                                              ->pluck('count', 'priority'),
            'average_completion_time' => RepairService::whereNotNull('completed_date')
                                                   ->where('created_at', '>=', $startDate)
                                                   ->get()
                                                   ->avg('days_in_progress'),
            'total_revenue' => RepairService::where('status', 'delivered')
                                          ->where('created_at', '>=', $startDate)
                                          ->sum('actual_cost'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}
