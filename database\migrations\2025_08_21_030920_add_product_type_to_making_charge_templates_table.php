<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('making_charge_templates', function (Blueprint $table) {
            // Add missing columns from the original migration
            if (!Schema::hasColumn('making_charge_templates', 'product_type')) {
                $table->string('product_type')->nullable()->after('metal_id'); // ring, necklace, bracelet, etc.
            }
            if (!Schema::hasColumn('making_charge_templates', 'percentage_charge')) {
                $table->decimal('percentage_charge', 5, 2)->default(0)->after('product_type'); // Making charge percentage
            }
            if (!Schema::hasColumn('making_charge_templates', 'fixed_charge')) {
                $table->decimal('fixed_charge', 10, 2)->default(0)->after('percentage_charge'); // Fixed making charge
            }
            if (!Schema::hasColumn('making_charge_templates', 'wastage_percentage')) {
                $table->decimal('wastage_percentage', 5, 2)->default(0)->after('max_charge'); // Wastage percentage
            }
            if (!Schema::hasColumn('making_charge_templates', 'weight_range_min')) {
                $table->decimal('weight_range_min', 8, 3)->nullable()->after('wastage_percentage'); // Minimum weight for this template
            }
            if (!Schema::hasColumn('making_charge_templates', 'weight_range_max')) {
                $table->decimal('weight_range_max', 8, 3)->nullable()->after('weight_range_min'); // Maximum weight for this template
            }
            if (!Schema::hasColumn('making_charge_templates', 'additional_charges')) {
                $table->json('additional_charges')->nullable()->after('weight_range_max'); // Stone setting, polishing, etc.
            }
            if (!Schema::hasColumn('making_charge_templates', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('is_active');
            }
            if (!Schema::hasColumn('making_charge_templates', 'location_id')) {
                $table->foreignId('location_id')->nullable()->constrained()->onDelete('set null')->after('sort_order');
            }
            if (!Schema::hasColumn('making_charge_templates', 'created_by')) {
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade')->after('location_id');
            }
            if (!Schema::hasColumn('making_charge_templates', 'updated_by')) {
                $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null')->after('created_by');
            }

            // Add indexes
            if (!Schema::hasIndex('making_charge_templates', 'making_charge_templates_metal_id_product_type_is_active_index')) {
                $table->index(['metal_id', 'product_type', 'is_active']);
            }
            if (!Schema::hasIndex('making_charge_templates', 'making_charge_templates_location_id_is_active_index')) {
                $table->index(['location_id', 'is_active']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('making_charge_templates', function (Blueprint $table) {
            $table->dropColumn([
                'product_type',
                'percentage_charge',
                'fixed_charge',
                'wastage_percentage',
                'weight_range_min',
                'weight_range_max',
                'additional_charges',
                'sort_order',
                'location_id',
                'created_by',
                'updated_by'
            ]);
        });
    }
};
