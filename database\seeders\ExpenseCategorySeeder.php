<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ExpenseCategory;
use App\Models\User;

class ExpenseCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user for created_by
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        $categories = [
            [
                'name' => 'Raw Materials',
                'description' => 'Gold, silver, platinum, and other raw materials',
                'is_active' => true,
            ],
            [
                'name' => 'Stones & Gems',
                'description' => 'Diamonds, precious stones, and semi-precious stones',
                'is_active' => true,
            ],
            [
                'name' => 'Tools & Equipment',
                'description' => 'Jewelry making tools, machinery, and equipment',
                'is_active' => true,
            ],
            [
                'name' => 'Packaging Materials',
                'description' => 'Jewelry boxes, pouches, bags, and packaging supplies',
                'is_active' => true,
            ],
            [
                'name' => 'Office Supplies',
                'description' => 'Stationery, printing, and office equipment',
                'is_active' => true,
            ],
            [
                'name' => 'Utilities',
                'description' => 'Electricity, water, internet, and other utilities',
                'is_active' => true,
            ],
            [
                'name' => 'Rent & Lease',
                'description' => 'Store rent, equipment lease, and property expenses',
                'is_active' => true,
            ],
            [
                'name' => 'Marketing & Advertising',
                'description' => 'Promotional activities, advertising, and marketing campaigns',
                'is_active' => true,
            ],
            [
                'name' => 'Professional Services',
                'description' => 'Legal, accounting, consulting, and professional fees',
                'is_active' => true,
            ],
            [
                'name' => 'Transportation',
                'description' => 'Delivery, shipping, and transportation costs',
                'is_active' => true,
            ],
            [
                'name' => 'Insurance',
                'description' => 'Business insurance, inventory insurance, and coverage',
                'is_active' => true,
            ],
            [
                'name' => 'Maintenance & Repairs',
                'description' => 'Equipment maintenance, store repairs, and upkeep',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            $category['created_by'] = $user->id;
            ExpenseCategory::create($category);
        }
    }
}
