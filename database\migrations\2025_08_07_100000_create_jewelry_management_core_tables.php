<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Personal Access Tokens table for Sanctum (skip if exists)
        // This table is usually created by Laravel Sanctum migration

        // Repair Jobs table (enhanced version)
        if (!Schema::hasTable('repair_jobs')) {
            Schema::create('repair_jobs', function (Blueprint $table) {
                $table->id();
                $table->foreignId('customer_id')->constrained()->onDelete('cascade');
                $table->foreignId('product_id')->nullable()->constrained()->onDelete('set null');
                $table->string('job_number')->unique();
                $table->string('item_description');
                $table->text('issue_description');
                $table->decimal('estimated_cost', 10, 2)->nullable();
                $table->decimal('actual_cost', 10, 2)->nullable();
                $table->date('estimated_completion_date')->nullable();
                $table->date('actual_completion_date')->nullable();
                $table->enum('status', ['received', 'in_progress', 'completed', 'ready_for_delivery', 'delivered', 'cancelled'])->default('received');
                $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
                $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
                $table->foreignId('received_by')->nullable()->constrained('users')->onDelete('set null');
                $table->foreignId('delivered_by')->nullable()->constrained('users')->onDelete('set null');
                $table->text('notes')->nullable();
                $table->json('images')->nullable();
                $table->string('receipt_number')->nullable();
                $table->string('delivery_receipt_number')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->foreignId('location_id')->constrained()->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['status']);
                $table->index(['priority']);
                $table->index(['estimated_completion_date']);
                $table->index(['created_at']);
            });
        }

        // API Logs table for tracking API usage
        if (!Schema::hasTable('api_logs')) {
            Schema::create('api_logs', function (Blueprint $table) {
                $table->id();
                $table->string('method', 10);
                $table->string('url');
                $table->json('headers')->nullable();
                $table->json('request_data')->nullable();
                $table->json('response_data')->nullable();
                $table->integer('status_code');
                $table->float('response_time')->nullable();
                $table->string('ip_address', 45);
                $table->string('user_agent')->nullable();
                $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
                $table->timestamps();

                $table->index(['method']);
                $table->index(['status_code']);
                $table->index(['created_at']);
                $table->index(['user_id']);
            });
        }

        // System Settings table
        if (!Schema::hasTable('system_settings')) {
            Schema::create('system_settings', function (Blueprint $table) {
                $table->id();
                $table->string('key')->unique();
                $table->text('value')->nullable();
                $table->string('type')->default('string'); // string, integer, boolean, json
                $table->string('group')->default('general');
                $table->text('description')->nullable();
                $table->boolean('is_public')->default(false);
                $table->timestamps();

                $table->index(['group']);
                $table->index(['is_public']);
            });
        }

        // User Preferences table
        if (!Schema::hasTable('user_preferences')) {
            Schema::create('user_preferences', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('key');
                $table->text('value')->nullable();
                $table->timestamps();

                $table->unique(['user_id', 'key']);
                $table->index(['user_id']);
            });
        }

        // Backup Logs table
        if (!Schema::hasTable('backup_logs')) {
            Schema::create('backup_logs', function (Blueprint $table) {
                $table->id();
                $table->string('filename');
                $table->string('path');
                $table->bigInteger('size')->nullable();
                $table->enum('type', ['full', 'incremental', 'differential'])->default('full');
                $table->enum('status', ['started', 'completed', 'failed'])->default('started');
                $table->text('error_message')->nullable();
                $table->timestamp('started_at');
                $table->timestamp('completed_at')->nullable();
                $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
                $table->timestamps();

                $table->index(['status']);
                $table->index(['type']);
                $table->index(['started_at']);
            });
        }

        // Notification Templates table
        if (!Schema::hasTable('notification_templates')) {
            Schema::create('notification_templates', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('type'); // email, sms, whatsapp
                $table->string('event'); // birthday, anniversary, payment_reminder, etc.
                $table->string('subject')->nullable();
                $table->text('content');
                $table->json('variables')->nullable(); // Available template variables
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                $table->index(['type']);
                $table->index(['event']);
                $table->index(['is_active']);
            });
        }

        // Activity Logs table (enhanced version of existing)
        if (!Schema::hasTable('activity_logs')) {
            Schema::create('activity_logs', function (Blueprint $table) {
                $table->id();
                $table->string('log_name')->nullable();
                $table->text('description');
                $table->nullableMorphs('subject', 'subject');
                $table->nullableMorphs('causer', 'causer');
                $table->json('properties')->nullable();
                $table->string('event')->nullable();
                $table->uuid('batch_uuid')->nullable();
                $table->timestamps();

                $table->index('log_name');
                $table->index(['subject_type', 'subject_id'], 'subject');
                $table->index(['causer_type', 'causer_id'], 'causer');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
        Schema::dropIfExists('notification_templates');
        Schema::dropIfExists('backup_logs');
        Schema::dropIfExists('user_preferences');
        Schema::dropIfExists('system_settings');
        Schema::dropIfExists('api_logs');
        Schema::dropIfExists('repair_jobs');
        Schema::dropIfExists('personal_access_tokens');
    }
};
