<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class JournalEntry extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'journal_number',
        'entry_date',
        'description',
        'reference_type',
        'reference_id',
        'total_debit',
        'total_credit',
        'status',
        'posted_at',
        'posted_by',
        'created_by',
        'notes',
    ];

    protected $casts = [
        'entry_date' => 'date',
        'total_debit' => 'decimal:2',
        'total_credit' => 'decimal:2',
        'posted_at' => 'datetime',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($entry) {
            if (!$entry->journal_number) {
                $entry->journal_number = static::generateJournalNumber();
            }
            $entry->created_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function postedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'posted_by');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(AccountTransaction::class);
    }

    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scopes
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopePosted($query)
    {
        return $query->where('status', 'posted');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('entry_date', [$startDate, $endDate]);
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute(): string
    {
        $statuses = [
            'draft' => 'Draft',
            'posted' => 'Posted',
            'cancelled' => 'Cancelled',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getIsPostedAttribute(): bool
    {
        return $this->status === 'posted';
    }

    public function getIsBalancedAttribute(): bool
    {
        return abs($this->total_debit - $this->total_credit) < 0.01;
    }

    public function getCanPostAttribute(): bool
    {
        return $this->status === 'draft' && $this->is_balanced && $this->transactions->count() >= 2;
    }

    public function getCanEditAttribute(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Business Logic Methods
     */
    public static function generateJournalNumber(): string
    {
        $prefix = 'JE';
        $year = now()->format('Y');
        $month = now()->format('m');

        $lastEntry = static::whereYear('created_at', now()->year)
                          ->whereMonth('created_at', now()->month)
                          ->orderBy('id', 'desc')
                          ->first();

        $sequence = $lastEntry ? (int) substr($lastEntry->journal_number, -4) + 1 : 1;

        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function addTransaction($accountId, $debitAmount = 0, $creditAmount = 0, $description = null): AccountTransaction
    {
        if (!$this->can_edit) {
            throw new \Exception('Cannot modify posted journal entry');
        }

        $transaction = $this->transactions()->create([
            'account_id' => $accountId,
            'transaction_date' => $this->entry_date,
            'transaction_type' => 'journal',
            'debit_amount' => $debitAmount,
            'credit_amount' => $creditAmount,
            'description' => $description ?: $this->description,
            'reference_type' => get_class($this),
            'reference_id' => $this->id,
        ]);

        $this->updateTotals();

        return $transaction;
    }

    public function updateTotals(): self
    {
        $this->update([
            'total_debit' => $this->transactions()->sum('debit_amount'),
            'total_credit' => $this->transactions()->sum('credit_amount'),
        ]);

        return $this;
    }

    public function post(): self
    {
        if (!$this->can_post) {
            throw new \Exception('Journal entry cannot be posted. Check if it is balanced and has at least 2 transactions.');
        }

        $this->update([
            'status' => 'posted',
            'posted_at' => now(),
            'posted_by' => auth()->id(),
        ]);

        return $this;
    }

    public function cancel($reason = null): self
    {
        if ($this->is_posted) {
            throw new \Exception('Cannot cancel posted journal entry');
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? ($this->notes . "\n\nCancelled: " . $reason) : $this->notes,
        ]);

        // Delete associated transactions
        $this->transactions()->delete();

        return $this;
    }

    public static function createSimpleEntry($description, $debitAccountId, $creditAccountId, $amount, $entryDate = null): self
    {
        $entry = static::create([
            'entry_date' => $entryDate ?: now()->toDateString(),
            'description' => $description,
            'status' => 'draft',
        ]);

        $entry->addTransaction($debitAccountId, $amount, 0, $description);
        $entry->addTransaction($creditAccountId, 0, $amount, $description);

        return $entry;
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'journal_number' => $this->journal_number,
            'entry_date' => $this->entry_date->format('Y-m-d'),
            'description' => $this->description,
            'total_debit' => $this->total_debit,
            'total_credit' => $this->total_credit,
            'status' => $this->status_display,
            'is_balanced' => $this->is_balanced,
            'is_posted' => $this->is_posted,
            'can_post' => $this->can_post,
            'can_edit' => $this->can_edit,
            'transactions_count' => $this->transactions->count(),
            'posted_at' => $this->posted_at?->format('Y-m-d H:i:s'),
            'posted_by' => $this->postedBy?->name,
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
