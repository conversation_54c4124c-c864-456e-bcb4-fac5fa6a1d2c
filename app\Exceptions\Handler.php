<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Database\QueryException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $exception)
    {
        // Handle unique constraint violations
        if ($exception instanceof QueryException ||
            $exception instanceof \Illuminate\Database\UniqueConstraintViolationException) {

            $message = $exception->getMessage();

            if (str_contains($message, 'Duplicate entry') ||
                str_contains($message, 'UNIQUE constraint failed') ||
                str_contains($message, 'Integrity constraint violation') ||
                $exception instanceof \Illuminate\Database\UniqueConstraintViolationException) {

                // Log the full stack trace to help debug
                \Log::error('🚨 UNIQUE CONSTRAINT VIOLATION AT CONNECTION.PHP:824', [
                    'exception_type' => get_class($exception),
                    'message' => $exception->getMessage(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine(),
                    'sql' => method_exists($exception, 'getSql') ? $exception->getSql() : 'N/A',
                    'bindings' => method_exists($exception, 'getBindings') ? $exception->getBindings() : 'N/A',
                    'request_url' => $request->fullUrl(),
                    'request_method' => $request->method(),
                    'request_data' => $this->sanitizeRequestData($request->all()),
                    'user_id' => auth()->id(),
                    'user_agent' => $request->userAgent(),
                    'ip_address' => $request->ip(),
                    'timestamp' => now()->toISOString(),
                    'stack_trace' => $exception->getTraceAsString(),
                ]);

                // Also log to a separate file for easier tracking
                \Log::channel('single')->error('UNIQUE_CONSTRAINT_824: ' . $message, [
                    'url' => $request->fullUrl(),
                    'data' => $this->sanitizeRequestData($request->all()),
                    'user' => auth()->id(),
                ]);

                return UniqueConstraintHandler::handle($exception, $request);
            }
        }

        return parent::render($request, $exception);
    }

    /**
     * Sanitize request data for logging (remove sensitive information)
     */
    private function sanitizeRequestData(array $data): array
    {
        $sensitiveFields = ['password', 'password_confirmation', 'current_password', 'token', 'api_key'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }
        
        return $data;
    }
}
