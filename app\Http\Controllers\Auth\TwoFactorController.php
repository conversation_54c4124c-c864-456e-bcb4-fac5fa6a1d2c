<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use PragmaRX\Google2FA\Google2FA;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class TwoFactorController extends Controller
{
    protected $google2fa;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
        $this->middleware('auth');
    }

    /**
     * Show 2FA setup page
     */
    public function show()
    {
        $user = Auth::user();
        
        if (!$user->two_factor_secret) {
            // Generate new secret
            $user->two_factor_secret = $this->google2fa->generateSecretKey();
            $user->save();
        }

        $qrCodeUrl = $this->google2fa->getQRCodeUrl(
            config('app.name'),
            $user->email,
            $user->two_factor_secret
        );

        // Generate QR Code using SimpleSoftwareIO QrCode
        $qrCode = base64_encode(QrCode::format('png')->size(200)->generate($qrCodeUrl));

        return view('auth.two-factor.setup', [
            'qrCode' => $qrCode,
            'secret' => $user->two_factor_secret,
            'enabled' => $user->two_factor_enabled
        ]);
    }

    /**
     * Enable 2FA
     */
    public function enable(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
            'password' => 'required|string'
        ]);

        $user = Auth::user();

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'password' => ['The provided password is incorrect.']
            ]);
        }

        // Verify 2FA code
        if (!$this->google2fa->verifyKey($user->two_factor_secret, $request->code)) {
            throw ValidationException::withMessages([
                'code' => ['The provided two factor authentication code is invalid.']
            ]);
        }

        // Enable 2FA
        $user->two_factor_enabled = true;
        $user->two_factor_recovery_codes = $this->generateRecoveryCodes();
        $user->save();

        return redirect()->route('two-factor.show')
            ->with('success', 'Two factor authentication has been enabled.');
    }

    /**
     * Disable 2FA
     */
    public function disable(Request $request)
    {
        $request->validate([
            'password' => 'required|string'
        ]);

        $user = Auth::user();

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'password' => ['The provided password is incorrect.']
            ]);
        }

        // Disable 2FA
        $user->two_factor_enabled = false;
        $user->two_factor_secret = null;
        $user->two_factor_recovery_codes = null;
        $user->save();

        return redirect()->route('two-factor.show')
            ->with('success', 'Two factor authentication has been disabled.');
    }

    /**
     * Show 2FA challenge
     */
    public function challenge()
    {
        if (!session('login.id')) {
            return redirect()->route('login');
        }

        return view('auth.two-factor.challenge');
    }

    /**
     * Verify 2FA challenge
     */
    public function verify(Request $request)
    {
        $request->validate([
            'code' => 'required|string'
        ]);

        $userId = session('login.id');
        $user = \App\Models\User::find($userId);

        if (!$user) {
            return redirect()->route('login');
        }

        $isValid = false;

        // Check if it's a recovery code
        if (strlen($request->code) === 10) {
            $recoveryCodes = $user->two_factor_recovery_codes ?? [];
            if (in_array($request->code, $recoveryCodes)) {
                // Remove used recovery code
                $recoveryCodes = array_diff($recoveryCodes, [$request->code]);
                $user->two_factor_recovery_codes = array_values($recoveryCodes);
                $user->save();
                $isValid = true;
            }
        } else {
            // Verify 2FA code
            $isValid = $this->google2fa->verifyKey($user->two_factor_secret, $request->code);
        }

        if (!$isValid) {
            throw ValidationException::withMessages([
                'code' => ['The provided two factor authentication code is invalid.']
            ]);
        }

        // Complete login
        Auth::login($user, session('login.remember', false));
        session()->forget(['login.id', 'login.remember']);

        return redirect()->intended(route('admin.dashboard'));
    }

    /**
     * Generate recovery codes
     */
    protected function generateRecoveryCodes()
    {
        $codes = [];
        for ($i = 0; $i < 8; $i++) {
            $codes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 10));
        }
        return $codes;
    }

    /**
     * Show recovery codes
     */
    public function recoveryCodes()
    {
        $user = Auth::user();
        
        if (!$user->two_factor_enabled) {
            return redirect()->route('two-factor.show');
        }

        return view('auth.two-factor.recovery-codes', [
            'recoveryCodes' => $user->two_factor_recovery_codes ?? []
        ]);
    }

    /**
     * Regenerate recovery codes
     */
    public function regenerateRecoveryCodes(Request $request)
    {
        $request->validate([
            'password' => 'required|string'
        ]);

        $user = Auth::user();

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'password' => ['The provided password is incorrect.']
            ]);
        }

        $user->two_factor_recovery_codes = $this->generateRecoveryCodes();
        $user->save();

        return redirect()->route('two-factor.recovery-codes')
            ->with('success', 'Recovery codes have been regenerated.');
    }
}
