<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MakingChargeTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'metal_id',
        'product_type',
        'percentage_charge',
        'fixed_charge',
        'min_charge',
        'max_charge',
        'wastage_percentage',
        'weight_range_min',
        'weight_range_max',
        'additional_charges',
        'is_active',
        'sort_order',
        'location_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'percentage_charge' => 'decimal:2',
        'fixed_charge' => 'decimal:2',
        'min_charge' => 'decimal:2',
        'max_charge' => 'decimal:2',
        'wastage_percentage' => 'decimal:2',
        'weight_range_min' => 'decimal:3',
        'weight_range_max' => 'decimal:3',
        'additional_charges' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function metal(): BelongsTo
    {
        return $this->belongsTo(Metal::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForMetal($query, $metalId)
    {
        return $query->where('metal_id', $metalId);
    }

    public function scopeForProductType($query, $productType)
    {
        return $query->where('product_type', $productType);
    }

    public function scopeForWeight($query, $weight)
    {
        return $query->where(function ($q) use ($weight) {
            $q->where(function ($subQ) use ($weight) {
                $subQ->whereNull('weight_range_min')
                     ->orWhere('weight_range_min', '<=', $weight);
            })
            ->where(function ($subQ) use ($weight) {
                $subQ->whereNull('weight_range_max')
                     ->orWhere('weight_range_max', '>=', $weight);
            });
        });
    }

    public function scopeForLocation($query, $locationId)
    {
        return $query->where(function ($q) use ($locationId) {
            $q->whereNull('location_id')
              ->orWhere('location_id', $locationId);
        });
    }

    /**
     * Calculate making charges for given parameters
     */
    public function calculateCharges($metalValue, $weight = null)
    {
        $makingCharges = 0;

        // Percentage-based charge
        if ($this->percentage_charge > 0) {
            $makingCharges += $metalValue * ($this->percentage_charge / 100);
        }

        // Fixed charge
        if ($this->fixed_charge > 0) {
            $makingCharges += $this->fixed_charge;
        }

        // Apply minimum charge
        if ($this->min_charge > 0) {
            $makingCharges = max($makingCharges, $this->min_charge);
        }

        // Apply maximum charge
        if ($this->max_charge > 0) {
            $makingCharges = min($makingCharges, $this->max_charge);
        }

        // Calculate additional charges
        $additionalCharges = 0;
        if ($this->additional_charges) {
            foreach ($this->additional_charges as $charge) {
                if (isset($charge['amount'])) {
                    $additionalCharges += $charge['amount'];
                }
            }
        }

        return [
            'base_making_charges' => $makingCharges,
            'additional_charges' => $additionalCharges,
            'total_making_charges' => $makingCharges + $additionalCharges,
            'template_used' => $this->name,
            'percentage_applied' => $this->percentage_charge,
            'fixed_charge_applied' => $this->fixed_charge,
        ];
    }

    /**
     * Check if template applies to given parameters
     */
    public function appliesTo($metalId, $productType = null, $weight = null, $locationId = null)
    {
        // Check metal
        if ($this->metal_id !== $metalId) {
            return false;
        }

        // Check product type
        if ($this->product_type && $productType && $this->product_type !== $productType) {
            return false;
        }

        // Check weight range
        if ($weight) {
            if ($this->weight_range_min && $weight < $this->weight_range_min) {
                return false;
            }
            if ($this->weight_range_max && $weight > $this->weight_range_max) {
                return false;
            }
        }

        // Check location
        if ($this->location_id && $locationId && $this->location_id !== $locationId) {
            return false;
        }

        return $this->is_active;
    }

    /**
     * Get formatted weight range
     */
    public function getWeightRangeAttribute()
    {
        if (!$this->weight_range_min && !$this->weight_range_max) {
            return 'All weights';
        }

        if ($this->weight_range_min && $this->weight_range_max) {
            return "{$this->weight_range_min}g - {$this->weight_range_max}g";
        }

        if ($this->weight_range_min) {
            return "Above {$this->weight_range_min}g";
        }

        return "Up to {$this->weight_range_max}g";
    }

    /**
     * Get formatted charge description
     */
    public function getChargeDescriptionAttribute()
    {
        $parts = [];

        if ($this->percentage_charge > 0) {
            $parts[] = "{$this->percentage_charge}% of metal value";
        }

        if ($this->fixed_charge > 0) {
            $parts[] = "₹{$this->fixed_charge} fixed";
        }

        if ($this->min_charge > 0) {
            $parts[] = "min ₹{$this->min_charge}";
        }

        if ($this->max_charge > 0) {
            $parts[] = "max ₹{$this->max_charge}";
        }

        return implode(' + ', $parts) ?: 'No charges defined';
    }
}
