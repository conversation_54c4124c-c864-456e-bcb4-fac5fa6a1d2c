<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Metal extends Model
{
    protected $fillable = [
        'name',
        'purity',
        'symbol',
        'current_rate_per_gram',
        'rate_updated_at',
        'is_active',
    ];

    protected $casts = [
        'current_rate_per_gram' => 'decimal:2',
        'rate_updated_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    public function rates(): HasMany
    {
        return $this->hasMany(MetalRate::class);
    }

    public function rateHistory(): HasMany
    {
        return $this->hasMany(MetalRateHistory::class);
    }

    public function makingChargeTemplates(): HasMany
    {
        return $this->hasMany(MakingChargeTemplate::class);
    }

    // Rate management methods
    public function updateRate(float $newRate, string $source = 'manual'): bool
    {
        $today = Carbon::today();

        // Update current rate
        $this->update([
            'current_rate_per_gram' => $newRate,
            'rate_updated_at' => now(),
        ]);

        // Store in history (avoid duplicates for same date)
        $this->rateHistory()->updateOrCreate(
            ['rate_date' => $today],
            [
                'rate_per_gram' => $newRate,
                'source' => $source,
            ]
        );

        return true;
    }

    public function getRateForDate(Carbon $date): ?float
    {
        $rateHistory = $this->rateHistory()
            ->where('rate_date', '<=', $date->format('Y-m-d'))
            ->orderBy('rate_date', 'desc')
            ->first();

        return $rateHistory ? $rateHistory->rate_per_gram : $this->current_rate_per_gram;
    }

    public function getFormattedCurrentRateAttribute(): string
    {
        return '₹' . number_format($this->current_rate_per_gram, 2);
    }

    public function getFullNameAttribute(): string
    {
        return "{$this->name} {$this->purity}";
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeGold($query)
    {
        return $query->where('name', 'Gold');
    }

    public function scopeSilver($query)
    {
        return $query->where('name', 'Silver');
    }

    // Static methods for common operations
    public static function getGoldRates(): array
    {
        return static::gold()->active()->get()->mapWithKeys(function ($metal) {
            return [$metal->purity => $metal->current_rate_per_gram];
        })->toArray();
    }

    public static function getSilverRates(): array
    {
        return static::silver()->active()->get()->mapWithKeys(function ($metal) {
            return [$metal->purity => $metal->current_rate_per_gram];
        })->toArray();
    }
}
