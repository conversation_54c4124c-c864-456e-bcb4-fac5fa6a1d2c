<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules;

class AuthController extends Controller
{
    /**
     * Show the login form
     */
    public function showLogin()
    {
        return view('website.auth.login');
    }

    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            // Redirect to intended page or cart checkout
            $intended = session('url.intended', route('website.home'));
            
            // If they were trying to checkout, redirect there
            if (str_contains($intended, 'checkout')) {
                return redirect()->route('website.cart.checkout')
                               ->with('success', 'Welcome back! Please complete your order.');
            }

            return redirect($intended)
                         ->with('success', 'Welcome back!');
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    /**
     * Show the registration form
     */
    public function showRegister()
    {
        return view('website.auth.register');
    }

    /**
     * Handle registration request
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        DB::beginTransaction();

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'is_active' => true,
            ]);

            // Create corresponding Customer profile for website users
            $nameParts = explode(' ', $request->name, 2);
            $firstName = $nameParts[0];
            $lastName = $nameParts[1] ?? '';

            Customer::create([
                'customer_code' => $this->generateCustomerCode(),
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $request->email,
                'phone' => $request->phone,
                'customer_type' => 'individual',
                'customer_segment' => 'regular',
                'is_active' => true,
                'kyc_status' => 'pending',
            ]);

            DB::commit();
            Auth::login($user);
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['email' => 'Registration failed. Please try again.'])
                         ->withInput();
        }

        // Redirect to intended page or cart checkout
        $intended = session('url.intended', route('website.home'));
        
        // If they were trying to checkout, redirect there
        if (str_contains($intended, 'checkout')) {
            return redirect()->route('website.cart.checkout')
                           ->with('success', 'Account created successfully! Please complete your order.');
        }

        return redirect($intended)
                     ->with('success', 'Account created successfully! Welcome to JewelSoft.');
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('website.home')
                       ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Generate unique customer code
     */
    private function generateCustomerCode(): string
    {
        $prefix = 'WEB';
        $year = date('Y');

        // Get the last customer code for this year
        $lastCustomer = Customer::where('customer_code', 'like', $prefix . $year . '%')
                               ->orderBy('customer_code', 'desc')
                               ->first();

        if ($lastCustomer) {
            $lastNumber = (int) substr($lastCustomer->customer_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
