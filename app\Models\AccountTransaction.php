<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class AccountTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'transaction_number',
        'account_id',
        'transaction_date',
        'transaction_type',
        'debit_amount',
        'credit_amount',
        'description',
        'reference_type',
        'reference_id',
        'journal_entry_id',
        'reconciled',
        'reconciled_at',
        'created_by',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'debit_amount' => 'decimal:2',
        'credit_amount' => 'decimal:2',
        'reconciled' => 'boolean',
        'reconciled_at' => 'datetime',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (!$transaction->transaction_number) {
                $transaction->transaction_number = static::generateTransactionNumber();
            }
            $transaction->created_by = auth()->id();
        });

        static::created(function ($transaction) {
            // Update account balance
            $transaction->updateAccountBalance();
        });

        static::deleted(function ($transaction) {
            // Reverse account balance update
            $transaction->reverseAccountBalance();
        });
    }

    /**
     * Relationships
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(ChartOfAccount::class, 'account_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function journalEntry(): BelongsTo
    {
        return $this->belongsTo(JournalEntry::class);
    }

    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scopes
     */
    public function scopeDebits($query)
    {
        return $query->where('debit_amount', '>', 0);
    }

    public function scopeCredits($query)
    {
        return $query->where('credit_amount', '>', 0);
    }

    public function scopeReconciled($query)
    {
        return $query->where('reconciled', true);
    }

    public function scopeUnreconciled($query)
    {
        return $query->where('reconciled', false);
    }

    public function scopeByAccount($query, $accountId)
    {
        return $query->where('account_id', $accountId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }

    /**
     * Accessors
     */
    public function getTransactionTypeDisplayAttribute(): string
    {
        $types = [
            'sale' => 'Sale',
            'purchase' => 'Purchase',
            'payment' => 'Payment',
            'receipt' => 'Receipt',
            'adjustment' => 'Adjustment',
            'transfer' => 'Transfer',
            'opening_balance' => 'Opening Balance',
            'closing_balance' => 'Closing Balance',
            'journal' => 'Journal Entry',
        ];

        return $types[$this->transaction_type] ?? ucfirst(str_replace('_', ' ', $this->transaction_type));
    }

    public function getAmountAttribute(): float
    {
        return $this->debit_amount ?: $this->credit_amount;
    }

    public function getTransactionDirectionAttribute(): string
    {
        return $this->debit_amount > 0 ? 'debit' : 'credit';
    }

    public function getFormattedAmountAttribute(): string
    {
        $amount = $this->amount;
        $direction = $this->transaction_direction;

        return '₹' . number_format($amount, 2) . ' ' . ucfirst($direction);
    }

    public function getIsReconciledAttribute(): bool
    {
        return $this->reconciled;
    }

    /**
     * Business Logic Methods
     */
    public static function generateTransactionNumber(): string
    {
        $prefix = 'TXN';
        $date = now()->format('ymd');

        $lastTransaction = static::whereDate('created_at', today())
                                ->orderBy('id', 'desc')
                                ->first();

        $sequence = $lastTransaction ? (int) substr($lastTransaction->transaction_number, -4) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function updateAccountBalance(): void
    {
        if ($this->debit_amount > 0) {
            $this->account->updateBalance($this->debit_amount, 'debit');
        } elseif ($this->credit_amount > 0) {
            $this->account->updateBalance($this->credit_amount, 'credit');
        }
    }

    public function reverseAccountBalance(): void
    {
        if ($this->debit_amount > 0) {
            $this->account->updateBalance($this->debit_amount, 'credit');
        } elseif ($this->credit_amount > 0) {
            $this->account->updateBalance($this->credit_amount, 'debit');
        }
    }

    public function reconcile(): self
    {
        $this->update([
            'reconciled' => true,
            'reconciled_at' => now(),
        ]);

        return $this;
    }

    public function unreconcile(): self
    {
        $this->update([
            'reconciled' => false,
            'reconciled_at' => null,
        ]);

        return $this;
    }

    public static function createTransaction(array $data): self
    {
        return static::create([
            'account_id' => $data['account_id'],
            'transaction_date' => $data['transaction_date'] ?? now()->toDateString(),
            'transaction_type' => $data['transaction_type'],
            'debit_amount' => $data['debit_amount'] ?? 0,
            'credit_amount' => $data['credit_amount'] ?? 0,
            'description' => $data['description'],
            'reference_type' => $data['reference_type'] ?? null,
            'reference_id' => $data['reference_id'] ?? null,
            'journal_entry_id' => $data['journal_entry_id'] ?? null,
        ]);
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'transaction_number' => $this->transaction_number,
            'account_name' => $this->account->account_name,
            'account_code' => $this->account->account_code,
            'transaction_date' => $this->transaction_date->format('Y-m-d'),
            'transaction_type' => $this->transaction_type_display,
            'debit_amount' => $this->debit_amount,
            'credit_amount' => $this->credit_amount,
            'amount' => $this->amount,
            'direction' => $this->transaction_direction,
            'formatted_amount' => $this->formatted_amount,
            'description' => $this->description,
            'reconciled' => $this->reconciled,
            'reconciled_at' => $this->reconciled_at?->format('Y-m-d H:i:s'),
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Static helper methods
     */
    public static function getAccountBalance($accountId, $asOfDate = null): float
    {
        $query = static::where('account_id', $accountId);

        if ($asOfDate) {
            $query->where('transaction_date', '<=', $asOfDate);
        }

        $debits = $query->sum('debit_amount');
        $credits = $query->sum('credit_amount');

        return $debits - $credits;
    }

    public static function getAccountTransactions($accountId, $startDate = null, $endDate = null)
    {
        $query = static::with(['account', 'createdBy'])
                      ->where('account_id', $accountId)
                      ->orderBy('transaction_date', 'desc')
                      ->orderBy('created_at', 'desc');

        if ($startDate) {
            $query->where('transaction_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('transaction_date', '<=', $endDate);
        }

        return $query->get();
    }
}
