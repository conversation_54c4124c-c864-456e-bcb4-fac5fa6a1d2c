<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\RepairService;
use App\Models\LoyaltyPoint;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class AccountController extends Controller
{
    public function __construct()
    {
        // Middleware is applied in routes, but we can also apply it here for specific methods
        // $this->middleware('auth'); // Already applied in routes
    }

    /**
     * Customer dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();
        $customer = $user->getCustomer();

        // If no customer profile exists, create one
        if (!$customer) {
            $customer = $this->createCustomerProfile($user);
        }

        $stats = [
            'total_orders' => $customer->invoices()->count(),
            'pending_orders' => $customer->invoices()->where('status', 'sent')->count(),
            'total_spent' => $customer->invoices()->where('status', 'paid')->sum('total_amount'),
            'loyalty_points' => $customer->total_loyalty_points ?? 0,
        ];

        $recentOrders = $customer->invoices()
                               ->with(['items.product'])
                               ->latest()
                               ->limit(5)
                               ->get();

        $recentRepairs = $customer->repairServices()
                                ->latest()
                                ->limit(3)
                                ->get();

        $loyaltyActivity = $customer->loyaltyPoints()
                                  ->latest()
                                  ->limit(5)
                                  ->get();

        return view('website.account.dashboard', compact(
            'user',
            'customer',
            'stats',
            'recentOrders',
            'recentRepairs',
            'loyaltyActivity'
        ));
    }

    /**
     * Customer profile
     */
    public function profile()
    {
        $customer = Auth::user() /* Using default web guard for now */;
        return view('website.account.profile', compact('customer'));
    }

    /**
     * Update customer profile
     */
    public function updateProfile(Request $request)
    {
        $customer = Auth::user() /* Using default web guard for now */;

        $validated = $request->validate([
            'first_name' => 'required|string|max:50',
            'last_name' => 'nullable|string|max:50',
            'email' => 'required|email|max:100|unique:customers,email,' . $customer->id,
            'phone' => 'required|string|max:20',
            'date_of_birth' => 'nullable|date',
            'anniversary_date' => 'nullable|date',
            'billing_address' => 'nullable|string|max:500',
            'shipping_address' => 'nullable|string|max:500',
        ]);

        try {
            $customer->update($validated);

            return back()->with('success', 'Profile updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Failed to update profile. Please try again.');
        }
    }

    /**
     * Change password
     */
    public function changePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => 'required',
            'new_password' => 'required|min:8|confirmed',
        ]);

        $customer = Auth::user() /* Using default web guard for now */;

        if (!Hash::check($validated['current_password'], $customer->password)) {
            return back()->with('error', 'Current password is incorrect.');
        }

        try {
            $customer->update([
                'password' => Hash::make($validated['new_password']),
            ]);

            return back()->with('success', 'Password changed successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to change password. Please try again.');
        }
    }

    /**
     * Order history
     */
    public function orders(Request $request)
    {
        $user = Auth::user();
        $customer = $user->getCustomer();

        // If no customer profile exists, create one
        if (!$customer) {
            $customer = $this->createCustomerProfile($user);
        }

        $query = $customer->invoices()->with(['items.product']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('invoice_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('invoice_date', '<=', $request->date_to);
        }

        $orders = $query->latest('invoice_date')->paginate(10);

        return view('website.account.orders', compact('orders'));
    }

    /**
     * Order details
     */
    public function orderDetails(Invoice $invoice)
    {
        $customer = Auth::user() /* Using default web guard for now */;

        if ($invoice->customer_id !== $customer->id) {
            abort(404);
        }

        $invoice->load(['items.product', 'payments']);

        return view('website.account.order-details', compact('invoice'));
    }

    /**
     * Repair services
     */
    public function repairs(Request $request)
    {
        $customer = Auth::user() /* Using default web guard for now */;

        $query = $customer->repairServices();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $repairs = $query->latest('created_at')->paginate(10);

        return view('website.account.repairs', compact('repairs'));
    }

    /**
     * Repair service details
     */
    public function repairDetails(RepairService $repair)
    {
        $customer = Auth::user() /* Using default web guard for now */;

        if ($repair->customer_id !== $customer->id) {
            abort(404);
        }

        return view('website.account.repair-details', compact('repair'));
    }

    /**
     * Loyalty points
     */
    public function loyalty()
    {
        $customer = Auth::user() /* Using default web guard for now */;

        $loyaltyPoints = $customer->loyaltyPoints()
                                ->latest()
                                ->paginate(20);

        $summary = [
            'total_points' => $customer->total_loyalty_points,
            'total_earned' => LoyaltyPoint::getCustomerTotalEarned($customer->id),
            'total_redeemed' => LoyaltyPoint::getCustomerTotalRedeemed($customer->id),
            'expiring_soon' => $customer->loyaltyPoints()->expiringIn(30)->sum('points'),
        ];

        return view('website.account.loyalty', compact('loyaltyPoints', 'summary'));
    }

    /**
     * Wishlist
     */
    public function wishlist()
    {
        $customer = Auth::user() /* Using default web guard for now */;

        // Get wishlist from session or database
        $wishlistIds = session()->get('wishlist', []);
        $wishlistProducts = collect();

        if (!empty($wishlistIds)) {
            $wishlistProducts = Product::with(['category', 'images'])
                                     ->where('is_active', true)
                                     ->whereIn('id', $wishlistIds)
                                     ->get();
        }

        return view('website.account.wishlist', compact('wishlistProducts'));
    }

    /**
     * Add to wishlist
     */
    public function addToWishlist(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $wishlist = session()->get('wishlist', []);

        if (!in_array($validated['product_id'], $wishlist)) {
            $wishlist[] = $validated['product_id'];
            session()->put('wishlist', $wishlist);
        }

        return response()->json([
            'success' => true,
            'message' => 'Product added to wishlist.',
            'wishlist_count' => count($wishlist),
        ]);
    }

    /**
     * Remove from wishlist
     */
    public function removeFromWishlist(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $wishlist = session()->get('wishlist', []);
        $wishlist = array_filter($wishlist, function ($id) use ($validated) {
            return $id != $validated['product_id'];
        });

        session()->put('wishlist', array_values($wishlist));

        return response()->json([
            'success' => true,
            'message' => 'Product removed from wishlist.',
            'wishlist_count' => count($wishlist),
        ]);
    }

    /**
     * Create customer profile for user
     */
    private function createCustomerProfile(User $user): Customer
    {
        $nameParts = explode(' ', $user->name, 2);
        $firstName = $nameParts[0];
        $lastName = $nameParts[1] ?? '';

        return Customer::create([
            'customer_code' => $this->generateCustomerCode(),
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => $user->email,
            'phone' => $user->phone,
            'customer_type' => 'individual',
            'customer_segment' => 'regular',
            'is_active' => true,
            'kyc_status' => 'pending',
        ]);
    }

    /**
     * Generate unique customer code
     */
    private function generateCustomerCode(): string
    {
        $prefix = 'WEB';
        $year = date('Y');

        // Get the last customer code for this year
        $lastCustomer = Customer::where('customer_code', 'like', $prefix . $year . '%')
                               ->orderBy('customer_code', 'desc')
                               ->first();

        if ($lastCustomer) {
            $lastNumber = (int) substr($lastCustomer->customer_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
