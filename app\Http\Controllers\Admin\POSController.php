<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\POSSession;
use App\Models\POSTransaction;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Location;
use App\Models\InventoryItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class POSController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:access_pos')->only(['index', 'terminal']);
        $this->middleware('permission:manage_pos_sessions')->only(['openSession', 'closeSession']);
        $this->middleware('permission:process_sales')->only(['processTransaction', 'addToCart']);
    }

    /**
     * Display POS dashboard
     */
    public function index()
    {
        $activeSession = POSSession::where('user_id', auth()->id())
                                  ->where('status', 'active')
                                  ->first();

        $locations = Location::orderBy('name')->get(['id', 'name']);
        $todayStats = $this->getTodayStats();

        return view('admin.pos.index', compact('activeSession', 'locations', 'todayStats'));
    }

    /**
     * Display POS terminal interface
     */
    public function terminal()
    {
        $activeSession = POSSession::where('user_id', auth()->id())
                                  ->where('status', 'active')
                                  ->first();

        if (!$activeSession) {
            return redirect()->route('admin.pos.index')
                           ->with('error', 'Please open a POS session first.');
        }

        $customers = Customer::orderBy('name')->get(['id', 'name', 'phone']);
        $recentTransactions = POSTransaction::where('pos_session_id', $activeSession->id)
                                          ->with(['customer', 'items.product'])
                                          ->latest()
                                          ->limit(10)
                                          ->get();

        return view('admin.pos.terminal', compact('activeSession', 'customers', 'recentTransactions'));
    }

    /**
     * Open POS session
     */
    public function openSession(Request $request)
    {
        $validated = $request->validate([
            'location_id' => 'required|exists:locations,id',
            'terminal_id' => 'required|string|max:50',
            'opening_cash' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $session = POSSession::openSession(
                auth()->id(),
                $validated['location_id'],
                $validated['terminal_id'],
                $validated['opening_cash'],
                $validated['notes'] ?? null
            );

            return redirect()->route('admin.pos.terminal')
                           ->with('success', 'POS session opened successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to open POS session: ' . $e->getMessage());
        }
    }

    /**
     * Close POS session
     */
    public function closeSession(Request $request)
    {
        $validated = $request->validate([
            'closing_cash' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $activeSession = POSSession::where('user_id', auth()->id())
                                      ->where('status', 'active')
                                      ->firstOrFail();

            $activeSession->closeSession(
                $validated['closing_cash'],
                $validated['notes'] ?? null
            );

            return redirect()->route('admin.pos.index')
                           ->with('success', 'POS session closed successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to close POS session: ' . $e->getMessage());
        }
    }

    /**
     * Search products for POS
     */
    public function searchProducts(Request $request)
    {
        $query = $request->get('q', '');
        $locationId = $request->get('location_id');

        if (strlen($query) < 2) {
            return response()->json(['products' => []]);
        }

        $products = Product::with(['category', 'metal'])
                          ->where(function ($q) use ($query) {
                              $q->where('name', 'like', "%{$query}%")
                                ->orWhere('product_code', 'like', "%{$query}%")
                                ->orWhere('barcode', $query);
                          })
                          ->where('is_active', true)
                          ->limit(20)
                          ->get();

        $productsWithStock = $products->map(function ($product) use ($locationId) {
            $availableStock = InventoryItem::where('product_id', $product->id)
                                         ->where('location_id', $locationId)
                                         ->where('status', 'available')
                                         ->count();

            return [
                'id' => $product->id,
                'name' => $product->name,
                'product_code' => $product->product_code,
                'barcode' => $product->barcode,
                'category' => $product->category->name,
                'metal' => $product->metal->name,
                'selling_price' => $product->selling_price,
                'available_stock' => $availableStock,
                'image_url' => $product->image_url,
            ];
        });

        return response()->json(['products' => $productsWithStock]);
    }

    /**
     * Get product by barcode
     */
    public function getProductByBarcode(Request $request)
    {
        $barcode = $request->get('barcode');
        $locationId = $request->get('location_id');

        $product = Product::with(['category', 'metal'])
                         ->where('barcode', $barcode)
                         ->where('is_active', true)
                         ->first();

        if (!$product) {
            return response()->json(['error' => 'Product not found'], 404);
        }

        $availableStock = InventoryItem::where('product_id', $product->id)
                                     ->where('location_id', $locationId)
                                     ->where('status', 'available')
                                     ->count();

        return response()->json([
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'product_code' => $product->product_code,
                'barcode' => $product->barcode,
                'category' => $product->category->name,
                'metal' => $product->metal->name,
                'selling_price' => $product->selling_price,
                'available_stock' => $availableStock,
                'image_url' => $product->image_url,
            ]
        ]);
    }

    /**
     * Process POS transaction
     */
    public function processTransaction(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.001',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'required|in:cash,card,upi,bank_transfer,cheque,credit,mixed',
            'cash_received' => 'nullable|numeric|min:0',
            'payment_reference' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $activeSession = POSSession::where('user_id', auth()->id())
                                      ->where('status', 'active')
                                      ->firstOrFail();

            // Create transaction
            $transaction = POSTransaction::create([
                'pos_session_id' => $activeSession->id,
                'customer_id' => $validated['customer_id'],
                'discount_amount' => $validated['discount_amount'] ?? 0,
                'notes' => $validated['notes'],
                'status' => 'pending',
            ]);

            // Add items to transaction
            foreach ($validated['items'] as $itemData) {
                $transaction->addItem(
                    $itemData['product_id'],
                    $itemData['quantity'],
                    $itemData['unit_price'],
                    $itemData['discount'] ?? 0
                );
            }

            // Calculate totals
            $transaction->calculateTotals();

            // Process payment
            $transaction->processPayment(
                $validated['payment_method'],
                $validated['cash_received'] ?? null,
                $validated['payment_reference'] ?? null
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'transaction' => $transaction->getSummary(),
                'receipt' => $transaction->generateReceipt(),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get transaction details
     */
    public function getTransaction(POSTransaction $transaction)
    {
        $transaction->load(['customer', 'items.product', 'createdBy']);

        return response()->json([
            'success' => true,
            'transaction' => $transaction->getSummary(),
            'receipt' => $transaction->generateReceipt(),
        ]);
    }

    /**
     * Cancel transaction
     */
    public function cancelTransaction(Request $request, POSTransaction $transaction)
    {
        $validated = $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $transaction->cancel($validated['reason']);

            return response()->json([
                'success' => true,
                'message' => 'Transaction cancelled successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Refund transaction
     */
    public function refundTransaction(Request $request, POSTransaction $transaction)
    {
        $validated = $request->validate([
            'amount' => 'nullable|numeric|min:0',
            'reason' => 'required|string|max:500',
        ]);

        try {
            $transaction->refund(
                $validated['amount'] ?? null,
                $validated['reason']
            );

            return response()->json([
                'success' => true,
                'message' => 'Transaction refunded successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Add cash movement
     */
    public function addCashMovement(Request $request)
    {
        $validated = $request->validate([
            'movement_type' => 'required|in:cash_in,cash_out,float_adjustment',
            'amount' => 'required|numeric|min:0',
            'reason' => 'required|string|max:255',
            'reference' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $activeSession = POSSession::where('user_id', auth()->id())
                                      ->where('status', 'active')
                                      ->firstOrFail();

            $movement = $activeSession->addCashMovement(
                $validated['movement_type'],
                $validated['amount'],
                $validated['reason'],
                $validated['reference'] ?? null
            );

            return response()->json([
                'success' => true,
                'movement' => $movement->getSummary(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get session summary
     */
    public function getSessionSummary()
    {
        try {
            $activeSession = POSSession::where('user_id', auth()->id())
                                      ->where('status', 'active')
                                      ->firstOrFail();

            return response()->json([
                'success' => true,
                'summary' => $activeSession->getSessionSummary(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'No active session found.',
            ], 404);
        }
    }

    /**
     * Print receipt
     */
    public function printReceipt(POSTransaction $transaction)
    {
        $transaction->load(['customer', 'items.product', 'createdBy', 'posSession.location']);

        return view('admin.pos.receipt', compact('transaction'));
    }

    /**
     * Get today's statistics
     */
    protected function getTodayStats()
    {
        $today = today();

        return [
            'total_sales' => POSTransaction::whereDate('created_at', $today)
                                         ->where('status', 'completed')
                                         ->sum('total_amount'),
            'total_transactions' => POSTransaction::whereDate('created_at', $today)
                                                ->where('status', 'completed')
                                                ->count(),
            'cash_sales' => POSTransaction::whereDate('created_at', $today)
                                        ->where('status', 'completed')
                                        ->where('payment_method', 'cash')
                                        ->sum('total_amount'),
            'card_sales' => POSTransaction::whereDate('created_at', $today)
                                        ->where('status', 'completed')
                                        ->where('payment_method', 'card')
                                        ->sum('total_amount'),
            'active_sessions' => POSSession::where('status', 'active')->count(),
        ];
    }

    /**
     * Get POS statistics
     */
    public function getStatistics(Request $request)
    {
        $days = $request->get('days', 7);
        $startDate = now()->subDays($days);

        $stats = [
            'total_sales' => POSTransaction::where('created_at', '>=', $startDate)
                                         ->where('status', 'completed')
                                         ->sum('total_amount'),
            'total_transactions' => POSTransaction::where('created_at', '>=', $startDate)
                                                ->where('status', 'completed')
                                                ->count(),
            'average_transaction' => POSTransaction::where('created_at', '>=', $startDate)
                                                 ->where('status', 'completed')
                                                 ->avg('total_amount'),
            'sales_by_payment_method' => POSTransaction::where('created_at', '>=', $startDate)
                                                     ->where('status', 'completed')
                                                     ->groupBy('payment_method')
                                                     ->selectRaw('payment_method, sum(total_amount) as total')
                                                     ->pluck('total', 'payment_method'),
            'hourly_sales' => POSTransaction::where('created_at', '>=', $startDate)
                                          ->where('status', 'completed')
                                          ->selectRaw('HOUR(created_at) as hour, sum(total_amount) as total')
                                          ->groupBy('hour')
                                          ->orderBy('hour')
                                          ->pluck('total', 'hour'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}
