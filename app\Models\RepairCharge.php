<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairCharge extends Model
{
    use HasFactory;

    protected $fillable = [
        'repair_service_id',
        'description',
        'amount',
        'charge_type',
        'quantity',
        'unit_price',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'quantity' => 'decimal:3',
        'unit_price' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($charge) {
            $charge->created_by = auth()->id();
        });

        static::saving(function ($charge) {
            if ($charge->quantity && $charge->unit_price) {
                $charge->amount = $charge->quantity * $charge->unit_price;
            }
        });
    }

    /**
     * Relationships
     */
    public function repairService(): BelongsTo
    {
        return $this->belongsTo(RepairService::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scopes
     */
    public function scopeByType($query, $type)
    {
        return $query->where('charge_type', $type);
    }

    public function scopeLabor($query)
    {
        return $query->where('charge_type', 'labor');
    }

    public function scopeMaterial($query)
    {
        return $query->where('charge_type', 'material');
    }

    public function scopeOther($query)
    {
        return $query->where('charge_type', 'other');
    }

    /**
     * Accessors
     */
    public function getChargeTypeDisplayAttribute()
    {
        $types = [
            'labor' => 'Labor',
            'material' => 'Material',
            'parts' => 'Parts',
            'other' => 'Other',
        ];

        return $types[$this->charge_type] ?? ucfirst($this->charge_type);
    }

    public function getChargeTypeColorAttribute()
    {
        $colors = [
            'labor' => 'blue',
            'material' => 'green',
            'parts' => 'orange',
            'other' => 'gray',
        ];

        return $colors[$this->charge_type] ?? 'gray';
    }

    /**
     * Business Logic Methods
     */
    public function updateAmount($amount, $notes = null)
    {
        $this->update([
            'amount' => $amount,
            'notes' => $notes ? ($this->notes . "\n\n" . $notes) : $this->notes,
        ]);

        return $this;
    }

    public function updateQuantityAndPrice($quantity, $unitPrice, $notes = null)
    {
        $this->update([
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'amount' => $quantity * $unitPrice,
            'notes' => $notes ? ($this->notes . "\n\n" . $notes) : $this->notes,
        ]);

        return $this;
    }

    public function getSummary()
    {
        return [
            'id' => $this->id,
            'description' => $this->description,
            'charge_type' => $this->charge_type_display,
            'quantity' => $this->quantity,
            'unit_price' => $this->unit_price,
            'amount' => $this->amount,
            'notes' => $this->notes,
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
