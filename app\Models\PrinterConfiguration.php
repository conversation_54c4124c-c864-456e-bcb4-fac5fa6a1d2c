<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PrinterConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'connection_type',
        'connection_string',
        'port',
        'device_path',
        'connection_options',
        'paper_width_mm',
        'characters_per_line',
        'supports_graphics',
        'supports_barcode',
        'supports_qr_code',
        'supports_cut',
        'supports_drawer',
        'default_settings',
        'print_density',
        'print_speed',
        'auto_cut',
        'cut_lines',
        'print_types',
        'is_default_invoice',
        'is_default_receipt',
        'is_default_label',
        'is_default_tag',
        'location_id',
        'allowed_users',
        'allowed_roles',
        'is_active',
        'is_online',
        'last_used_at',
        'last_checked_at',
        'last_error',
        'total_prints',
        'successful_prints',
        'failed_prints',
        'last_maintenance_date',
        'next_maintenance_due',
        'maintenance_notes',
        'description',
        'custom_settings',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'connection_options' => 'array',
        'supports_graphics' => 'boolean',
        'supports_barcode' => 'boolean',
        'supports_qr_code' => 'boolean',
        'supports_cut' => 'boolean',
        'supports_drawer' => 'boolean',
        'default_settings' => 'array',
        'auto_cut' => 'boolean',
        'print_types' => 'array',
        'is_default_invoice' => 'boolean',
        'is_default_receipt' => 'boolean',
        'is_default_label' => 'boolean',
        'is_default_tag' => 'boolean',
        'allowed_users' => 'array',
        'allowed_roles' => 'array',
        'is_active' => 'boolean',
        'is_online' => 'boolean',
        'last_used_at' => 'datetime',
        'last_checked_at' => 'datetime',
        'last_maintenance_date' => 'date',
        'next_maintenance_due' => 'date',
        'custom_settings' => 'array',
    ];

    /**
     * Relationships
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function printJobs(): HasMany
    {
        return $this->hasMany(PrintJob::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOnline($query)
    {
        return $query->where('is_online', true);
    }

    public function scopeForPrintType($query, $printType)
    {
        $defaultField = "is_default_{$printType}";
        return $query->where($defaultField, true);
    }

    public function scopeForLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Accessors
     */
    public function getConnectionTypeDisplayAttribute()
    {
        $types = [
            'usb' => 'USB',
            'network' => 'Network (TCP/IP)',
            'bluetooth' => 'Bluetooth',
            'serial' => 'Serial Port',
            'file' => 'File Output',
        ];

        return $types[$this->connection_type] ?? ucfirst($this->connection_type);
    }

    public function getTypeDisplayAttribute()
    {
        return ucfirst($this->type);
    }

    public function getStatusDisplayAttribute()
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        return $this->is_online ? 'Online' : 'Offline';
    }

    public function getStatusColorAttribute()
    {
        if (!$this->is_active) {
            return 'gray';
        }

        return $this->is_online ? 'green' : 'red';
    }

    public function getSuccessRateAttribute()
    {
        if ($this->total_prints == 0) {
            return 0;
        }

        return round(($this->successful_prints / $this->total_prints) * 100, 2);
    }

    public function getFailureRateAttribute()
    {
        if ($this->total_prints == 0) {
            return 0;
        }

        return round(($this->failed_prints / $this->total_prints) * 100, 2);
    }

    public function getMaintenanceStatusAttribute()
    {
        if (!$this->next_maintenance_due) {
            return 'No schedule';
        }

        if ($this->next_maintenance_due < now()) {
            return 'Overdue';
        }

        if ($this->next_maintenance_due <= now()->addDays(7)) {
            return 'Due soon';
        }

        return 'Up to date';
    }

    public function getMaintenanceStatusColorAttribute()
    {
        $status = $this->maintenance_status;

        switch ($status) {
            case 'Overdue':
                return 'red';
            case 'Due soon':
                return 'yellow';
            case 'Up to date':
                return 'green';
            default:
                return 'gray';
        }
    }

    public function getPrintTypesDisplayAttribute()
    {
        if (!$this->print_types) {
            return 'All types';
        }

        $types = [
            'invoice' => 'Invoices',
            'receipt' => 'Receipts',
            'estimate' => 'Estimates',
            'tag' => 'Tags',
            'label' => 'Labels',
            'barcode' => 'Barcodes',
            'qr_code' => 'QR Codes',
        ];

        $supportedTypes = array_intersect_key($types, array_flip($this->print_types));
        return implode(', ', $supportedTypes);
    }

    public function getDefaultForTypesAttribute()
    {
        $defaults = [];

        if ($this->is_default_invoice) $defaults[] = 'Invoice';
        if ($this->is_default_receipt) $defaults[] = 'Receipt';
        if ($this->is_default_label) $defaults[] = 'Label';
        if ($this->is_default_tag) $defaults[] = 'Tag';

        return $defaults;
    }

    /**
     * Check if user can use this printer
     */
    public function canBeUsedBy($userId)
    {
        // If no restrictions, anyone can use
        if (empty($this->allowed_users) && empty($this->allowed_roles)) {
            return true;
        }

        // Check user ID
        if ($this->allowed_users && in_array($userId, $this->allowed_users)) {
            return true;
        }

        // Check user roles
        if ($this->allowed_roles) {
            $user = User::find($userId);
            if ($user && $user->hasAnyRole($this->allowed_roles)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if printer supports a specific print type
     */
    public function supportsPrintType($printType)
    {
        if (!$this->print_types) {
            return true; // Supports all types if not specified
        }

        return in_array($printType, $this->print_types);
    }

    /**
     * Get printer statistics
     */
    public function getStatistics($days = 30)
    {
        $jobs = $this->printJobs()
                    ->where('created_at', '>=', now()->subDays($days))
                    ->get();

        return [
            'total_jobs' => $jobs->count(),
            'completed_jobs' => $jobs->where('status', 'completed')->count(),
            'failed_jobs' => $jobs->where('status', 'failed')->count(),
            'pending_jobs' => $jobs->where('status', 'pending')->count(),
            'success_rate' => $jobs->count() > 0 ?
                round(($jobs->where('status', 'completed')->count() / $jobs->count()) * 100, 2) : 0,
            'average_processing_time' => $jobs->where('status', 'completed')
                                             ->avg('processing_time_ms'),
            'jobs_by_type' => $jobs->groupBy('print_type')->map->count(),
            'total_prints_lifetime' => $this->total_prints,
            'success_rate_lifetime' => $this->success_rate,
            'last_used' => $this->last_used_at,
        ];
    }

    /**
     * Update printer status
     */
    public function updateStatus($isOnline, $error = null)
    {
        $this->update([
            'is_online' => $isOnline,
            'last_checked_at' => now(),
            'last_error' => $error,
        ]);
    }

    /**
     * Record print job completion
     */
    public function recordPrintJob($success = true)
    {
        $this->increment('total_prints');

        if ($success) {
            $this->increment('successful_prints');
            $this->update(['last_used_at' => now()]);
        } else {
            $this->increment('failed_prints');
        }
    }

    /**
     * Schedule maintenance
     */
    public function scheduleMaintenance($date, $notes = null)
    {
        $this->update([
            'next_maintenance_due' => $date,
            'maintenance_notes' => $notes,
        ]);
    }

    /**
     * Complete maintenance
     */
    public function completeMaintenance($notes = null)
    {
        $this->update([
            'last_maintenance_date' => now(),
            'next_maintenance_due' => null,
            'maintenance_notes' => $notes,
        ]);
    }

    /**
     * Get connection string for display
     */
    public function getConnectionDisplayAttribute()
    {
        switch ($this->connection_type) {
            case 'network':
                return $this->connection_string . ':' . ($this->port ?? 9100);
            case 'usb':
            case 'serial':
                return $this->device_path ?: $this->connection_string;
            case 'file':
                return $this->device_path ?: 'File output';
            default:
                return $this->connection_string;
        }
    }

    /**
     * Test printer connection
     */
    public function testConnection()
    {
        try {
            // This would be implemented in the printing service
            app(ThermalPrintingService::class)->testPrinter($this->id);
            return true;
        } catch (\Exception $e) {
            $this->updateStatus(false, $e->getMessage());
            return false;
        }
    }

    /**
     * Get printer configuration summary
     */
    public function getSummary()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type_display,
            'connection' => $this->connection_display,
            'status' => $this->status_display,
            'paper_width' => $this->paper_width_mm . 'mm',
            'supports' => [
                'graphics' => $this->supports_graphics,
                'barcode' => $this->supports_barcode,
                'qr_code' => $this->supports_qr_code,
                'auto_cut' => $this->supports_cut,
                'cash_drawer' => $this->supports_drawer,
            ],
            'defaults' => $this->default_for_types,
            'statistics' => [
                'total_prints' => $this->total_prints,
                'success_rate' => $this->success_rate . '%',
                'last_used' => $this->last_used_at?->diffForHumans(),
            ],
            'maintenance' => [
                'status' => $this->maintenance_status,
                'last_date' => $this->last_maintenance_date?->format('Y-m-d'),
                'next_due' => $this->next_maintenance_due?->format('Y-m-d'),
            ],
        ];
    }
}
