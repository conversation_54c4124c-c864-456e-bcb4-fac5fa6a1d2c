<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ChartOfAccount;
use App\Models\AccountTransaction;
use App\Models\JournalEntry;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use App\Models\Invoice;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FinancialController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_financial_reports')->only(['index', 'reports']);
        $this->middleware('permission:manage_chart_of_accounts')->only(['chartOfAccounts', 'createAccount']);
        $this->middleware('permission:manage_journal_entries')->only(['journalEntries', 'createJournalEntry']);
        $this->middleware('permission:view_financial_analytics')->only(['analytics', 'getFinancialAnalytics']);
    }

    /**
     * Financial Dashboard
     */
    public function index()
    {
        $stats = $this->getFinancialStats();
        $recentTransactions = $this->getRecentTransactions();
        $cashFlow = $this->getCashFlowData();
        $profitLoss = $this->getProfitLossData();

        return view('admin.financial.index', compact(
            'stats',
            'recentTransactions',
            'cashFlow',
            'profitLoss'
        ));
    }

    /**
     * Chart of Accounts Management
     */
    public function chartOfAccounts(Request $request)
    {
        $query = ChartOfAccount::with(['parentAccount', 'childAccounts', 'createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('account_name', 'like', "%{$search}%")
                  ->orWhere('account_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by account type
        if ($request->filled('account_type')) {
            $query->where('account_type', $request->account_type);
        }

        // Filter by account category
        if ($request->filled('account_category')) {
            $query->where('account_category', $request->account_category);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Sort
        $sortBy = $request->get('sort_by', 'account_code');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $accounts = $query->paginate(20);

        // Get account type summary
        $accountSummary = $this->getAccountTypeSummary();

        return view('admin.financial.chart-of-accounts', compact('accounts', 'accountSummary'));
    }

    /**
     * Create Chart of Account
     */
    public function createAccount(Request $request)
    {
        $validated = $request->validate([
            'account_name' => 'required|string|max:255|unique:chart_of_accounts',
            'account_type' => 'required|in:asset,liability,equity,revenue,expense',
            'account_category' => 'required|string',
            'parent_account_id' => 'nullable|exists:chart_of_accounts,id',
            'description' => 'nullable|string',
            'opening_balance' => 'nullable|numeric',
            'balance_type' => 'required|in:debit,credit',
            'tax_applicable' => 'boolean',
        ]);

        try {
            $account = ChartOfAccount::create([
                'account_name' => $validated['account_name'],
                'account_type' => $validated['account_type'],
                'account_category' => $validated['account_category'],
                'parent_account_id' => $validated['parent_account_id'],
                'description' => $validated['description'],
                'opening_balance' => $validated['opening_balance'] ?? 0,
                'current_balance' => $validated['opening_balance'] ?? 0,
                'balance_type' => $validated['balance_type'],
                'tax_applicable' => $validated['tax_applicable'] ?? false,
                'is_active' => true,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Account created successfully',
                'account' => $account->getSummary(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Journal Entries Management
     */
    public function journalEntries(Request $request)
    {
        $query = JournalEntry::with(['createdBy', 'postedBy', 'transactions.account']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('journal_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('entry_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('entry_date', '<=', $request->date_to);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'entry_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $journalEntries = $query->paginate(20);

        return view('admin.financial.journal-entries', compact('journalEntries'));
    }

    /**
     * Create Journal Entry
     */
    public function createJournalEntry(Request $request)
    {
        $validated = $request->validate([
            'entry_date' => 'required|date',
            'description' => 'required|string|max:255',
            'transactions' => 'required|array|min:2',
            'transactions.*.account_id' => 'required|exists:chart_of_accounts,id',
            'transactions.*.debit_amount' => 'nullable|numeric|min:0',
            'transactions.*.credit_amount' => 'nullable|numeric|min:0',
            'transactions.*.description' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Validate that debits equal credits
            $totalDebits = collect($validated['transactions'])->sum('debit_amount');
            $totalCredits = collect($validated['transactions'])->sum('credit_amount');

            if (abs($totalDebits - $totalCredits) > 0.01) {
                throw new \Exception('Total debits must equal total credits');
            }

            // Create journal entry
            $journalEntry = JournalEntry::create([
                'entry_date' => $validated['entry_date'],
                'description' => $validated['description'],
                'status' => 'draft',
            ]);

            // Add transactions
            foreach ($validated['transactions'] as $transactionData) {
                if (($transactionData['debit_amount'] ?? 0) > 0 || ($transactionData['credit_amount'] ?? 0) > 0) {
                    $journalEntry->addTransaction(
                        $transactionData['account_id'],
                        $transactionData['debit_amount'] ?? 0,
                        $transactionData['credit_amount'] ?? 0,
                        $transactionData['description'] ?? null
                    );
                }
            }

            // Post the journal entry
            $journalEntry->post();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Journal entry created successfully',
                'journal_entry' => $journalEntry->getSummary(),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Financial Reports
     */
    public function reports(Request $request)
    {
        $reportType = $request->get('type', 'profit_loss');
        $startDate = $request->get('start_date', now()->startOfMonth()->toDateString());
        $endDate = $request->get('end_date', now()->endOfMonth()->toDateString());

        $reportData = match($reportType) {
            'profit_loss' => $this->generateProfitLossReport($startDate, $endDate),
            'balance_sheet' => $this->generateBalanceSheetReport($endDate),
            'cash_flow' => $this->generateCashFlowReport($startDate, $endDate),
            'trial_balance' => $this->generateTrialBalanceReport($endDate),
            default => $this->generateProfitLossReport($startDate, $endDate),
        };

        return view('admin.financial.reports', compact('reportType', 'reportData', 'startDate', 'endDate'));
    }

    /**
     * Financial Analytics
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '12_months');
        $analyticsData = $this->getFinancialAnalytics($period);

        return view('admin.financial.analytics', compact('analyticsData', 'period'));
    }

    /**
     * Get Financial Analytics Data
     */
    public function getFinancialAnalytics(Request $request)
    {
        $period = $request->get('period', '12_months');

        $analytics = [
            'revenue_trend' => $this->getRevenueTrend($period),
            'expense_trend' => $this->getExpenseTrend($period),
            'profit_margin_trend' => $this->getProfitMarginTrend($period),
            'cash_flow_trend' => $this->getCashFlowTrend($period),
            'expense_breakdown' => $this->getExpenseBreakdown($period),
            'revenue_breakdown' => $this->getRevenueBreakdown($period),
            'financial_ratios' => $this->getFinancialRatios(),
            'budget_vs_actual' => $this->getBudgetVsActual($period),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Helper Methods
     */
    protected function getFinancialStats()
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        return [
            'total_revenue_this_month' => $this->getRevenueForPeriod($currentMonth, now()),
            'total_revenue_last_month' => $this->getRevenueForPeriod($lastMonth, $lastMonth->copy()->endOfMonth()),
            'total_expenses_this_month' => $this->getExpensesForPeriod($currentMonth, now()),
            'total_expenses_last_month' => $this->getExpensesForPeriod($lastMonth, $lastMonth->copy()->endOfMonth()),
            'net_profit_this_month' => $this->getNetProfitForPeriod($currentMonth, now()),
            'net_profit_last_month' => $this->getNetProfitForPeriod($lastMonth, $lastMonth->copy()->endOfMonth()),
            'cash_balance' => $this->getCashBalance(),
            'accounts_receivable' => $this->getAccountsReceivable(),
            'accounts_payable' => $this->getAccountsPayable(),
            'pending_expenses' => Expense::pending()->count(),
        ];
    }

    protected function getRecentTransactions()
    {
        return AccountTransaction::with(['account', 'createdBy'])
                                ->latest()
                                ->limit(10)
                                ->get();
    }

    protected function getCashFlowData()
    {
        $months = collect();
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $months->push([
                'month' => $date->format('M Y'),
                'cash_in' => $this->getCashInForMonth($date),
                'cash_out' => $this->getCashOutForMonth($date),
            ]);
        }
        return $months;
    }

    protected function getProfitLossData()
    {
        $months = collect();
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $startDate = $date->copy()->startOfMonth();
            $endDate = $date->copy()->endOfMonth();

            $revenue = $this->getRevenueForPeriod($startDate, $endDate);
            $expenses = $this->getExpensesForPeriod($startDate, $endDate);

            $months->push([
                'month' => $date->format('M Y'),
                'revenue' => $revenue,
                'expenses' => $expenses,
                'profit' => $revenue - $expenses,
            ]);
        }
        return $months;
    }

    protected function getAccountTypeSummary()
    {
        return ChartOfAccount::selectRaw('account_type, COUNT(*) as count, SUM(current_balance) as total_balance')
                           ->groupBy('account_type')
                           ->get()
                           ->keyBy('account_type');
    }

    protected function getRevenueForPeriod($startDate, $endDate)
    {
        return AccountTransaction::whereHas('account', function ($query) {
                    $query->where('account_type', 'revenue');
                })
                ->whereBetween('transaction_date', [$startDate, $endDate])
                ->sum('credit_amount');
    }

    protected function getExpensesForPeriod($startDate, $endDate)
    {
        return AccountTransaction::whereHas('account', function ($query) {
                    $query->where('account_type', 'expense');
                })
                ->whereBetween('transaction_date', [$startDate, $endDate])
                ->sum('debit_amount');
    }

    protected function getNetProfitForPeriod($startDate, $endDate)
    {
        return $this->getRevenueForPeriod($startDate, $endDate) - $this->getExpensesForPeriod($startDate, $endDate);
    }

    protected function getCashBalance()
    {
        $cashAccount = ChartOfAccount::where('account_name', 'Cash')->first();
        return $cashAccount ? $cashAccount->current_balance : 0;
    }

    protected function getAccountsReceivable()
    {
        $arAccount = ChartOfAccount::where('account_name', 'Accounts Receivable')->first();
        return $arAccount ? $arAccount->current_balance : 0;
    }

    protected function getAccountsPayable()
    {
        $apAccount = ChartOfAccount::where('account_name', 'Accounts Payable')->first();
        return $apAccount ? $apAccount->current_balance : 0;
    }

    protected function getCashInForMonth($date)
    {
        $cashAccount = ChartOfAccount::where('account_name', 'Cash')->first();
        if (!$cashAccount) return 0;

        return AccountTransaction::where('account_id', $cashAccount->id)
                                ->whereMonth('transaction_date', $date->month)
                                ->whereYear('transaction_date', $date->year)
                                ->sum('debit_amount');
    }

    protected function getCashOutForMonth($date)
    {
        $cashAccount = ChartOfAccount::where('account_name', 'Cash')->first();
        if (!$cashAccount) return 0;

        return AccountTransaction::where('account_id', $cashAccount->id)
                                ->whereMonth('transaction_date', $date->month)
                                ->whereYear('transaction_date', $date->year)
                                ->sum('credit_amount');
    }

    protected function generateProfitLossReport($startDate, $endDate)
    {
        $revenue = ChartOfAccount::where('account_type', 'revenue')
                                ->with(['transactions' => function ($query) use ($startDate, $endDate) {
                                    $query->whereBetween('transaction_date', [$startDate, $endDate]);
                                }])
                                ->get();

        $expenses = ChartOfAccount::where('account_type', 'expense')
                                 ->with(['transactions' => function ($query) use ($startDate, $endDate) {
                                     $query->whereBetween('transaction_date', [$startDate, $endDate]);
                                 }])
                                 ->get();

        $totalRevenue = $revenue->sum(function ($account) {
            return $account->transactions->sum('credit_amount');
        });

        $totalExpenses = $expenses->sum(function ($account) {
            return $account->transactions->sum('debit_amount');
        });

        return [
            'revenue_accounts' => $revenue,
            'expense_accounts' => $expenses,
            'total_revenue' => $totalRevenue,
            'total_expenses' => $totalExpenses,
            'net_profit' => $totalRevenue - $totalExpenses,
            'profit_margin' => $totalRevenue > 0 ? (($totalRevenue - $totalExpenses) / $totalRevenue) * 100 : 0,
        ];
    }

    protected function generateBalanceSheetReport($asOfDate)
    {
        $assets = ChartOfAccount::where('account_type', 'asset')->get();
        $liabilities = ChartOfAccount::where('account_type', 'liability')->get();
        $equity = ChartOfAccount::where('account_type', 'equity')->get();

        $totalAssets = $assets->sum('current_balance');
        $totalLiabilities = $liabilities->sum('current_balance');
        $totalEquity = $equity->sum('current_balance');

        return [
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => $equity,
            'total_assets' => $totalAssets,
            'total_liabilities' => $totalLiabilities,
            'total_equity' => $totalEquity,
            'is_balanced' => abs($totalAssets - ($totalLiabilities + $totalEquity)) < 0.01,
        ];
    }

    protected function generateTrialBalanceReport($asOfDate)
    {
        $accounts = ChartOfAccount::where('is_active', true)
                                 ->orderBy('account_code')
                                 ->get();

        $totalDebits = 0;
        $totalCredits = 0;

        $accountBalances = $accounts->map(function ($account) use (&$totalDebits, &$totalCredits) {
            $balance = $account->current_balance;

            if ($account->is_debit_balance) {
                $debitBalance = abs($balance);
                $creditBalance = 0;
                $totalDebits += $debitBalance;
            } else {
                $debitBalance = 0;
                $creditBalance = abs($balance);
                $totalCredits += $creditBalance;
            }

            return [
                'account' => $account,
                'debit_balance' => $debitBalance,
                'credit_balance' => $creditBalance,
            ];
        });

        return [
            'accounts' => $accountBalances,
            'total_debits' => $totalDebits,
            'total_credits' => $totalCredits,
            'is_balanced' => abs($totalDebits - $totalCredits) < 0.01,
        ];
    }
}
