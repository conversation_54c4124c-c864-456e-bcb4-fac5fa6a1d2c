<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\CustomerDocument;
use App\Models\Invoice;
use App\Models\Estimate;
use App\Models\ServiceRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class CustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Customer::with(['invoices', 'estimates'])
            ->withCount(['invoices', 'estimates'])
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('customer_code', 'like', "%{$search}%")
                  ->orWhere('pan_number', 'like', "%{$search}%")
                  ->orWhere('aadhar_number', 'like', "%{$search}%");
            });
        }

        // Customer type filter
        if ($request->filled('customer_type')) {
            $query->where('customer_type', $request->customer_type);
        }

        // KYC status filter
        if ($request->filled('kyc_status')) {
            $query->where('kyc_status', $request->kyc_status);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $customers = $query->paginate(20);

        return view('admin.customers.index', compact('customers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.customers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => 'required|string|max:20|unique:customers,phone',
            'alternate_phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'anniversary_date' => 'nullable|date',
            'customer_type' => 'required|in:individual,business',
            'gender' => 'nullable|in:male,female,other',
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postal_code' => 'required|string|max:10',
            'country' => 'required|string|max:100',
            'pan_number' => 'nullable|string|max:10|unique:customers,pan_number',
            'aadhar_number' => 'nullable|string|max:12|unique:customers,aadhar_number',
            'gstin' => 'nullable|string|max:15|unique:customers,gstin',
            'company_name' => 'nullable|string|max:255',
            'designation' => 'nullable|string|max:100',
            'notes' => 'nullable|string',
            'credit_limit' => 'nullable|numeric|min:0',
            'credit_days' => 'nullable|integer|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'kyc_documents.*' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
        ]);

        try {
            DB::beginTransaction();

            // Generate customer code
            $validated['customer_code'] = $this->generateCustomerCode();
            $validated['kyc_status'] = 'pending';
            $validated['is_active'] = true;

            // Create customer
            $customer = Customer::create($validated);

            // Handle KYC document uploads
            if ($request->hasFile('kyc_documents')) {
                foreach ($request->file('kyc_documents') as $documentType => $file) {
                    if ($file) {
                        $path = $file->store('customers/kyc', 'public');

                        CustomerDocument::create([
                            'customer_id' => $customer->id,
                            'document_type' => $documentType,
                            'file_path' => $path,
                            'original_filename' => $file->getClientOriginalName(),
                            'file_size' => $file->getSize(),
                            'uploaded_by' => auth()->id(),
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()->route('admin.customers.index')
                ->with('success', 'Customer created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error creating customer: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Customer $customer)
    {
        $customer->load(['documents', 'invoices.items.product', 'estimates', 'serviceRequests']);

        // Calculate customer statistics
        $stats = [
            'total_purchases' => $customer->invoices()->sum('total_amount'),
            'total_orders' => $customer->invoices()->count(),
            'last_purchase' => $customer->invoices()->latest()->first()?->created_at,
            'pending_estimates' => $customer->estimates()->where('status', 'pending')->count(),
            'active_services' => $customer->serviceRequests()->whereIn('status', ['pending', 'in_progress'])->count(),
        ];

        return view('admin.customers.show', compact('customer', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Customer $customer)
    {
        return view('admin.customers.edit', compact('customer'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email,' . $customer->id,
            'phone' => 'required|string|max:20|unique:customers,phone,' . $customer->id,
            'alternate_phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'anniversary_date' => 'nullable|date',
            'customer_type' => 'required|in:individual,business',
            'gender' => 'nullable|in:male,female,other',
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postal_code' => 'required|string|max:10',
            'country' => 'required|string|max:100',
            'pan_number' => 'nullable|string|max:10|unique:customers,pan_number,' . $customer->id,
            'aadhar_number' => 'nullable|string|max:12|unique:customers,aadhar_number,' . $customer->id,
            'gstin' => 'nullable|string|max:15|unique:customers,gstin,' . $customer->id,
            'company_name' => 'nullable|string|max:255',
            'designation' => 'nullable|string|max:100',
            'notes' => 'nullable|string',
            'credit_limit' => 'nullable|numeric|min:0',
            'credit_days' => 'nullable|integer|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'kyc_status' => 'required|in:pending,verified,rejected',
            'is_active' => 'boolean',
        ]);

        try {
            $customer->update($validated);

            return redirect()->route('admin.customers.show', $customer)
                ->with('success', 'Customer updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Error updating customer: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Customer $customer)
    {
        try {
            DB::beginTransaction();

            // Check if customer has any invoices
            if ($customer->invoices()->count() > 0) {
                return back()->with('error', 'Cannot delete customer with existing invoices.');
            }

            // Delete customer documents from storage
            foreach ($customer->documents as $document) {
                Storage::disk('public')->delete($document->file_path);
                $document->delete();
            }

            // Delete estimates and service requests
            $customer->estimates()->delete();
            $customer->serviceRequests()->delete();

            // Delete the customer
            $customer->delete();

            DB::commit();

            return redirect()->route('admin.customers.index')
                ->with('success', 'Customer deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting customer: ' . $e->getMessage());
        }
    }

    /**
     * Upload KYC document for customer
     */
    public function uploadDocument(Request $request, Customer $customer)
    {
        $request->validate([
            'document_type' => 'required|in:aadhar,pan,passport,driving_license,voter_id,other',
            'document' => 'required|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $file = $request->file('document');
            $path = $file->store('customers/kyc', 'public');

            CustomerDocument::create([
                'customer_id' => $customer->id,
                'document_type' => $request->document_type,
                'file_path' => $path,
                'original_filename' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'notes' => $request->notes,
                'uploaded_by' => auth()->id(),
            ]);

            return response()->json(['success' => true, 'message' => 'Document uploaded successfully.']);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error uploading document: ' . $e->getMessage()]);
        }
    }

    /**
     * Delete customer document
     */
    public function deleteDocument(CustomerDocument $document)
    {
        try {
            Storage::disk('public')->delete($document->file_path);
            $document->delete();

            return response()->json(['success' => true, 'message' => 'Document deleted successfully.']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error deleting document: ' . $e->getMessage()]);
        }
    }

    /**
     * Update KYC status
     */
    public function updateKycStatus(Request $request, Customer $customer)
    {
        $request->validate([
            'kyc_status' => 'required|in:pending,verified,rejected',
            'kyc_notes' => 'nullable|string|max:500',
        ]);

        $customer->update([
            'kyc_status' => $request->kyc_status,
            'kyc_verified_at' => $request->kyc_status === 'verified' ? now() : null,
            'kyc_verified_by' => $request->kyc_status === 'verified' ? auth()->id() : null,
            'kyc_notes' => $request->kyc_notes,
        ]);

        return response()->json(['success' => true, 'message' => 'KYC status updated successfully.']);
    }

    /**
     * Generate customer code
     */
    private function generateCustomerCode(): string
    {
        $prefix = 'CUST';
        $year = date('y');
        $month = date('m');

        // Get the last customer code for this month
        $lastCustomer = Customer::where('customer_code', 'like', "{$prefix}{$year}{$month}%")
            ->orderBy('customer_code', 'desc')
            ->first();

        if ($lastCustomer) {
            $lastNumber = (int) substr($lastCustomer->customer_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . $month . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get customer purchase history
     */
    public function purchaseHistory(Customer $customer)
    {
        $invoices = $customer->invoices()
            ->with(['items.product', 'location'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.customers.purchase-history', compact('customer', 'invoices'));
    }

    /**
     * Export customer data
     */
    public function export(Request $request)
    {
        // This would implement CSV/Excel export functionality
        // For now, return a simple response
        return response()->json(['message' => 'Export functionality to be implemented']);
    }
}
