<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Saving Scheme Plans (Templates)
        if (!Schema::hasTable('saving_scheme_plans')) {
            Schema::create('saving_scheme_plans', function (Blueprint $table) {
                $table->id();
                $table->string('plan_name');
                $table->string('plan_code')->unique();
                $table->text('description')->nullable();
                $table->enum('plan_type', ['monthly', 'quarterly', 'yearly', 'custom']);
                $table->integer('duration_months'); // Total duration in months
                $table->decimal('minimum_amount', 10, 2); // Minimum monthly/installment amount
                $table->decimal('maximum_amount', 10, 2)->nullable(); // Maximum monthly/installment amount
                $table->decimal('interest_rate', 5, 2)->default(0); // Annual interest rate %
                $table->decimal('bonus_percentage', 5, 2)->default(0); // Bonus on maturity %
                $table->boolean('allow_partial_withdrawal')->default(false);
                $table->boolean('allow_premature_closure')->default(true);
                $table->decimal('premature_closure_penalty', 5, 2)->default(0); // Penalty %
                $table->integer('grace_period_days')->default(7); // Grace period for late payments
                $table->decimal('late_fee_amount', 8, 2)->default(0); // Late payment fee
                $table->boolean('is_active')->default(true);
                $table->json('terms_conditions')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['plan_type']);
                $table->index(['is_active']);
            });
        }

        // Customer Saving Schemes
        if (!Schema::hasTable('saving_schemes')) {
            Schema::create('saving_schemes', function (Blueprint $table) {
                $table->id();
                $table->string('scheme_number')->unique();
                $table->foreignId('customer_id')->constrained()->onDelete('cascade');
                $table->foreignId('plan_id')->constrained('saving_scheme_plans')->onDelete('cascade');
                $table->string('scheme_name')->nullable(); // Custom name given by customer
                $table->date('start_date');
                $table->date('maturity_date');
                $table->decimal('installment_amount', 10, 2); // Monthly/installment amount
                $table->decimal('total_target_amount', 12, 2); // Total amount to be saved
                $table->decimal('total_paid_amount', 12, 2)->default(0); // Total amount paid so far
                $table->decimal('interest_earned', 10, 2)->default(0); // Interest earned
                $table->decimal('bonus_amount', 10, 2)->default(0); // Bonus amount
                $table->decimal('penalty_amount', 10, 2)->default(0); // Penalty amount
                $table->decimal('current_value', 12, 2)->default(0); // Current scheme value
                $table->enum('status', ['active', 'matured', 'closed', 'suspended', 'defaulted'])->default('active');
                $table->date('last_payment_date')->nullable();
                $table->date('next_due_date')->nullable();
                $table->integer('payments_completed')->default(0);
                $table->integer('payments_missed')->default(0);
                $table->text('closure_reason')->nullable();
                $table->date('closure_date')->nullable();
                $table->foreignId('closed_by')->nullable()->constrained('users')->onDelete('set null');
                $table->text('notes')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['customer_id']);
                $table->index(['status']);
                $table->index(['maturity_date']);
                $table->index(['next_due_date']);
            });
        }

        // Scheme Payments
        if (!Schema::hasTable('scheme_payments')) {
            Schema::create('scheme_payments', function (Blueprint $table) {
                $table->id();
                $table->string('payment_number')->unique();
                $table->foreignId('scheme_id')->constrained('saving_schemes')->onDelete('cascade');
                $table->date('payment_date');
                $table->date('due_date');
                $table->decimal('amount', 10, 2);
                $table->decimal('late_fee', 8, 2)->default(0);
                $table->decimal('total_amount', 10, 2); // amount + late_fee
                $table->enum('payment_method', ['cash', 'card', 'upi', 'bank_transfer', 'cheque']);
                $table->string('payment_reference')->nullable();
                $table->enum('status', ['pending', 'paid', 'overdue', 'waived'])->default('pending');
                $table->integer('installment_number'); // Which installment this is
                $table->boolean('is_advance_payment')->default(false);
                $table->text('notes')->nullable();
                $table->foreignId('received_by')->nullable()->constrained('users')->onDelete('set null');
                $table->timestamp('paid_at')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['scheme_id']);
                $table->index(['payment_date']);
                $table->index(['due_date']);
                $table->index(['status']);
                $table->index(['installment_number']);
            });
        }

        // Scheme Transactions (Interest, Bonus, Penalties)
        if (!Schema::hasTable('scheme_transactions')) {
            Schema::create('scheme_transactions', function (Blueprint $table) {
                $table->id();
                $table->string('transaction_number')->unique();
                $table->foreignId('scheme_id')->constrained('saving_schemes')->onDelete('cascade');
                $table->enum('transaction_type', ['interest', 'bonus', 'penalty', 'adjustment', 'withdrawal']);
                $table->decimal('amount', 10, 2);
                $table->enum('type', ['credit', 'debit']); // Credit increases scheme value, debit decreases
                $table->date('transaction_date');
                $table->text('description');
                $table->text('notes')->nullable();
                $table->foreignId('processed_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();

                $table->index(['scheme_id']);
                $table->index(['transaction_type']);
                $table->index(['transaction_date']);
            });
        }

        // Scheme Maturity Actions
        if (!Schema::hasTable('scheme_maturities')) {
            Schema::create('scheme_maturities', function (Blueprint $table) {
                $table->id();
                $table->foreignId('scheme_id')->constrained('saving_schemes')->onDelete('cascade');
                $table->date('maturity_date');
                $table->decimal('maturity_amount', 12, 2); // Total amount available at maturity
                $table->decimal('principal_amount', 12, 2); // Total payments made
                $table->decimal('interest_amount', 10, 2); // Interest earned
                $table->decimal('bonus_amount', 10, 2); // Bonus amount
                $table->enum('action_taken', ['pending', 'cash_withdrawal', 'jewelry_purchase', 'scheme_renewal', 'transfer']);
                $table->decimal('amount_withdrawn', 12, 2)->default(0);
                $table->decimal('amount_used_for_purchase', 12, 2)->default(0);
                $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null'); // If used for jewelry purchase
                $table->date('action_date')->nullable();
                $table->text('notes')->nullable();
                $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
                $table->timestamps();

                $table->index(['maturity_date']);
                $table->index(['action_taken']);
            });
        }

        // Scheme Notifications
        if (!Schema::hasTable('scheme_notifications')) {
            Schema::create('scheme_notifications', function (Blueprint $table) {
                $table->id();
                $table->foreignId('scheme_id')->constrained('saving_schemes')->onDelete('cascade');
                $table->enum('notification_type', ['payment_due', 'payment_overdue', 'maturity_approaching', 'maturity_reached', 'scheme_suspended']);
                $table->string('title');
                $table->text('message');
                $table->date('scheduled_date');
                $table->boolean('is_sent')->default(false);
                $table->timestamp('sent_at')->nullable();
                $table->json('delivery_channels')->nullable(); // ['sms', 'email', 'whatsapp']
                $table->timestamps();

                $table->index(['scheme_id']);
                $table->index(['notification_type']);
                $table->index(['scheduled_date']);
                $table->index(['is_sent']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scheme_notifications');
        Schema::dropIfExists('scheme_maturities');
        Schema::dropIfExists('scheme_transactions');
        Schema::dropIfExists('scheme_payments');
        Schema::dropIfExists('saving_schemes');
        Schema::dropIfExists('saving_scheme_plans');
    }
};
