<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ThermalPrintingService;
use App\Models\PrinterConfiguration;
use App\Models\PrintJob;
use App\Models\PrintTemplate;
use App\Models\Invoice;
use App\Models\Estimate;
use App\Models\InventoryItem;

class PrintController extends Controller
{
    protected $printingService;

    public function __construct(ThermalPrintingService $printingService)
    {
        $this->printingService = $printingService;
    }

    /**
     * Print invoice
     */
    public function printInvoice(Request $request, $invoiceId)
    {
        $request->validate([
            'printer_id' => 'nullable|exists:printer_configurations,id',
            'copies' => 'integer|min:1|max:10',
        ]);

        try {
            $printJob = $this->printingService->printInvoice(
                $invoiceId,
                $request->printer_id,
                $request->copies ?? 1
            );

            return response()->json([
                'success' => true,
                'message' => 'Invoice print job created successfully',
                'job_id' => $printJob->job_id,
                'status' => $printJob->status,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to print invoice: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Print receipt
     */
    public function printReceipt(Request $request, $invoiceId)
    {
        $request->validate([
            'printer_id' => 'nullable|exists:printer_configurations,id',
            'copies' => 'integer|min:1|max:10',
        ]);

        try {
            $printJob = $this->printingService->printReceipt(
                $invoiceId,
                $request->printer_id,
                $request->copies ?? 1
            );

            return response()->json([
                'success' => true,
                'message' => 'Receipt print job created successfully',
                'job_id' => $printJob->job_id,
                'status' => $printJob->status,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to print receipt: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Print estimate
     */
    public function printEstimate(Request $request, $estimateId)
    {
        $request->validate([
            'printer_id' => 'nullable|exists:printer_configurations,id',
            'copies' => 'integer|min:1|max:10',
        ]);

        try {
            $printJob = $this->printingService->printEstimate(
                $estimateId,
                $request->printer_id,
                $request->copies ?? 1
            );

            return response()->json([
                'success' => true,
                'message' => 'Estimate print job created successfully',
                'job_id' => $printJob->job_id,
                'status' => $printJob->status,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to print estimate: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Print inventory tag
     */
    public function printInventoryTag(Request $request, $itemId)
    {
        $request->validate([
            'printer_id' => 'nullable|exists:printer_configurations,id',
            'copies' => 'integer|min:1|max:10',
        ]);

        try {
            $printJob = $this->printingService->printInventoryTag(
                $itemId,
                $request->printer_id,
                $request->copies ?? 1
            );

            return response()->json([
                'success' => true,
                'message' => 'Inventory tag print job created successfully',
                'job_id' => $printJob->job_id,
                'status' => $printJob->status,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to print inventory tag: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Print barcode label
     */
    public function printBarcodeLabel(Request $request, $itemId)
    {
        $request->validate([
            'printer_id' => 'nullable|exists:printer_configurations,id',
            'copies' => 'integer|min:1|max:10',
        ]);

        try {
            $printJob = $this->printingService->printBarcodeLabel(
                $itemId,
                $request->printer_id,
                $request->copies ?? 1
            );

            return response()->json([
                'success' => true,
                'message' => 'Barcode label print job created successfully',
                'job_id' => $printJob->job_id,
                'status' => $printJob->status,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to print barcode label: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Print QR code label
     */
    public function printQRCodeLabel(Request $request, $itemId)
    {
        $request->validate([
            'printer_id' => 'nullable|exists:printer_configurations,id',
            'copies' => 'integer|min:1|max:10',
        ]);

        try {
            $printJob = $this->printingService->printQRCodeLabel(
                $itemId,
                $request->printer_id,
                $request->copies ?? 1
            );

            return response()->json([
                'success' => true,
                'message' => 'QR code label print job created successfully',
                'job_id' => $printJob->job_id,
                'status' => $printJob->status,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to print QR code label: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get print job status
     */
    public function getJobStatus($jobId)
    {
        $printJob = $this->printingService->getPrintJobStatus($jobId);

        if (!$printJob) {
            return response()->json([
                'success' => false,
                'message' => 'Print job not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'job' => [
                'job_id' => $printJob->job_id,
                'status' => $printJob->status,
                'print_type' => $printJob->print_type,
                'copies' => $printJob->copies,
                'created_at' => $printJob->created_at,
                'completed_at' => $printJob->completed_at,
                'error_message' => $printJob->error_message,
                'processing_time_ms' => $printJob->processing_time_ms,
                'printer_name' => $printJob->printerConfiguration->name,
            ],
        ]);
    }

    /**
     * Cancel print job
     */
    public function cancelJob($jobId)
    {
        $cancelled = $this->printingService->cancelPrintJob($jobId);

        if ($cancelled) {
            return response()->json([
                'success' => true,
                'message' => 'Print job cancelled successfully',
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Print job could not be cancelled or not found',
        ], 404);
    }

    /**
     * Test printer
     */
    public function testPrinter($printerId)
    {
        try {
            $this->printingService->testPrinter($printerId);

            return response()->json([
                'success' => true,
                'message' => 'Printer test completed successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Printer test failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get printer status
     */
    public function getPrinterStatus($printerId)
    {
        $status = $this->printingService->getPrinterStatus($printerId);

        if (!$status) {
            return response()->json([
                'success' => false,
                'message' => 'Printer not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status' => $status,
        ]);
    }

    /**
     * Get print queue
     */
    public function getPrintQueue(Request $request)
    {
        $printerId = $request->query('printer_id');
        $status = $request->query('status');

        $queue = $this->printingService->getPrintQueue($printerId, $status);

        return response()->json([
            'success' => true,
            'queue' => $queue->map(function ($job) {
                return [
                    'job_id' => $job->job_id,
                    'print_type' => $job->print_type,
                    'status' => $job->status,
                    'copies' => $job->copies,
                    'printer_name' => $job->printerConfiguration->name,
                    'user_name' => $job->user->name,
                    'created_at' => $job->created_at,
                    'completed_at' => $job->completed_at,
                    'error_message' => $job->error_message,
                ];
            }),
        ]);
    }

    /**
     * Get printing statistics
     */
    public function getStatistics(Request $request)
    {
        $printerId = $request->query('printer_id');
        $days = $request->query('days', 30);

        $statistics = $this->printingService->getPrintingStatistics($printerId, $days);

        return response()->json([
            'success' => true,
            'statistics' => $statistics,
        ]);
    }

    /**
     * Get available printers
     */
    public function getPrinters()
    {
        $printers = PrinterConfiguration::where('is_active', true)
                                       ->orderBy('name')
                                       ->get();

        return response()->json([
            'success' => true,
            'printers' => $printers->map(function ($printer) {
                return [
                    'id' => $printer->id,
                    'name' => $printer->name,
                    'type' => $printer->type,
                    'connection_type' => $printer->connection_type,
                    'is_online' => $printer->is_online,
                    'supports_barcode' => $printer->supports_barcode,
                    'supports_qr_code' => $printer->supports_qr_code,
                    'is_default_invoice' => $printer->is_default_invoice,
                    'is_default_receipt' => $printer->is_default_receipt,
                    'is_default_label' => $printer->is_default_label,
                    'is_default_tag' => $printer->is_default_tag,
                    'last_used_at' => $printer->last_used_at,
                ];
            }),
        ]);
    }

    /**
     * Get print templates
     */
    public function getTemplates($templateType = null)
    {
        $query = PrintTemplate::where('is_active', true);

        if ($templateType) {
            $query->where('template_type', $templateType);
        }

        $templates = $query->orderBy('name')->get();

        return response()->json([
            'success' => true,
            'templates' => $templates->map(function ($template) {
                return [
                    'id' => $template->id,
                    'name' => $template->name,
                    'code' => $template->code,
                    'template_type' => $template->template_type,
                    'description' => $template->description,
                    'paper_width_mm' => $template->paper_width_mm,
                    'is_default' => $template->is_default,
                    'is_system_template' => $template->is_system_template,
                    'usage_count' => $template->usage_count,
                ];
            }),
        ]);
    }

    /**
     * Bulk print inventory tags
     */
    public function bulkPrintInventoryTags(Request $request)
    {
        $request->validate([
            'item_ids' => 'required|array|min:1',
            'item_ids.*' => 'exists:inventory_items,id',
            'printer_id' => 'nullable|exists:printer_configurations,id',
            'copies' => 'integer|min:1|max:10',
        ]);

        try {
            $jobs = [];
            $copies = $request->copies ?? 1;

            foreach ($request->item_ids as $itemId) {
                $printJob = $this->printingService->printInventoryTag(
                    $itemId,
                    $request->printer_id,
                    $copies
                );
                $jobs[] = $printJob->job_id;
            }

            return response()->json([
                'success' => true,
                'message' => 'Bulk print jobs created successfully',
                'job_ids' => $jobs,
                'total_jobs' => count($jobs),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create bulk print jobs: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clear completed print jobs
     */
    public function clearCompletedJobs(Request $request)
    {
        $days = $request->query('days', 7);
        $deletedCount = $this->printingService->clearCompletedJobs($days);

        return response()->json([
            'success' => true,
            'message' => "Cleared {$deletedCount} completed print jobs",
            'deleted_count' => $deletedCount,
        ]);
    }

    /**
     * Preview print content
     */
    public function previewPrint(Request $request)
    {
        $request->validate([
            'type' => 'required|in:invoice,receipt,estimate,tag,barcode,qr_code',
            'id' => 'required|integer',
            'template_id' => 'nullable|exists:print_templates,id',
        ]);

        try {
            // This would generate a preview of the print content
            // For now, return a simple response
            return response()->json([
                'success' => true,
                'message' => 'Print preview generated',
                'preview_url' => '/print/preview/' . $request->type . '/' . $request->id,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate preview: ' . $e->getMessage(),
            ], 500);
        }
    }
}
