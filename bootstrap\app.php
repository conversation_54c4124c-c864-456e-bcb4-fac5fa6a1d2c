<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'prevent.duplicates' => \App\Http\Middleware\PreventDuplicateSubmissions::class,
            'handle.unique.constraints' => \App\Http\Middleware\HandleUniqueConstraintViolations::class,
        ]);
        
        // Apply unique constraint handling to all web routes
        $middleware->web(append: [
            \App\Http\Middleware\HandleUniqueConstraintViolations::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        $exceptions->render(function (\Illuminate\Database\UniqueConstraintViolationException $e, $request) {
            return \App\Exceptions\UniqueConstraintHandler::handle($e, $request);
        });
        
        $exceptions->render(function (\Illuminate\Database\QueryException $e, $request) {
            // Check if it's a unique constraint violation
            if (\App\Services\UniqueConstraintService::isUniqueConstraintViolation($e)) {
                return \App\Exceptions\UniqueConstraintHandler::handle($e, $request);
            }
            
            // Let other QueryExceptions be handled normally
            return null;
        });
    })->create();
