<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EstimateItem extends Model
{
    protected $fillable = [
        'estimate_id',
        'product_id',
        'custom_item_name',
        'quantity',
        'unit_price',
        'discount_percentage',
        'discount_amount',
        'making_charges',
        'stone_charges',
        'taxable_amount',
        'gst_rate',
        'cgst_amount',
        'sgst_amount',
        'igst_amount',
        'total_amount',
        'metal_rate_locked',
        'locked_metal_rate',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'making_charges' => 'decimal:2',
        'stone_charges' => 'decimal:2',
        'taxable_amount' => 'decimal:2',
        'gst_rate' => 'decimal:2',
        'cgst_amount' => 'decimal:2',
        'sgst_amount' => 'decimal:2',
        'igst_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'metal_rate_locked' => 'boolean',
        'locked_metal_rate' => 'decimal:2',
    ];

    // Relationships
    public function estimate(): BelongsTo
    {
        return $this->belongsTo(Estimate::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // Accessors
    public function getItemNameAttribute()
    {
        return $this->product ? $this->product->name : $this->custom_item_name;
    }

    public function getSubtotalAttribute()
    {
        return ($this->unit_price * $this->quantity) + $this->making_charges + $this->stone_charges;
    }

    public function getDiscountedAmountAttribute()
    {
        return $this->subtotal - $this->discount_amount;
    }

    public function getTotalGstAttribute()
    {
        return $this->cgst_amount + $this->sgst_amount + $this->igst_amount;
    }

    public function getFormattedTotalAttribute()
    {
        return '₹' . number_format($this->total_amount, 2);
    }

    public function getHsnCodeAttribute()
    {
        if ($this->product) {
            return $this->product->hsn_code ?? $this->product->category->hsn_code ?? '7113';
        }

        return '7113'; // Default HSN for jewelry
    }

    public function getIsCustomItemAttribute()
    {
        return !$this->product_id && $this->custom_item_name;
    }

    // Methods
    public function calculateCurrentPrice()
    {
        if (!$this->product || $this->metal_rate_locked) {
            return $this->unit_price;
        }

        // Calculate current price based on metal rates
        $metalRateService = app(\App\Services\MetalRateService::class);
        $currentRate = $metalRateService->getCurrentRate($this->product->metal_id);

        if ($currentRate) {
            $weight = $this->product->net_weight ?? 0;
            $metalValue = $weight * $currentRate;
            $makingCharges = $this->making_charges;
            $stoneCharges = $this->stone_charges;

            return $metalValue + $makingCharges + $stoneCharges;
        }

        return $this->unit_price;
    }

    public function lockMetalRate($rate = null)
    {
        if (!$rate && $this->product) {
            $metalRateService = app(\App\Services\MetalRateService::class);
            $rate = $metalRateService->getCurrentRate($this->product->metal_id);
        }

        $this->update([
            'metal_rate_locked' => true,
            'locked_metal_rate' => $rate,
        ]);

        return $this;
    }

    public function unlockMetalRate()
    {
        $this->update([
            'metal_rate_locked' => false,
            'locked_metal_rate' => null,
        ]);

        return $this;
    }

    public function updatePricing()
    {
        if ($this->metal_rate_locked) {
            return $this; // Don't update if rate is locked
        }

        $newPrice = $this->calculateCurrentPrice();

        if ($newPrice !== $this->unit_price) {
            $this->update(['unit_price' => $newPrice]);
        }

        return $this;
    }
}
