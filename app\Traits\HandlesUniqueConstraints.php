<?php

namespace App\Traits;

use Illuminate\Database\QueryException;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Support\Facades\Log;

trait HandlesUniqueConstraints
{
    /**
     * Save the model with automatic unique constraint handling
     */
    public function saveWithUniqueHandling(array $options = [])
    {
        $maxRetries = 3;
        $attempt = 0;

        while ($attempt < $maxRetries) {
            try {
                return $this->save($options);
            } catch (QueryException $e) {
                if ($this->isUniqueConstraintViolation($e)) {
                    $attempt++;
                    
                    Log::warning("Unique constraint violation attempt {$attempt}", [
                        'model' => get_class($this),
                        'error' => $e->getMessage(),
                        'attempt' => $attempt
                    ]);

                    if ($attempt >= $maxRetries) {
                        throw $e;
                    }

                    // Try to regenerate unique fields
                    $this->regenerateUniqueFields();
                    
                    // Small delay to avoid race conditions
                    usleep(100000); // 100ms
                } else {
                    throw $e;
                }
            } catch (UniqueConstraintViolationException $e) {
                $attempt++;
                
                Log::warning("Unique constraint violation attempt {$attempt}", [
                    'model' => get_class($this),
                    'error' => $e->getMessage(),
                    'attempt' => $attempt
                ]);

                if ($attempt >= $maxRetries) {
                    throw $e;
                }

                $this->regenerateUniqueFields();
                usleep(100000); // 100ms
            }
        }

        return false;
    }

    /**
     * Create a model with automatic unique constraint handling
     */
    public static function createWithUniqueHandling(array $attributes = [])
    {
        $instance = new static($attributes);
        $instance->saveWithUniqueHandling();
        return $instance;
    }

    /**
     * Check if the exception is a unique constraint violation
     */
    private function isUniqueConstraintViolation($exception): bool
    {
        if ($exception instanceof UniqueConstraintViolationException) {
            return true;
        }

        if ($exception instanceof QueryException) {
            $message = $exception->getMessage();
            return str_contains($message, 'Duplicate entry') ||
                   str_contains($message, 'UNIQUE constraint') ||
                   (str_contains($message, 'Integrity constraint violation') && 
                    str_contains($message, '1062'));
        }

        return false;
    }

    /**
     * Regenerate unique fields for this model
     */
    private function regenerateUniqueFields()
    {
        $modelClass = get_class($this);
        $originalAttributes = $this->getDirty();

        // Handle different model types
        switch ($modelClass) {
            case 'App\Models\SavingSchemePlan':
                if (method_exists($this, 'generatePlanCode')) {
                    $this->plan_code = static::generatePlanCode($this);
                }
                break;

            case 'App\Models\SavingScheme':
                if (method_exists($this, 'generateSchemeNumber')) {
                    $this->scheme_number = static::generateSchemeNumber();
                }
                break;

            case 'App\Models\SchemePayment':
                if (method_exists($this, 'generatePaymentNumber')) {
                    $this->payment_number = static::generatePaymentNumber();
                }
                if (empty($this->receipt_number) || $this->isDirty('receipt_number')) {
                    $this->receipt_number = 'REC' . time() . rand(1000, 9999);
                }
                break;

            case 'App\Models\SchemeTransaction':
                if (method_exists($this, 'generateTransactionNumber')) {
                    $this->transaction_number = static::generateTransactionNumber();
                }
                break;

            case 'App\Models\Customer':
                // For customers, we need to be more careful about modifying contact info
                if (!empty($this->customer_code) && method_exists($this, 'generateCustomerCode')) {
                    $this->customer_code = static::generateCustomerCode();
                }
                // Only modify phone/email as last resort and log it
                if ($this->isDirty('phone') && !empty($this->phone)) {
                    $originalPhone = $this->phone;
                    $this->phone = $this->phone . rand(10, 99);
                    Log::warning("Modified customer phone due to duplicate", [
                        'customer_id' => $this->id,
                        'original_phone' => $originalPhone,
                        'new_phone' => $this->phone
                    ]);
                }
                if ($this->isDirty('email') && !empty($this->email)) {
                    $originalEmail = $this->email;
                    $this->email = time() . '_' . $this->email;
                    Log::warning("Modified customer email due to duplicate", [
                        'customer_id' => $this->id,
                        'original_email' => $originalEmail,
                        'new_email' => $this->email
                    ]);
                }
                break;

            case 'App\Models\User':
                // For users, modify email but log it as this is critical
                if ($this->isDirty('email') && !empty($this->email)) {
                    $originalEmail = $this->email;
                    $this->email = time() . '_' . $this->email;
                    Log::warning("Modified user email due to duplicate", [
                        'user_id' => $this->id,
                        'original_email' => $originalEmail,
                        'new_email' => $this->email
                    ]);
                }
                break;

            case 'App\Models\Product':
                if (!empty($this->sku) && method_exists($this, 'generateSku')) {
                    $this->sku = static::generateSku();
                }
                if (!empty($this->barcode) && method_exists($this, 'generateBarcode')) {
                    $this->barcode = static::generateBarcode();
                }
                break;

            case 'App\Models\Invoice':
                if (!empty($this->invoice_number) && method_exists($this, 'generateInvoiceNumber')) {
                    $this->invoice_number = static::generateInvoiceNumber();
                }
                break;

            default:
                // Generic handling for models with common unique fields
                $this->regenerateCommonUniqueFields();
                break;
        }

        Log::info("Regenerated unique fields for {$modelClass}", [
            'model_id' => $this->id,
            'original_attributes' => $originalAttributes,
            'new_attributes' => $this->getDirty()
        ]);
    }

    /**
     * Handle common unique fields across models
     */
    private function regenerateCommonUniqueFields()
    {
        $commonFields = [
            'code' => 'generateCode',
            'number' => 'generateNumber',
            'reference' => 'generateReference',
            'slug' => 'generateSlug',
        ];

        foreach ($commonFields as $field => $method) {
            if ($this->isDirty($field) && !empty($this->$field) && method_exists($this, $method)) {
                $this->$field = static::$method();
            }
        }
    }

    /**
     * Boot the trait
     */
    public static function bootHandlesUniqueConstraints()
    {
        // Optionally add model events here if needed
    }
}
