<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\GeneratesUniqueFields;

class SchemeNotification extends Model
{
    use HasFactory, GeneratesUniqueFields;

    protected $fillable = [
        'scheme_id',
        'notification_type',
        'title',
        'message',
        'scheduled_date',
        'is_sent',
        'sent_at',
        'delivery_channels',
    ];

    protected $casts = [
        'scheduled_date' => 'date',
        'sent_at' => 'datetime',
        'is_sent' => 'boolean',
        'delivery_channels' => 'array',
    ];

    /**
     * Relationships
     */
    public function scheme(): BelongsTo
    {
        return $this->belongsTo(SavingScheme::class, 'scheme_id');
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('is_sent', false);
    }

    public function scopeSent($query)
    {
        return $query->where('is_sent', true);
    }

    public function scopeDue($query)
    {
        return $query->where('is_sent', false)
                    ->where('scheduled_date', '<=', now()->toDateString());
    }

    public function scopeByType($query, $type)
    {
        return $query->where('notification_type', $type);
    }

    /**
     * Accessors
     */
    public function getNotificationTypeDisplayAttribute(): string
    {
        $types = [
            'payment_due' => 'Payment Due',
            'payment_overdue' => 'Payment Overdue',
            'maturity_approaching' => 'Maturity Approaching',
            'maturity_reached' => 'Maturity Reached',
            'scheme_suspended' => 'Scheme Suspended',
        ];

        return $types[$this->notification_type] ?? ucfirst(str_replace('_', ' ', $this->notification_type));
    }

    public function getDeliveryChannelsDisplayAttribute(): string
    {
        if (empty($this->delivery_channels)) {
            return 'None';
        }

        $channels = [
            'sms' => 'SMS',
            'email' => 'Email',
            'whatsapp' => 'WhatsApp',
        ];

        $displayChannels = array_map(function ($channel) use ($channels) {
            return $channels[$channel] ?? ucfirst($channel);
        }, $this->delivery_channels);

        return implode(', ', $displayChannels);
    }

    /**
     * Business Logic Methods
     */
    public function markAsSent(): self
    {
        $this->update([
            'is_sent' => true,
            'sent_at' => now(),
        ]);

        return $this;
    }

    public static function createPaymentDueNotification($scheme, $dueDate): self
    {
        return static::create([
            'scheme_id' => $scheme->id,
            'notification_type' => 'payment_due',
            'title' => 'Payment Due Reminder',
            'message' => "Your saving scheme payment of {$scheme->formatted_installment_amount} is due on {$dueDate->format('d-m-Y')}.",
            'scheduled_date' => $dueDate->subDays(3),
            'delivery_channels' => ['sms', 'email'],
        ]);
    }

    public static function createPaymentOverdueNotification($scheme): self
    {
        return static::create([
            'scheme_id' => $scheme->id,
            'notification_type' => 'payment_overdue',
            'title' => 'Payment Overdue',
            'message' => "Your saving scheme payment of {$scheme->formatted_installment_amount} is overdue. Please make the payment to avoid penalties.",
            'scheduled_date' => now()->toDateString(),
            'delivery_channels' => ['sms', 'email'],
        ]);
    }

    public static function createMaturityNotification($scheme): self
    {
        return static::create([
            'scheme_id' => $scheme->id,
            'notification_type' => 'maturity_reached',
            'title' => 'Scheme Matured',
            'message' => "Congratulations! Your saving scheme has matured. Please visit our store to collect your maturity amount.",
            'scheduled_date' => $scheme->maturity_date,
            'delivery_channels' => ['sms', 'email'],
        ]);
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'scheme_number' => $this->scheme->scheme_number,
            'customer_name' => $this->scheme->customer->full_name,
            'notification_type' => $this->notification_type_display,
            'title' => $this->title,
            'message' => $this->message,
            'scheduled_date' => $this->scheduled_date->format('Y-m-d'),
            'delivery_channels' => $this->delivery_channels_display,
            'is_sent' => $this->is_sent,
            'sent_at' => $this->sent_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
