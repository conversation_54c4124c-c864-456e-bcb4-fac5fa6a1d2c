<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GoodsReceiptNote;
use App\Models\PurchaseOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GoodsReceiptNoteController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_goods_receipt')->only(['index', 'show']);
        $this->middleware('permission:create_goods_receipt')->only(['create', 'store']);
        $this->middleware('permission:edit_goods_receipt')->only(['edit', 'update']);
        $this->middleware('permission:delete_goods_receipt')->only(['destroy']);
    }

    /**
     * Display a listing of goods receipt notes
     */
    public function index(Request $request)
    {
        $query = GoodsReceiptNote::with(['purchaseOrder', 'supplier', 'location', 'createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('grn_number', 'like', "%{$search}%")
                  ->orWhereHas('purchaseOrder', function ($pq) use ($search) {
                      $pq->where('po_number', 'like', "%{$search}%");
                  })
                  ->orWhereHas('supplier', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by supplier
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Filter by location
        if ($request->filled('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('received_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('received_date', '<=', $request->date_to);
        }

        // Filter by quality rating
        if ($request->filled('quality_rating')) {
            $query->where('quality_rating', $request->quality_rating);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $grns = $query->paginate(20);

        // Get filter options
        $suppliers = \App\Models\Supplier::active()->orderBy('name')->get(['id', 'name']);
        $locations = \App\Models\Location::orderBy('name')->get(['id', 'name']);

        return view('admin.goods-receipt-notes.index', compact('grns', 'suppliers', 'locations'));
    }

    /**
     * Show the form for creating a new GRN
     */
    public function create(Request $request)
    {
        $purchaseOrderId = $request->get('purchase_order_id');
        $purchaseOrder = null;

        if ($purchaseOrderId) {
            $purchaseOrder = PurchaseOrder::with(['supplier', 'location', 'items.product'])
                                         ->where('id', $purchaseOrderId)
                                         ->where('status', '!=', 'completed')
                                         ->firstOrFail();
        }

        $availablePOs = PurchaseOrder::with(['supplier'])
                                   ->whereIn('status', ['confirmed', 'partially_received'])
                                   ->orderBy('po_date', 'desc')
                                   ->get();

        return view('admin.goods-receipt-notes.create', compact('purchaseOrder', 'availablePOs'));
    }

    /**
     * Store a newly created GRN
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'purchase_order_id' => 'required|exists:purchase_orders,id',
            'received_date' => 'required|date',
            'quality_rating' => 'nullable|numeric|min:1|max:5',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.po_item_id' => 'required|exists:purchase_order_items,id',
            'items.*.quantity_received' => 'required|numeric|min:0.001',
            'items.*.quality_rating' => 'nullable|numeric|min:1|max:5',
            'items.*.notes' => 'nullable|string',
            'update_inventory' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $purchaseOrder = PurchaseOrder::findOrFail($validated['purchase_order_id']);

            if (!$purchaseOrder->can_receive) {
                throw new \Exception('This purchase order cannot receive goods');
            }

            // Create GRN
            $grn = GoodsReceiptNote::create([
                'purchase_order_id' => $validated['purchase_order_id'],
                'supplier_id' => $purchaseOrder->supplier_id,
                'location_id' => $purchaseOrder->location_id,
                'received_date' => $validated['received_date'],
                'quality_rating' => $validated['quality_rating'],
                'notes' => $validated['notes'],
            ]);

            // Add items
            foreach ($validated['items'] as $itemData) {
                $grn->addItem(
                    $itemData['po_item_id'],
                    $itemData['quantity_received'],
                    $itemData['quality_rating'] ?? null,
                    $itemData['notes'] ?? null
                );
            }

            // Update inventory if requested
            if ($validated['update_inventory'] ?? false) {
                $grn->updateInventory();
            }

            DB::commit();

            return redirect()
                ->route('admin.goods-receipt-notes.show', $grn)
                ->with('success', 'Goods receipt note created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Failed to create GRN: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified GRN
     */
    public function show(GoodsReceiptNote $goodsReceiptNote)
    {
        $goodsReceiptNote->load([
            'purchaseOrder',
            'supplier',
            'location',
            'createdBy',
            'items.product.category',
            'items.product.metal',
            'items.purchaseOrderItem'
        ]);

        return view('admin.goods-receipt-notes.show', compact('goodsReceiptNote'));
    }

    /**
     * Show the form for editing the specified GRN
     */
    public function edit(GoodsReceiptNote $goodsReceiptNote)
    {
        $goodsReceiptNote->load([
            'purchaseOrder.items.product',
            'items.purchaseOrderItem'
        ]);

        return view('admin.goods-receipt-notes.edit', compact('goodsReceiptNote'));
    }

    /**
     * Update the specified GRN
     */
    public function update(Request $request, GoodsReceiptNote $goodsReceiptNote)
    {
        $validated = $request->validate([
            'received_date' => 'required|date',
            'quality_rating' => 'nullable|numeric|min:1|max:5',
            'notes' => 'nullable|string',
        ]);

        try {
            $goodsReceiptNote->update($validated);

            return redirect()
                ->route('admin.goods-receipt-notes.show', $goodsReceiptNote)
                ->with('success', 'Goods receipt note updated successfully.');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to update GRN: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified GRN
     */
    public function destroy(GoodsReceiptNote $goodsReceiptNote)
    {
        try {
            DB::beginTransaction();

            // Reverse the received quantities in purchase order items
            foreach ($goodsReceiptNote->items as $grnItem) {
                $grnItem->purchaseOrderItem->decrement('quantity_received', $grnItem->quantity_received);
            }

            // Update purchase order status
            $goodsReceiptNote->purchaseOrder->updateReceiptStatus();

            // Delete the GRN
            $goodsReceiptNote->delete();

            DB::commit();

            return redirect()
                ->route('admin.goods-receipt-notes.index')
                ->with('success', 'Goods receipt note deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to delete GRN: ' . $e->getMessage());
        }
    }

    /**
     * Update quality rating
     */
    public function updateQualityRating(Request $request, GoodsReceiptNote $goodsReceiptNote)
    {
        $validated = $request->validate([
            'quality_rating' => 'required|numeric|min:1|max:5',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $goodsReceiptNote->updateQualityRating(
                $validated['quality_rating'],
                $validated['notes'] ?? null
            );

            return back()->with('success', 'Quality rating updated successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update quality rating: ' . $e->getMessage());
        }
    }

    /**
     * Update inventory from GRN
     */
    public function updateInventory(GoodsReceiptNote $goodsReceiptNote)
    {
        try {
            $goodsReceiptNote->updateInventory();

            return back()->with('success', 'Inventory updated successfully from GRN.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update inventory: ' . $e->getMessage());
        }
    }

    /**
     * Print GRN
     */
    public function print(GoodsReceiptNote $goodsReceiptNote)
    {
        $goodsReceiptNote->load([
            'purchaseOrder',
            'supplier',
            'location',
            'createdBy',
            'items.product.category',
            'items.product.metal'
        ]);

        return view('admin.goods-receipt-notes.print', compact('goodsReceiptNote'));
    }

    /**
     * Get available purchase orders for receiving
     */
    public function getAvailablePurchaseOrders(Request $request)
    {
        $query = PurchaseOrder::with(['supplier'])
                             ->whereIn('status', ['confirmed', 'partially_received']);

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        $purchaseOrders = $query->orderBy('po_date', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $purchaseOrders->map(function ($po) {
                return [
                    'id' => $po->id,
                    'po_number' => $po->po_number,
                    'supplier_name' => $po->supplier->name,
                    'po_date' => $po->po_date->format('Y-m-d'),
                    'expected_delivery_date' => $po->expected_delivery_date?->format('Y-m-d'),
                    'total_amount' => $po->total_amount,
                    'completion_percentage' => $po->completion_percentage,
                ];
            }),
        ]);
    }

    /**
     * Get purchase order items for receiving
     */
    public function getPurchaseOrderItems(PurchaseOrder $purchaseOrder)
    {
        $items = $purchaseOrder->items()
                              ->with(['product.category', 'product.metal'])
                              ->where('quantity_received', '<', DB::raw('quantity_ordered'))
                              ->get();

        return response()->json([
            'success' => true,
            'data' => $items->map(function ($item) {
                return [
                    'id' => $item->id,
                    'product_name' => $item->product->name,
                    'product_code' => $item->product->product_code,
                    'category' => $item->product->category->name,
                    'metal' => $item->product->metal->name,
                    'quantity_ordered' => $item->quantity_ordered,
                    'quantity_received' => $item->quantity_received,
                    'pending_quantity' => $item->pending_quantity,
                    'unit_price' => $item->unit_price,
                    'receipt_status' => $item->receipt_status_display,
                ];
            }),
        ]);
    }
}
