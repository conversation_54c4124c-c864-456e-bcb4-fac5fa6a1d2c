<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add employee ID field if it doesn't exist
            if (!Schema::hasColumn('users', 'employee_id')) {
                $table->string('employee_id')->unique()->nullable()->after('id');
            }

            // Add profile photo path if it doesn't exist
            if (!Schema::hasColumn('users', 'profile_photo_path')) {
                $table->string('profile_photo_path')->nullable()->after('email');
            }

            // Add password change tracking if it doesn't exist
            if (!Schema::hasColumn('users', 'password_changed_at')) {
                $table->timestamp('password_changed_at')->nullable()->after('password');
            }

            // Add last login tracking if it doesn't exist (but not last_login_ip as it already exists)
            if (!Schema::hasColumn('users', 'last_login_at')) {
                $table->timestamp('last_login_at')->nullable()->after('password_changed_at');
            }

            // Add failed login attempts tracking if it doesn't exist
            if (!Schema::hasColumn('users', 'failed_login_attempts')) {
                $table->integer('failed_login_attempts')->default(0)->after('last_login_at');
            }

            // Add account lock functionality if it doesn't exist
            if (!Schema::hasColumn('users', 'locked_until')) {
                $table->timestamp('locked_until')->nullable()->after('failed_login_attempts');
            }

            // Add 2FA fields if they don't exist
            if (!Schema::hasColumn('users', 'two_factor_enabled')) {
                $table->boolean('two_factor_enabled')->default(false)->after('locked_until');
            }
            if (!Schema::hasColumn('users', 'two_factor_secret')) {
                $table->text('two_factor_secret')->nullable()->after('two_factor_enabled');
            }
            if (!Schema::hasColumn('users', 'two_factor_recovery_codes')) {
                $table->text('two_factor_recovery_codes')->nullable()->after('two_factor_secret');
            }

            // Add active status if it doesn't exist
            if (!Schema::hasColumn('users', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('two_factor_recovery_codes');
            }

            // Add location relationship if it doesn't exist
            if (!Schema::hasColumn('users', 'location_id')) {
                $table->foreignId('location_id')->nullable()->constrained()->onDelete('set null')->after('is_active');
            }
        });

        // Add indexes separately to avoid conflicts
        Schema::table('users', function (Blueprint $table) {
            // Only add indexes if columns exist and indexes don't already exist
            $indexes = collect(DB::select("SHOW INDEX FROM users"))->pluck('Key_name')->toArray();

            if (Schema::hasColumn('users', 'employee_id') && !in_array('users_employee_id_index', $indexes)) {
                $table->index(['employee_id']);
            }
            if (Schema::hasColumn('users', 'is_active') && !in_array('users_is_active_index', $indexes)) {
                $table->index(['is_active']);
            }
            if (Schema::hasColumn('users', 'location_id') && !in_array('users_location_id_index', $indexes)) {
                $table->index(['location_id']);
            }
            if (Schema::hasColumn('users', 'last_login_at') && !in_array('users_last_login_at_index', $indexes)) {
                $table->index(['last_login_at']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['employee_id']);
            $table->dropIndex(['is_active']);
            $table->dropIndex(['location_id']);
            $table->dropIndex(['last_login_at']);

            // Drop columns
            $table->dropColumn([
                'employee_id',
                'profile_photo_path',
                'password_changed_at',
                'last_login_at',
                'failed_login_attempts',
                'locked_until',
            ]);

            // Only drop these if they were added by this migration
            if (Schema::hasColumn('users', 'two_factor_enabled')) {
                $table->dropColumn('two_factor_enabled');
            }
            if (Schema::hasColumn('users', 'two_factor_secret')) {
                $table->dropColumn('two_factor_secret');
            }
            if (Schema::hasColumn('users', 'two_factor_recovery_codes')) {
                $table->dropColumn('two_factor_recovery_codes');
            }
            if (Schema::hasColumn('users', 'is_active')) {
                $table->dropColumn('is_active');
            }
            if (Schema::hasColumn('users', 'location_id')) {
                $table->dropForeign(['location_id']);
                $table->dropColumn('location_id');
            }
        });
    }
};
