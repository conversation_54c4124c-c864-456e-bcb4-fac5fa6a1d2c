<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user first if it doesn't exist
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::factory()->create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        // Run all seeders in proper order
        $this->call([
            JewelryBusinessSeeder::class,
            SupplierSeeder::class,
            ExpenseCategorySeeder::class,
            MakingChargeTemplateSeeder::class,
            ExpenseSeeder::class,
        ]);
    }
}
