<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Metal;
use App\Models\MetalRate;
use App\Services\MetalRateService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class MetalRateDashboardController extends Controller
{
    protected $metalRateService;

    public function __construct(MetalRateService $metalRateService)
    {
        $this->middleware('permission:view_metal_rate_dashboard');
        $this->metalRateService = $metalRateService;
    }

    /**
     * Display the metal rate dashboard
     */
    public function index()
    {
        // Get current rates for all metals
        $currentRates = $this->metalRateService->getCurrentRates();

        // Get rate alerts
        $alerts = $this->metalRateService->getAdvancedRateAlerts();

        // Get metal comparison
        $metalComparison = $this->metalRateService->getMetalRateComparison();

        // Get inventory impact
        $inventoryImpact = $this->metalRateService->getInventoryRateImpact();

        // Get rate statistics
        $stats = [
            'total_metals' => Metal::active()->count(),
            'metals_with_rates' => Metal::whereHas('rates')->count(),
            'avg_daily_change' => $this->getAverageDailyChange(),
            'most_volatile_metal' => $this->getMostVolatileMetal(),
            'last_update' => MetalRate::latest('created_at')->first()?->created_at,
        ];

        return view('admin.metal-rates.dashboard', compact(
            'currentRates',
            'alerts',
            'metalComparison',
            'inventoryImpact',
            'stats'
        ));
    }

    /**
     * Get rate analytics for specific metal
     */
    public function analytics(Metal $metal, Request $request)
    {
        $days = $request->days ?? 30;

        // Get rate history
        $rateHistory = $this->metalRateService->getRateHistory($metal->id, $days);

        // Get volatility analysis
        $volatility = $this->metalRateService->getRateVolatility($metal->id, $days);

        // Get rate forecast
        $forecast = $this->metalRateService->getRateForecast($metal->id, 7);

        // Get making charge recommendations
        $makingChargeRecommendations = $this->metalRateService->getMakingChargeRecommendations($metal->id);

        // Get pricing recommendations for different weights
        $pricingRecommendations = [];
        $sampleWeights = [1, 5, 10, 20, 50];

        foreach ($sampleWeights as $weight) {
            $pricingRecommendations[] = [
                'weight' => $weight,
                'recommendations' => $this->metalRateService->getPricingRecommendations(
                    $metal->id,
                    $weight,
                    $makingChargeRecommendations['making_charge_percentage']['recommended']
                ),
            ];
        }

        return view('admin.metal-rates.analytics', compact(
            'metal',
            'rateHistory',
            'volatility',
            'forecast',
            'makingChargeRecommendations',
            'pricingRecommendations',
            'days'
        ));
    }

    /**
     * Get rate comparison chart data
     */
    public function getComparisonChartData(Request $request)
    {
        $days = $request->days ?? 30;
        $metalIds = $request->metal_ids ?? Metal::active()->pluck('id')->toArray();

        $chartData = [
            'labels' => [],
            'datasets' => [],
        ];

        // Get date range
        $dates = collect();
        for ($i = $days - 1; $i >= 0; $i--) {
            $dates->push(Carbon::now()->subDays($i)->format('Y-m-d'));
        }

        $chartData['labels'] = $dates->map(function ($date) {
            return Carbon::parse($date)->format('M d');
        })->toArray();

        // Get data for each metal
        $colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899'];
        $colorIndex = 0;

        foreach ($metalIds as $metalId) {
            $metal = Metal::find($metalId);
            if (!$metal) continue;

            $rateData = [];
            foreach ($dates as $date) {
                $rate = $this->metalRateService->getRateForDate($metalId, $date);
                $rateData[] = $rate;
            }

            $chartData['datasets'][] = [
                'label' => $metal->name,
                'data' => $rateData,
                'borderColor' => $colors[$colorIndex % count($colors)],
                'backgroundColor' => $colors[$colorIndex % count($colors)] . '20',
                'tension' => 0.1,
            ];

            $colorIndex++;
        }

        return response()->json($chartData);
    }

    /**
     * Get volatility chart data
     */
    public function getVolatilityChartData(Request $request)
    {
        $metals = Metal::active()->get();
        $days = $request->days ?? 30;

        $chartData = [
            'labels' => [],
            'datasets' => [
                [
                    'label' => 'Volatility %',
                    'data' => [],
                    'backgroundColor' => [],
                ]
            ],
        ];

        foreach ($metals as $metal) {
            $volatility = $this->metalRateService->getRateVolatility($metal->id, $days);

            $chartData['labels'][] = $metal->name;
            $chartData['datasets'][0]['data'][] = $volatility['volatility'];

            // Color based on volatility level
            if ($volatility['volatility'] > 15) {
                $chartData['datasets'][0]['backgroundColor'][] = '#EF4444'; // Red
            } elseif ($volatility['volatility'] > 10) {
                $chartData['datasets'][0]['backgroundColor'][] = '#F59E0B'; // Orange
            } elseif ($volatility['volatility'] > 5) {
                $chartData['datasets'][0]['backgroundColor'][] = '#3B82F6'; // Blue
            } else {
                $chartData['datasets'][0]['backgroundColor'][] = '#10B981'; // Green
            }
        }

        return response()->json($chartData);
    }

    /**
     * Export rate analysis report
     */
    public function exportAnalysis(Request $request)
    {
        $metalId = $request->metal_id;
        $days = $request->days ?? 30;

        if ($metalId) {
            return $this->metalRateService->exportRateHistory($metalId, $days);
        } else {
            return $this->metalRateService->exportRateHistory(null, $days);
        }
    }

    /**
     * Get inventory impact data
     */
    public function getInventoryImpact(Request $request)
    {
        $locationId = $request->location_id;
        $impact = $this->metalRateService->getInventoryRateImpact($locationId);

        return response()->json($impact);
    }

    /**
     * Get making charge calculator data
     */
    public function getMakingChargeCalculator(Request $request)
    {
        $metalId = $request->metal_id;
        $productType = $request->product_type;

        if (!$metalId) {
            return response()->json(['error' => 'Metal ID is required']);
        }

        $recommendations = $this->metalRateService->getMakingChargeRecommendations($metalId, $productType);
        $currentRate = $this->metalRateService->getCurrentRate($metalId);

        return response()->json([
            'current_rate' => $currentRate,
            'recommendations' => $recommendations,
        ]);
    }

    /**
     * Get average daily change across all metals
     */
    private function getAverageDailyChange()
    {
        $rates = $this->metalRateService->getCurrentRates();
        $changes = array_column($rates, 'change_percentage');

        return count($changes) > 0 ? array_sum($changes) / count($changes) : 0;
    }

    /**
     * Get most volatile metal
     */
    private function getMostVolatileMetal()
    {
        $metals = Metal::active()->get();
        $maxVolatility = 0;
        $mostVolatileMetal = null;

        foreach ($metals as $metal) {
            $volatility = $this->metalRateService->getRateVolatility($metal->id, 30);
            if ($volatility['volatility'] > $maxVolatility) {
                $maxVolatility = $volatility['volatility'];
                $mostVolatileMetal = $metal;
            }
        }

        return $mostVolatileMetal;
    }

    /**
     * Update all rates manually
     */
    public function updateAllRates()
    {
        try {
            $success = $this->metalRateService->updateAllRatesFromExternal();

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'All metal rates updated successfully.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update rates from external source.',
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating rates: ' . $e->getMessage(),
            ]);
        }
    }
}
