<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProductResource;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ProductController extends Controller
{
    /**
     * Display a listing of products
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = Product::with(['category', 'metal', 'hsnCode', 'images'])
                       ->where('status', 'active');

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            if (is_numeric($request->category)) {
                $query->where('category_id', $request->category);
            } else {
                $query->whereHas('category', function ($q) use ($request) {
                    $q->where('slug', $request->category);
                });
            }
        }

        // Filter by metal type
        if ($request->filled('metal')) {
            if (is_numeric($request->metal)) {
                $query->where('metal_id', $request->metal);
            } else {
                $query->whereHas('metal', function ($q) use ($request) {
                    $q->where('name', $request->metal);
                });
            }
        }

        // Filter by price range
        if ($request->filled('min_price')) {
            $query->where('total_amount', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('total_amount', '<=', $request->max_price);
        }

        // Filter by weight range
        if ($request->filled('min_weight')) {
            $query->where('gross_weight', '>=', $request->min_weight);
        }
        if ($request->filled('max_weight')) {
            $query->where('gross_weight', '<=', $request->max_weight);
        }

        // Featured products
        if ($request->boolean('featured')) {
            $query->where('is_featured', true);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['name', 'total_amount', 'gross_weight', 'created_at', 'view_count'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = min($request->get('per_page', 15), 50); // Max 50 items per page
        $products = $query->paginate($perPage);

        return ProductResource::collection($products);
    }

    /**
     * Display the specified product
     */
    public function show(Request $request, $identifier)
    {
        $query = Product::with(['category', 'metal', 'hsnCode', 'images', 'stones'])
                       ->where('status', 'active');

        // Find by ID or slug
        if (is_numeric($identifier)) {
            $product = $query->findOrFail($identifier);
        } else {
            $product = $query->where('slug', $identifier)->firstOrFail();
        }

        // Increment view count
        $product->increment('view_count');

        return new ProductResource($product);
    }

    /**
     * Get featured products
     */
    public function featured(Request $request): AnonymousResourceCollection
    {
        $query = Product::with(['category', 'metal', 'images'])
                       ->where('status', 'active')
                       ->where('is_featured', true);

        $limit = min($request->get('limit', 10), 20); // Max 20 featured products
        $products = $query->limit($limit)->get();

        return ProductResource::collection($products);
    }

    /**
     * Get products by category
     */
    public function byCategory(Request $request, $categorySlug): AnonymousResourceCollection
    {
        $query = Product::with(['category', 'metal', 'images'])
                       ->where('status', 'active')
                       ->whereHas('category', function ($q) use ($categorySlug) {
                           $q->where('slug', $categorySlug)->where('is_active', true);
                       });

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['name', 'total_amount', 'gross_weight', 'created_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $perPage = min($request->get('per_page', 15), 50);
        $products = $query->paginate($perPage);

        return ProductResource::collection($products);
    }

    /**
     * Search products
     */
    public function search(Request $request): AnonymousResourceCollection
    {
        $request->validate([
            'q' => 'required|string|min:2|max:100',
        ]);

        $searchTerm = $request->q;
        
        $query = Product::with(['category', 'metal', 'images'])
                       ->where('status', 'active')
                       ->where(function ($q) use ($searchTerm) {
                           $q->where('name', 'like', "%{$searchTerm}%")
                             ->orWhere('description', 'like', "%{$searchTerm}%")
                             ->orWhere('sku', 'like', "%{$searchTerm}%")
                             ->orWhereHas('category', function ($categoryQuery) use ($searchTerm) {
                                 $categoryQuery->where('name', 'like', "%{$searchTerm}%");
                             });
                       });

        $perPage = min($request->get('per_page', 15), 50);
        $products = $query->paginate($perPage);

        return ProductResource::collection($products);
    }

    /**
     * Get related products
     */
    public function related(Request $request, Product $product): AnonymousResourceCollection
    {
        $relatedProducts = Product::with(['category', 'metal', 'images'])
                                 ->where('status', 'active')
                                 ->where('id', '!=', $product->id)
                                 ->where(function ($query) use ($product) {
                                     $query->where('category_id', $product->category_id)
                                           ->orWhere('metal_id', $product->metal_id);
                                 })
                                 ->limit(8)
                                 ->get();

        return ProductResource::collection($relatedProducts);
    }
}
