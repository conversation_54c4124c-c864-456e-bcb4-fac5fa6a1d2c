<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only create tables that don't exist

        // Repair Status History (rename from existing repair_status_history)
        if (!Schema::hasTable('repair_status_histories')) {
            Schema::create('repair_status_histories', function (Blueprint $table) {
                $table->id();
                $table->foreignId('repair_service_id')->constrained()->onDelete('cascade');
                $table->enum('status', ['received', 'in_progress', 'completed', 'delivered', 'cancelled']);
                $table->text('notes')->nullable();
                $table->foreignId('updated_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();

                $table->index(['repair_service_id']);
                $table->index(['status']);
                $table->index(['updated_by']);
            });
        }

        // Repair Items
        if (!Schema::hasTable('repair_items')) {
            Schema::create('repair_items', function (Blueprint $table) {
                $table->id();
                $table->foreignId('repair_service_id')->constrained()->onDelete('cascade');
                $table->string('item_name');
                $table->text('description')->nullable();
                $table->decimal('quantity', 10, 3)->default(1);
                $table->text('condition_before')->nullable();
                $table->text('condition_after')->nullable();
                $table->text('repair_notes')->nullable();
                $table->string('before_photo_path')->nullable();
                $table->string('after_photo_path')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();

                $table->index(['repair_service_id']);
                $table->index(['created_by']);
            });
        }

        // Repair Charges
        if (!Schema::hasTable('repair_charges')) {
            Schema::create('repair_charges', function (Blueprint $table) {
                $table->id();
                $table->foreignId('repair_service_id')->constrained()->onDelete('cascade');
                $table->string('description');
                $table->decimal('amount', 10, 2);
                $table->enum('charge_type', ['labor', 'material', 'parts', 'other'])->default('labor');
                $table->decimal('quantity', 10, 3)->nullable();
                $table->decimal('unit_price', 10, 2)->nullable();
                $table->text('notes')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();

                $table->index(['repair_service_id']);
                $table->index(['charge_type']);
                $table->index(['created_by']);
            });
        }

        // Update existing customer_notifications table if needed
        if (Schema::hasTable('customer_notifications')) {
            Schema::table('customer_notifications', function (Blueprint $table) {
                // Add missing columns if they don't exist
                if (!Schema::hasColumn('customer_notifications', 'title')) {
                    $table->string('title')->after('type');
                }
                if (!Schema::hasColumn('customer_notifications', 'related_type')) {
                    $table->string('related_type')->nullable()->after('message');
                }
                if (!Schema::hasColumn('customer_notifications', 'related_id')) {
                    $table->unsignedBigInteger('related_id')->nullable()->after('related_type');
                }
                if (!Schema::hasColumn('customer_notifications', 'data')) {
                    $table->json('data')->nullable()->after('related_id');
                }
                if (!Schema::hasColumn('customer_notifications', 'read_at')) {
                    $table->timestamp('read_at')->nullable()->after('sent_at');
                }
                if (!Schema::hasColumn('customer_notifications', 'delivery_status')) {
                    $table->enum('delivery_status', ['pending', 'sent', 'delivered', 'failed', 'read'])->default('pending')->after('read_at');
                }
                if (!Schema::hasColumn('customer_notifications', 'delivery_method')) {
                    $table->enum('delivery_method', ['sms', 'email', 'whatsapp', 'push', 'in_app'])->nullable()->after('delivery_status');
                }
                if (!Schema::hasColumn('customer_notifications', 'delivery_response')) {
                    $table->json('delivery_response')->nullable()->after('delivery_method');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only drop tables we created
        Schema::dropIfExists('repair_charges');
        Schema::dropIfExists('repair_items');
        Schema::dropIfExists('repair_status_histories');

        // Revert customer_notifications table changes if needed
        if (Schema::hasTable('customer_notifications')) {
            Schema::table('customer_notifications', function (Blueprint $table) {
                $table->dropColumn([
                    'title', 'related_type', 'related_id', 'data',
                    'read_at', 'delivery_status', 'delivery_method', 'delivery_response'
                ]);
            });
        }
    }
};
