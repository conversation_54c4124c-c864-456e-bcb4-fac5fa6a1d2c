<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Models\User;
use App\Models\Location;
use App\Services\UserActivityService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:manage_users');
    }

    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        $query = User::with(['roles', 'location']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->role($request->role);
        }

        // Filter by location
        if ($request->filled('location')) {
            $query->where('location_id', $request->location);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(15);
        $roles = Role::all();
        $locations = Location::active()->get();

        return view('admin.users.index', compact('users', 'roles', 'locations'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $roles = Role::all();
        $locations = Location::active()->get();
        
        return view('admin.users.create', compact('roles', 'locations'));
    }

    /**
     * Store a newly created user
     */
    public function store(StoreUserRequest $request)
    {
        $validated = $request->validated();

        // Handle profile photo upload
        $profilePhotoPath = null;
        if ($request->hasFile('profile_photo')) {
            $profilePhotoPath = $request->file('profile_photo')->store('profile-photos', 'public');
        }

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'employee_id' => $validated['employee_id'],
            'password' => Hash::make($validated['password']),
            'location_id' => $validated['location_id'],
            'is_active' => $validated['is_active'] ?? true,
            'profile_photo_path' => $profilePhotoPath,
            'email_verified_at' => now(),
            'password_changed_at' => now(),
        ]);

        $user->assignRole($validated['role']);

        // Update the role column for easier querying
        $user->update(['role' => $validated['role']]);

        // Log activity
        UserActivityService::logUserCreated($user);
        UserActivityService::logRoleAssigned($user, [$validated['role']]);

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully with Employee ID: ' . $user->employee_id);
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load(['roles', 'location', 'createdInvoices', 'createdEstimates']);
        
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $locations = Location::active()->get();
        
        return view('admin.users.edit', compact('user', 'roles', 'locations'));
    }

    /**
     * Update the specified user
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        $validated = $request->validated();
        $originalData = $user->toArray();

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            // Delete old photo if exists
            if ($user->profile_photo_path) {
                Storage::disk('public')->delete($user->profile_photo_path);
            }
            $validated['profile_photo_path'] = $request->file('profile_photo')->store('profile-photos', 'public');
        }

        $updateData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
        ];

        // Only allow these fields if user has manage_users permission
        if ($request->user()->can('manage_users') && $request->user()->id !== $user->id) {
            $updateData = array_merge($updateData, [
                'employee_id' => $validated['employee_id'] ?? $user->employee_id,
                'location_id' => $validated['location_id'],
                'is_active' => $validated['is_active'] ?? $user->is_active,
            ]);
        }

        if (isset($validated['profile_photo_path'])) {
            $updateData['profile_photo_path'] = $validated['profile_photo_path'];
        }

        if (!empty($validated['password'])) {
            $updateData['password'] = Hash::make($validated['password']);
            $updateData['password_changed_at'] = now();
        }

        $user->update($updateData);

        // Update role if user has permission and it's not their own profile
        if (isset($validated['role']) && $request->user()->can('manage_users') && $request->user()->id !== $user->id) {
            $oldRoles = $user->getRoleNames()->toArray();
            $user->syncRoles([$validated['role']]);

            // Update the role column for easier querying
            $user->update(['role' => $validated['role']]);

            if ($oldRoles !== [$validated['role']]) {
                UserActivityService::logRoleAssigned($user, [$validated['role']]);
            }
        }

        // Log activity
        $changes = array_diff_assoc($updateData, $originalData);
        if (!empty($changes)) {
            UserActivityService::logUserUpdated($user, $changes);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        if ($user->id === auth()->id()) {
            return back()->with('error', 'You cannot delete your own account.');
        }

        // Check if user has any related records
        $hasInvoices = $user->createdInvoices()->exists();
        $hasEstimates = $user->createdEstimates()->exists();

        if ($hasInvoices || $hasEstimates) {
            return back()->with('error', 'Cannot delete user with existing invoices or estimates. Deactivate the user instead.');
        }

        // Delete profile photo if exists
        if ($user->profile_photo_path) {
            Storage::disk('public')->delete($user->profile_photo_path);
        }

        // Log activity before deletion
        UserActivityService::logUserDeleted($user);

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user status
     */
    public function toggleStatus(User $user)
    {
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot deactivate your own account.'
            ]);
        }

        $newStatus = !$user->is_active;
        $user->update(['is_active' => $newStatus]);

        // Log activity
        UserActivityService::logStatusChanged($user, $newStatus);

        return response()->json([
            'success' => true,
            'message' => 'User status updated successfully.',
            'is_active' => $user->is_active
        ]);
    }

    /**
     * Reset user password
     */
    public function resetPassword(Request $request, User $user)
    {
        $validated = $request->validate([
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user->update([
            'password' => Hash::make($validated['password'])
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password reset successfully.'
        ]);
    }

    /**
     * Show role assignment form
     */
    public function roles(User $user)
    {
        $roles = Role::all();
        $permissions = Permission::all()->groupBy(function ($permission) {
            return explode('_', $permission->name)[1] ?? 'general';
        });

        return view('admin.users.roles', compact('user', 'roles', 'permissions'));
    }

    /**
     * Assign roles to user
     */
    public function assignRoles(Request $request, User $user)
    {
        $request->validate([
            'roles' => 'array',
            'roles.*' => 'exists:roles,name',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        // Sync roles
        if ($request->has('roles')) {
            $user->syncRoles($request->roles);

            // Update the role column with the primary role
            $primaryRole = collect($request->roles)->first();
            if ($primaryRole) {
                $user->update(['role' => $primaryRole]);
            }
        }

        // Sync direct permissions (in addition to role permissions)
        if ($request->has('permissions')) {
            $user->syncPermissions($request->permissions);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User roles and permissions updated successfully.');
    }

    /**
     * Show user permissions
     */
    public function permissions(User $user)
    {
        $userPermissions = $user->getAllPermissions();
        $rolePermissions = $user->getPermissionsViaRoles();
        $directPermissions = $user->getDirectPermissions();

        return view('admin.users.permissions', compact(
            'user',
            'userPermissions',
            'rolePermissions',
            'directPermissions'
        ));
    }

    /**
     * Enable/Disable 2FA for user
     */
    public function toggle2FA(User $user)
    {
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot modify your own 2FA settings from here.'
            ]);
        }

        $newStatus = !$user->two_factor_enabled;

        $user->update([
            'two_factor_enabled' => $newStatus,
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
        ]);

        // Log activity
        UserActivityService::log2FAToggled($user, $newStatus);

        return response()->json([
            'success' => true,
            'message' => '2FA has been ' . ($user->two_factor_enabled ? 'enabled' : 'disabled') . ' for this user.',
            'two_factor_enabled' => $user->two_factor_enabled
        ]);
    }

    /**
     * Lock/Unlock user account
     */
    public function toggleLock(User $user)
    {
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot lock your own account.'
            ]);
        }

        $isLocked = $user->locked_until && $user->locked_until->isFuture();

        if ($isLocked) {
            // Unlock account
            $user->update([
                'locked_until' => null,
                'failed_login_attempts' => 0,
            ]);
            $message = 'User account has been unlocked.';
        } else {
            // Lock account for 24 hours
            $user->update([
                'locked_until' => now()->addDay(),
            ]);
            $message = 'User account has been locked for 24 hours.';
        }

        // Log activity
        UserActivityService::logAccountLockToggled($user, !$isLocked);

        return response()->json([
            'success' => true,
            'message' => $message,
            'is_locked' => !$isLocked
        ]);
    }

    /**
     * Bulk operations on users
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete,assign_role',
            'user_ids' => 'required|array|min:1',
            'user_ids.*' => 'exists:users,id',
            'role' => 'required_if:action,assign_role|exists:roles,name',
        ]);

        $userIds = $request->user_ids;
        $action = $request->action;
        $currentUserId = auth()->id();

        // Remove current user from bulk operations
        $userIds = array_filter($userIds, fn($id) => $id != $currentUserId);

        if (empty($userIds)) {
            return response()->json([
                'success' => false,
                'message' => 'No valid users selected for bulk operation.'
            ]);
        }

        $users = User::whereIn('id', $userIds)->get();
        $successCount = 0;
        $errors = [];

        foreach ($users as $user) {
            try {
                switch ($action) {
                    case 'activate':
                        $user->update(['is_active' => true]);
                        UserActivityService::logStatusChanged($user, true);
                        $successCount++;
                        break;

                    case 'deactivate':
                        $user->update(['is_active' => false]);
                        UserActivityService::logStatusChanged($user, false);
                        $successCount++;
                        break;

                    case 'delete':
                        // Check if user has related records
                        if ($user->createdInvoices()->exists() || $user->createdEstimates()->exists()) {
                            $errors[] = "Cannot delete {$user->name} - has existing records";
                            continue 2;
                        }

                        UserActivityService::logUserDeleted($user);
                        if ($user->profile_photo_path) {
                            Storage::disk('public')->delete($user->profile_photo_path);
                        }
                        $user->delete();
                        $successCount++;
                        break;

                    case 'assign_role':
                        $oldRoles = $user->getRoleNames()->toArray();
                        $user->syncRoles([$request->role]);

                        // Update the role column for easier querying
                        $user->update(['role' => $request->role]);

                        UserActivityService::logRoleAssigned($user, [$request->role]);
                        $successCount++;
                        break;
                }
            } catch (\Exception $e) {
                $errors[] = "Error processing {$user->name}: " . $e->getMessage();
            }
        }

        $message = "Bulk operation completed. {$successCount} users processed successfully.";
        if (!empty($errors)) {
            $message .= " Errors: " . implode(', ', $errors);
        }

        return response()->json([
            'success' => $successCount > 0,
            'message' => $message,
            'processed_count' => $successCount,
            'errors' => $errors
        ]);
    }

    /**
     * Export users data
     */
    public function export(Request $request)
    {
        $query = User::with(['roles', 'location']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->role($request->role);
        }

        if ($request->filled('location')) {
            $query->where('location_id', $request->location);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $users = $query->get();

        $csvData = [];
        $csvData[] = [
            'Employee ID', 'Name', 'Email', 'Phone', 'Role', 'Location',
            'Status', 'Last Login', 'Created At', '2FA Enabled'
        ];

        foreach ($users as $user) {
            $csvData[] = [
                $user->employee_id,
                $user->name,
                $user->email,
                $user->phone,
                $user->getRoleNames()->first() ?? 'No Role',
                $user->location->name ?? 'No Location',
                $user->is_active ? 'Active' : 'Inactive',
                $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : 'Never',
                $user->created_at->format('Y-m-d H:i:s'),
                $user->two_factor_enabled ? 'Yes' : 'No'
            ];
        }

        $filename = 'users_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
}
