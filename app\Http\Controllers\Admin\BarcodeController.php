<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;
use Milon\Barcode\DNS1D;
use Milon\Barcode\DNS2D;
use Barryvdh\DomPDF\Facade\Pdf;

class BarcodeController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:generate_barcodes')->only(['generate', 'generateBatch']);
        $this->middleware('permission:print_barcodes')->only(['print', 'printBatch']);
        $this->middleware('permission:manage_barcode_settings')->only(['settings', 'updateSettings']);
    }

    /**
     * Generate barcode for a product
     */
    public function generate(Product $product, Request $request)
    {
        $type = $request->get('type', 'C128'); // Default to Code 128
        $format = $request->get('format', 'png'); // png, svg, html
        $width = $request->get('width', 2);
        $height = $request->get('height', 30);

        $barcode = $product->barcode ?: $product->sku;

        if ($format === 'svg') {
            $barcodeData = DNS1D::getBarcodeSVG($barcode, $type, $width, $height);
            return response($barcodeData)->header('Content-Type', 'image/svg+xml');
        } elseif ($format === 'html') {
            $barcodeData = DNS1D::getBarcodeHTML($barcode, $type, $width, $height);
            return response($barcodeData)->header('Content-Type', 'text/html');
        } else {
            $barcodeData = DNS1D::getBarcodePNG($barcode, $type, $width, $height);
            return response($barcodeData)->header('Content-Type', 'image/png');
        }
    }

    /**
     * Generate QR code for a product
     */
    public function generateQR(Product $product, Request $request)
    {
        $size = $request->get('size', 5);
        $format = $request->get('format', 'png');

        // Create QR code data with product information
        $qrData = json_encode([
            'id' => $product->id,
            'name' => $product->name,
            'sku' => $product->sku,
            'barcode' => $product->barcode,
            'price' => $product->total_amount,
            'url' => route('admin.products.show', $product)
        ]);

        if ($format === 'svg') {
            $qrCode = DNS2D::getBarcodeSVG($qrData, 'QRCODE', $size, $size);
            return response($qrCode)->header('Content-Type', 'image/svg+xml');
        } elseif ($format === 'html') {
            $qrCode = DNS2D::getBarcodeHTML($qrData, 'QRCODE', $size, $size);
            return response($qrCode)->header('Content-Type', 'text/html');
        } else {
            $qrCode = DNS2D::getBarcodePNG($qrData, 'QRCODE', $size, $size);
            return response($qrCode)->header('Content-Type', 'image/png');
        }
    }

    /**
     * Show barcode generation page
     */
    public function show(Product $product)
    {
        return view('admin.products.barcode', compact('product'));
    }

    /**
     * Generate product labels (barcode + product info)
     */
    public function generateLabel(Product $product, Request $request)
    {
        $quantity = $request->get('quantity', 1);
        $includePrice = $request->boolean('include_price', true);
        $includeQR = $request->boolean('include_qr', false);
        $labelSize = $request->get('label_size', 'small'); // small, medium, large

        $labels = [];
        for ($i = 0; $i < $quantity; $i++) {
            $labels[] = [
                'product' => $product,
                'barcode' => DNS1D::getBarcodeSVG($product->barcode ?: $product->sku, 'C128', 2, 30),
                'qrcode' => $includeQR ? DNS2D::getBarcodeSVG(json_encode([
                    'id' => $product->id,
                    'sku' => $product->sku,
                    'name' => $product->name
                ]), 'QRCODE', 3, 3) : null,
                'include_price' => $includePrice,
                'label_size' => $labelSize
            ];
        }

        $pdf = PDF::loadView('admin.products.labels', compact('labels', 'labelSize'));
        
        // Set paper size based on label size
        switch ($labelSize) {
            case 'large':
                $pdf->setPaper([0, 0, 288, 432], 'portrait'); // 4x6 inches
                break;
            case 'medium':
                $pdf->setPaper([0, 0, 216, 288], 'portrait'); // 3x4 inches
                break;
            default:
                $pdf->setPaper([0, 0, 144, 216], 'portrait'); // 2x3 inches
                break;
        }

        return $pdf->stream("product-labels-{$product->sku}.pdf");
    }

    /**
     * Bulk generate barcodes for multiple products
     */
    public function bulkGenerate(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:products,id',
            'format' => 'in:png,svg,pdf',
            'type' => 'in:barcode,qrcode,both'
        ]);

        $products = Product::whereIn('id', $request->product_ids)->get();
        $format = $request->get('format', 'pdf');
        $type = $request->get('type', 'barcode');

        if ($format === 'pdf') {
            $barcodes = [];
            foreach ($products as $product) {
                $barcodeData = [
                    'product' => $product,
                    'barcode' => null,
                    'qrcode' => null
                ];

                if ($type === 'barcode' || $type === 'both') {
                    $barcodeData['barcode'] = DNS1D::getBarcodeSVG(
                        $product->barcode ?: $product->sku, 
                        'C128', 
                        2, 
                        30
                    );
                }

                if ($type === 'qrcode' || $type === 'both') {
                    $barcodeData['qrcode'] = DNS2D::getBarcodeSVG(
                        json_encode([
                            'id' => $product->id,
                            'sku' => $product->sku,
                            'name' => $product->name
                        ]), 
                        'QRCODE', 
                        3, 
                        3
                    );
                }

                $barcodes[] = $barcodeData;
            }

            $pdf = PDF::loadView('admin.products.bulk-barcodes', compact('barcodes', 'type'));
            return $pdf->stream('bulk-barcodes-' . now()->format('Y-m-d-H-i-s') . '.pdf');
        }

        // For non-PDF formats, create a ZIP file
        $zip = new \ZipArchive();
        $zipFileName = 'barcodes-' . now()->format('Y-m-d-H-i-s') . '.zip';
        $zipPath = storage_path('app/temp/' . $zipFileName);

        // Ensure temp directory exists
        if (!file_exists(dirname($zipPath))) {
            mkdir(dirname($zipPath), 0755, true);
        }

        if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
            foreach ($products as $product) {
                if ($type === 'barcode' || $type === 'both') {
                    $barcodeData = DNS1D::getBarcodePNG($product->barcode ?: $product->sku, 'C128', 2, 30);
                    $zip->addFromString("barcode-{$product->sku}.png", $barcodeData);
                }

                if ($type === 'qrcode' || $type === 'both') {
                    $qrData = json_encode([
                        'id' => $product->id,
                        'sku' => $product->sku,
                        'name' => $product->name
                    ]);
                    $qrCodeData = DNS2D::getBarcodePNG($qrData, 'QRCODE', 5, 5);
                    $zip->addFromString("qrcode-{$product->sku}.png", $qrCodeData);
                }
            }
            $zip->close();

            return response()->download($zipPath)->deleteFileAfterSend(true);
        }

        return back()->with('error', 'Failed to create barcode archive.');
    }

    /**
     * Show bulk barcode generation page
     */
    public function bulkShow(Request $request)
    {
        $query = Product::where('is_active', true);

        // Apply filters if provided
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        if ($request->filled('metal')) {
            $query->where('metal_id', $request->metal);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        $products = $query->with(['category', 'metal'])->paginate(20);
        $categories = \App\Models\Category::where('is_active', true)->get();
        $metals = \App\Models\Metal::where('is_active', true)->get();

        return view('admin.products.bulk-barcode', compact('products', 'categories', 'metals'));
    }

    /**
     * Generate shelf labels with pricing
     */
    public function generateShelfLabel(Product $product, Request $request)
    {
        $quantity = $request->get('quantity', 1);
        $showPrice = $request->boolean('show_price', true);
        $showDiscount = $request->boolean('show_discount', false);
        $discountPercent = $request->get('discount_percent', 0);

        $labels = [];
        for ($i = 0; $i < $quantity; $i++) {
            $labels[] = [
                'product' => $product,
                'barcode' => DNS1D::getBarcodeSVG($product->barcode ?: $product->sku, 'C128', 1.5, 25),
                'show_price' => $showPrice,
                'show_discount' => $showDiscount,
                'discount_percent' => $discountPercent,
                'discounted_price' => $showDiscount ? 
                    $product->total_amount * (1 - $discountPercent / 100) : 
                    $product->total_amount
            ];
        }

        $pdf = PDF::loadView('admin.products.shelf-labels', compact('labels'));
        $pdf->setPaper([0, 0, 216, 144], 'landscape'); // 3x2 inches landscape

        return $pdf->stream("shelf-labels-{$product->sku}.pdf");
    }
}
