<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\InventoryItem;
use App\Models\Location;
use App\Models\Product;
use App\Services\InventoryService;
use App\Services\BarcodeService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class InventoryDashboardController extends Controller
{
    protected $inventoryService;
    protected $barcodeService;

    public function __construct(InventoryService $inventoryService, BarcodeService $barcodeService)
    {
        $this->middleware('permission:view_inventory_dashboard');
        $this->inventoryService = $inventoryService;
        $this->barcodeService = $barcodeService;
    }

    /**
     * Display the inventory dashboard
     */
    public function index(Request $request)
    {
        $locationId = $request->location_id;
        
        // Get comprehensive analytics
        $analytics = $this->inventoryService->getInventoryAnalytics($locationId);
        
        // Get alerts and notifications
        $lowStockAlerts = $this->inventoryService->getLowStockAlerts($locationId);
        $verificationAlerts = $this->inventoryService->getItemsNeedingVerification($locationId);
        $warrantyAlerts = $this->inventoryService->getWarrantyExpiringItems($locationId);
        
        // Get recent activity
        $recentItems = InventoryItem::with(['product', 'location'])
                                  ->when($locationId, function ($query) use ($locationId) {
                                      $query->where('location_id', $locationId);
                                  })
                                  ->orderBy('created_at', 'desc')
                                  ->limit(10)
                                  ->get();
        
        $recentSales = InventoryItem::with(['product', 'location', 'soldToCustomer'])
                                  ->sold()
                                  ->when($locationId, function ($query) use ($locationId) {
                                      $query->where('location_id', $locationId);
                                  })
                                  ->where('sale_date', '>=', now()->subDays(7))
                                  ->orderBy('sale_date', 'desc')
                                  ->limit(10)
                                  ->get();
        
        // Get inventory turnover
        $turnover = $this->inventoryService->getInventoryTurnover($locationId);
        
        // Get slow-moving items
        $slowMovingItems = $this->inventoryService->getSlowMovingInventory($locationId, 180);
        
        // Get fast-moving products
        $fastMovingProducts = $this->inventoryService->getFastMovingInventory($locationId);
        
        // Get barcode statistics
        $barcodeStats = $this->barcodeService->getBarcodeStatistics();
        
        $locations = Location::active()->orderBy('name')->get();

        return view('admin.inventory.dashboard', compact(
            'analytics',
            'lowStockAlerts',
            'verificationAlerts',
            'warrantyAlerts',
            'recentItems',
            'recentSales',
            'turnover',
            'slowMovingItems',
            'fastMovingProducts',
            'barcodeStats',
            'locations',
            'locationId'
        ));
    }

    /**
     * Get inventory analytics chart data
     */
    public function getAnalyticsChartData(Request $request)
    {
        $locationId = $request->location_id;
        $period = $request->period ?? 30;
        
        try {
            // Get daily inventory value over time
            $dates = collect();
            $values = [];
            
            for ($i = $period - 1; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $dates->push($date->format('Y-m-d'));
                
                $dayValue = InventoryItem::whereIn('status', ['available', 'reserved', 'display'])
                                       ->when($locationId, function ($query) use ($locationId) {
                                           $query->where('location_id', $locationId);
                                       })
                                       ->where('created_at', '<=', $date->endOfDay())
                                       ->sum('total_cost');
                
                $values[] = $dayValue;
            }
            
            return response()->json([
                'success' => true,
                'chart_data' => [
                    'labels' => $dates->map(function ($date) {
                        return Carbon::parse($date)->format('M d');
                    })->toArray(),
                    'datasets' => [
                        [
                            'label' => 'Inventory Value',
                            'data' => $values,
                            'borderColor' => '#3B82F6',
                            'backgroundColor' => '#3B82F620',
                            'tension' => 0.1,
                            'fill' => true,
                        ]
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting chart data: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get stock movement chart data
     */
    public function getStockMovementChartData(Request $request)
    {
        $locationId = $request->location_id;
        $days = $request->days ?? 30;
        
        try {
            $dates = collect();
            $stockInData = [];
            $stockOutData = [];
            
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $dates->push($date->format('Y-m-d'));
                
                $stockIn = InventoryItem::when($locationId, function ($query) use ($locationId) {
                                           $query->where('location_id', $locationId);
                                       })
                                       ->whereDate('created_at', $date)
                                       ->count();
                
                $stockOut = InventoryItem::when($locationId, function ($query) use ($locationId) {
                                            $query->where('location_id', $locationId);
                                        })
                                        ->sold()
                                        ->whereDate('sale_date', $date)
                                        ->count();
                
                $stockInData[] = $stockIn;
                $stockOutData[] = $stockOut;
            }
            
            return response()->json([
                'success' => true,
                'chart_data' => [
                    'labels' => $dates->map(function ($date) {
                        return Carbon::parse($date)->format('M d');
                    })->toArray(),
                    'datasets' => [
                        [
                            'label' => 'Stock In',
                            'data' => $stockInData,
                            'backgroundColor' => '#10B981',
                            'borderColor' => '#059669',
                        ],
                        [
                            'label' => 'Stock Out',
                            'data' => $stockOutData,
                            'backgroundColor' => '#EF4444',
                            'borderColor' => '#DC2626',
                        ]
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting stock movement data: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get category distribution chart data
     */
    public function getCategoryDistributionChartData(Request $request)
    {
        $locationId = $request->location_id;
        
        try {
            $query = InventoryItem::with(['product.category'])
                                 ->whereIn('status', ['available', 'reserved', 'display']);
            
            if ($locationId) {
                $query->where('location_id', $locationId);
            }
            
            $items = $query->get();
            $categoryData = $items->groupBy('product.category.name');
            
            $labels = [];
            $data = [];
            $colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#6B7280', '#F97316'];
            
            $colorIndex = 0;
            foreach ($categoryData as $categoryName => $categoryItems) {
                $labels[] = $categoryName;
                $data[] = $categoryItems->count();
                $colorIndex++;
            }
            
            return response()->json([
                'success' => true,
                'chart_data' => [
                    'labels' => $labels,
                    'datasets' => [
                        [
                            'data' => $data,
                            'backgroundColor' => array_slice($colors, 0, count($labels)),
                        ]
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting category distribution: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get inventory valuation report
     */
    public function getValuationReport(Request $request)
    {
        $locationId = $request->location_id;
        $asOfDate = $request->as_of_date ? Carbon::parse($request->as_of_date) : now();
        
        try {
            $valuation = $this->inventoryService->getInventoryValuation($locationId, $asOfDate);
            
            return response()->json([
                'success' => true,
                'valuation' => $valuation,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting valuation report: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get aging report
     */
    public function getAgingReport(Request $request)
    {
        $locationId = $request->location_id;
        
        try {
            $agingReport = $this->inventoryService->generateInventoryReport('aging', $locationId);
            
            return response()->json([
                'success' => true,
                'aging_report' => $agingReport,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting aging report: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get verification report
     */
    public function getVerificationReport(Request $request)
    {
        $locationId = $request->location_id;
        
        try {
            $verificationReport = $this->inventoryService->generateInventoryReport('verification', $locationId);
            
            return response()->json([
                'success' => true,
                'verification_report' => $verificationReport,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting verification report: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Barcode scanner interface
     */
    public function barcodeScanner()
    {
        return view('admin.inventory.barcode-scanner');
    }

    /**
     * Physical verification interface
     */
    public function physicalVerification(Request $request)
    {
        $locationId = $request->location_id;
        
        $items = InventoryItem::with(['product', 'location'])
                             ->whereIn('status', ['available', 'reserved', 'display'])
                             ->when($locationId, function ($query) use ($locationId) {
                                 $query->where('location_id', $locationId);
                             })
                             ->needsVerification()
                             ->orderBy('last_verified_at', 'asc')
                             ->paginate(50);
        
        $locations = Location::active()->orderBy('name')->get();
        
        return view('admin.inventory.physical-verification', compact('items', 'locations', 'locationId'));
    }

    /**
     * Process physical verification
     */
    public function processPhysicalVerification(Request $request)
    {
        $validated = $request->validate([
            'verification_data' => 'required|array',
            'verification_data.*.item_id' => 'required|exists:inventory_items,id',
            'verification_data.*.found' => 'required|boolean',
            'verification_data.*.actual_weight' => 'nullable|numeric|min:0',
            'verification_data.*.condition' => 'nullable|in:new,excellent,good,fair,poor',
            'verification_data.*.shelf_location' => 'nullable|string|max:100',
            'verification_data.*.notes' => 'nullable|string|max:500',
        ]);

        try {
            $itemIds = array_column($validated['verification_data'], 'item_id');
            $verificationData = array_combine($itemIds, $validated['verification_data']);
            
            $results = $this->inventoryService->performPhysicalVerification($itemIds, $verificationData);
            
            return response()->json([
                'success' => true,
                'message' => 'Physical verification completed successfully.',
                'results' => $results,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing verification: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate printable labels
     */
    public function generatePrintableLabels(Request $request)
    {
        $validated = $request->validate([
            'item_ids' => 'required|array|min:1',
            'item_ids.*' => 'exists:inventory_items,id',
            'format' => 'required|in:standard,compact,detailed',
        ]);

        try {
            $items = InventoryItem::with(['product', 'location'])
                                 ->whereIn('id', $validated['item_ids'])
                                 ->get();
            
            $labels = $this->barcodeService->generatePrintableLabels($items, $validated['format']);
            
            return response()->json([
                'success' => true,
                'labels' => $labels,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating labels: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get inventory summary by location
     */
    public function getLocationSummary()
    {
        try {
            $locations = Location::active()->get();
            $summary = [];
            
            foreach ($locations as $location) {
                $analytics = $this->inventoryService->getInventoryAnalytics($location->id);
                
                $summary[] = [
                    'location' => $location,
                    'analytics' => $analytics,
                ];
            }
            
            return response()->json([
                'success' => true,
                'summary' => $summary,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting location summary: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get top performing products
     */
    public function getTopPerformingProducts(Request $request)
    {
        $locationId = $request->location_id;
        $days = $request->days ?? 30;
        $limit = $request->limit ?? 10;
        
        try {
            $startDate = now()->subDays($days);
            
            $topProducts = InventoryItem::select('product_id', 
                                               \DB::raw('COUNT(*) as sales_count'),
                                               \DB::raw('SUM(selling_price) as total_revenue'),
                                               \DB::raw('SUM(profit_amount) as total_profit'))
                                      ->with(['product.category', 'product.metal'])
                                      ->sold()
                                      ->where('sale_date', '>=', $startDate)
                                      ->when($locationId, function ($query) use ($locationId) {
                                          $query->where('location_id', $locationId);
                                      })
                                      ->groupBy('product_id')
                                      ->orderBy('sales_count', 'desc')
                                      ->limit($limit)
                                      ->get();
            
            return response()->json([
                'success' => true,
                'top_products' => $topProducts,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting top products: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get inventory alerts
     */
    public function getInventoryAlerts(Request $request)
    {
        $locationId = $request->location_id;
        
        try {
            $alerts = [
                'low_stock' => $this->inventoryService->getLowStockAlerts($locationId),
                'needs_verification' => $this->inventoryService->getItemsNeedingVerification($locationId),
                'warranty_expiring' => $this->inventoryService->getWarrantyExpiringItems($locationId),
                'damaged_items' => InventoryItem::with(['product', 'location'])
                                                ->where('status', 'damaged')
                                                ->when($locationId, function ($query) use ($locationId) {
                                                    $query->where('location_id', $locationId);
                                                })
                                                ->get(),
            ];
            
            return response()->json([
                'success' => true,
                'alerts' => $alerts,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting alerts: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Export comprehensive inventory report
     */
    public function exportComprehensiveReport(Request $request)
    {
        $locationId = $request->location_id;
        $reportType = $request->report_type ?? 'summary';
        
        try {
            switch ($reportType) {
                case 'valuation':
                    $report = $this->inventoryService->generateInventoryReport('valuation', $locationId);
                    break;
                case 'aging':
                    $report = $this->inventoryService->generateInventoryReport('aging', $locationId);
                    break;
                case 'verification':
                    $report = $this->inventoryService->generateInventoryReport('verification', $locationId);
                    break;
                case 'stock_movement':
                    $startDate = $request->start_date ? Carbon::parse($request->start_date) : now()->subDays(30);
                    $endDate = $request->end_date ? Carbon::parse($request->end_date) : now();
                    $report = $this->inventoryService->generateInventoryReport('stock_movement', $locationId, $startDate, $endDate);
                    break;
                default:
                    $report = $this->inventoryService->getInventoryAnalytics($locationId);
                    break;
            }
            
            return $this->exportReportToCSV($report, $reportType);

        } catch (\Exception $e) {
            return back()->with('error', 'Error exporting report: ' . $e->getMessage());
        }
    }

    /**
     * Export report to CSV
     */
    private function exportReportToCSV($report, $reportType)
    {
        $filename = "inventory-{$reportType}-report-" . now()->format('Y-m-d-H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($report, $reportType) {
            $file = fopen('php://output', 'w');
            
            // Write report data based on type
            switch ($reportType) {
                case 'valuation':
                    fputcsv($file, ['Inventory Valuation Report']);
                    fputcsv($file, ['Generated:', now()->format('Y-m-d H:i:s')]);
                    fputcsv($file, []);
                    fputcsv($file, ['Total Items:', $report['total_items']]);
                    fputcsv($file, ['Cost Value:', number_format($report['cost_value'], 2)]);
                    fputcsv($file, ['Selling Value:', number_format($report['selling_value'], 2)]);
                    fputcsv($file, ['Potential Profit:', number_format($report['potential_profit'], 2)]);
                    break;
                    
                case 'aging':
                    fputcsv($file, ['Inventory Aging Report']);
                    fputcsv($file, ['Generated:', now()->format('Y-m-d H:i:s')]);
                    fputcsv($file, []);
                    fputcsv($file, ['Age Range', 'Item Count', 'Total Value']);
                    
                    foreach ($report['aging_buckets'] as $bucket => $data) {
                        fputcsv($file, [
                            $bucket . ' days',
                            $data['count'],
                            number_format($data['value'], 2)
                        ]);
                    }
                    break;
                    
                default:
                    fputcsv($file, ['Inventory Summary Report']);
                    fputcsv($file, ['Generated:', now()->format('Y-m-d H:i:s')]);
                    fputcsv($file, []);
                    
                    foreach ($report as $key => $value) {
                        if (is_numeric($value)) {
                            fputcsv($file, [ucwords(str_replace('_', ' ', $key)), number_format($value, 2)]);
                        }
                    }
                    break;
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
