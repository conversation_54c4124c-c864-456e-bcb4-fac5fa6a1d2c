<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PaymentMethod;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            ['name' => 'Cash', 'code' => 'CASH', 'is_active' => true],
            ['name' => 'Card', 'code' => 'CARD', 'is_active' => true],
            ['name' => 'UPI', 'code' => 'UPI', 'is_active' => true],
            ['name' => 'Bank Transfer', 'code' => 'BANK', 'is_active' => true],
            ['name' => 'Cheque', 'code' => 'CHEQUE', 'is_active' => true],
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::firstOrCreate(
                ['code' => $method['code']],
                $method
            );
        }

        $this->command->info('Payment methods seeded successfully!');
    }
}
