<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Customer;
use App\Models\Location;

class GstCalculationService
{
    /**
     * Calculate GST for a single invoice item
     */
    public function calculateItemGst(array $itemData, Product $product, Customer $customer = null, Location $location = null)
    {
        $quantity = $itemData['quantity'];
        $unitPrice = $itemData['unit_price'];
        $discountPercentage = $itemData['discount_percentage'] ?? 0;
        $makingCharges = $itemData['making_charges'] ?? 0;
        $stoneCharges = $itemData['stone_charges'] ?? 0;

        // Calculate base amount
        $baseAmount = ($unitPrice * $quantity) + $makingCharges + $stoneCharges;
        
        // Calculate discount
        $discountAmount = ($baseAmount * $discountPercentage) / 100;
        $taxableAmount = $baseAmount - $discountAmount;

        // Get GST rate from product or default to 3% for jewelry
        $gstRate = $product->gst_rate ?? 3;

        // Determine if same state or different state
        $isSameState = $this->isSameState($customer, $location);

        if ($isSameState) {
            // Same state - CGST + SGST
            $cgstRate = $gstRate / 2;
            $sgstRate = $gstRate / 2;
            $cgstAmount = ($taxableAmount * $cgstRate) / 100;
            $sgstAmount = ($taxableAmount * $sgstRate) / 100;
            $igstAmount = 0;
        } else {
            // Different state - IGST
            $cgstAmount = 0;
            $sgstAmount = 0;
            $igstAmount = ($taxableAmount * $gstRate) / 100;
        }

        $totalGst = $cgstAmount + $sgstAmount + $igstAmount;
        $totalAmount = $taxableAmount + $totalGst;

        return [
            'base_amount' => $baseAmount,
            'discount_amount' => $discountAmount,
            'taxable_amount' => $taxableAmount,
            'gst_rate' => $gstRate,
            'cgst_amount' => $cgstAmount,
            'sgst_amount' => $sgstAmount,
            'igst_amount' => $igstAmount,
            'total_gst' => $totalGst,
            'total_amount' => $totalAmount,
        ];
    }

    /**
     * Calculate total GST for entire invoice
     */
    public function calculateInvoiceTotal(array $invoiceData)
    {
        $items = $invoiceData['items'] ?? [];
        $discountAmount = $invoiceData['discount_amount'] ?? 0;
        $shippingCharges = $invoiceData['shipping_charges'] ?? 0;

        $subtotal = 0;
        $totalCgst = 0;
        $totalSgst = 0;
        $totalIgst = 0;

        // Get customer and location for state comparison
        $customer = null;
        $location = null;
        
        if (isset($invoiceData['customer_id'])) {
            $customer = Customer::find($invoiceData['customer_id']);
        }
        
        if (isset($invoiceData['location_id'])) {
            $location = Location::find($invoiceData['location_id']);
        }

        foreach ($items as $itemData) {
            $product = Product::find($itemData['product_id']);
            $itemCalculation = $this->calculateItemGst($itemData, $product, $customer, $location);
            
            $subtotal += $itemCalculation['base_amount'];
            $totalCgst += $itemCalculation['cgst_amount'];
            $totalSgst += $itemCalculation['sgst_amount'];
            $totalIgst += $itemCalculation['igst_amount'];
        }

        // Apply invoice-level discount
        $discountedSubtotal = $subtotal - $discountAmount;
        
        // Recalculate GST on discounted amount if discount is applied
        if ($discountAmount > 0) {
            $discountRatio = $discountedSubtotal / $subtotal;
            $totalCgst *= $discountRatio;
            $totalSgst *= $discountRatio;
            $totalIgst *= $discountRatio;
        }

        $totalGst = $totalCgst + $totalSgst + $totalIgst;
        $totalBeforeRounding = $discountedSubtotal + $totalGst + $shippingCharges;
        $roundedTotal = round($totalBeforeRounding);
        $roundOffAmount = $roundedTotal - $totalBeforeRounding;

        return [
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'shipping_charges' => $shippingCharges,
            'taxable_amount' => $discountedSubtotal,
            'cgst_amount' => $totalCgst,
            'sgst_amount' => $totalSgst,
            'igst_amount' => $totalIgst,
            'total_gst' => $totalGst,
            'round_off_amount' => $roundOffAmount,
            'total_amount' => $roundedTotal,
        ];
    }

    /**
     * Check if customer and company are in same state
     */
    private function isSameState(Customer $customer = null, Location $location = null)
    {
        if (!$customer || !$location) {
            return true; // Default to same state if information not available
        }

        $customerState = $customer->state ?? 'Unknown';
        $locationState = $location->state ?? 'Unknown';

        return strtolower(trim($customerState)) === strtolower(trim($locationState));
    }

    /**
     * Get GST breakdown for display
     */
    public function getGstBreakdown(array $invoiceData)
    {
        $calculation = $this->calculateInvoiceTotal($invoiceData);
        
        $breakdown = [
            'subtotal' => $calculation['subtotal'],
            'discount' => $calculation['discount_amount'],
            'taxable_amount' => $calculation['taxable_amount'],
            'shipping' => $calculation['shipping_charges'],
        ];

        if ($calculation['cgst_amount'] > 0) {
            $breakdown['cgst'] = $calculation['cgst_amount'];
            $breakdown['sgst'] = $calculation['sgst_amount'];
        }

        if ($calculation['igst_amount'] > 0) {
            $breakdown['igst'] = $calculation['igst_amount'];
        }

        $breakdown['total_gst'] = $calculation['total_gst'];
        $breakdown['round_off'] = $calculation['round_off_amount'];
        $breakdown['total'] = $calculation['total_amount'];

        return $breakdown;
    }

    /**
     * Validate GST number format
     */
    public function validateGstNumber($gstNumber)
    {
        if (empty($gstNumber)) {
            return false;
        }

        // GST number format: 15 characters
        // First 2: State code
        // Next 10: PAN number
        // 12th: Entity number
        // 13th: Check digit
        // Last 2: Default 'Z' and check digit
        $pattern = '/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/';
        
        return preg_match($pattern, $gstNumber);
    }

    /**
     * Get HSN code for product
     */
    public function getHsnCode(Product $product)
    {
        // Check if product has specific HSN code
        if ($product->hsn_code) {
            return $product->hsn_code;
        }

        // Check category HSN code
        if ($product->category && $product->category->hsn_code_id) {
            return $product->category->hsnCode->code ?? '7113'; // Default jewelry HSN
        }

        // Default HSN code for jewelry
        return '7113';
    }

    /**
     * Calculate making charges based on weight and rate
     */
    public function calculateMakingCharges(Product $product, $makingRate = null)
    {
        if (!$makingRate) {
            $makingRate = $product->making_charges_per_gram ?? 0;
        }

        $weight = $product->net_weight ?? $product->gross_weight ?? 0;
        
        return $weight * $makingRate;
    }

    /**
     * Get current metal rate for pricing
     */
    public function getCurrentMetalRate($metalId)
    {
        // This would integrate with metal rates API or database
        // For now, return a default rate
        return 5000; // Default rate per gram
    }

    /**
     * Calculate dynamic pricing based on current metal rates
     */
    public function calculateDynamicPrice(Product $product)
    {
        $metalRate = $this->getCurrentMetalRate($product->metal_id);
        $weight = $product->net_weight ?? 0;
        $makingCharges = $this->calculateMakingCharges($product);
        $stoneCharges = $product->stone_charges ?? 0;

        $metalValue = $weight * $metalRate;
        $totalPrice = $metalValue + $makingCharges + $stoneCharges;

        return [
            'metal_rate' => $metalRate,
            'metal_value' => $metalValue,
            'making_charges' => $makingCharges,
            'stone_charges' => $stoneCharges,
            'total_price' => $totalPrice,
        ];
    }
}
