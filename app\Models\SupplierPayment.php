<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupplierPayment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'payment_number',
        'supplier_id',
        'purchase_order_id',
        'amount',
        'payment_date',
        'payment_method',
        'reference_number',
        'bank_details',
        'status',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date',
        'bank_details' => 'array',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (!$payment->payment_number) {
                $payment->payment_number = static::generatePaymentNumber();
            }
            $payment->created_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scopes
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('payment_date', now()->month)
                    ->whereYear('payment_date', now()->year);
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'pending' => 'Pending',
            'processing' => 'Processing',
            'completed' => 'Completed',
            'failed' => 'Failed',
            'cancelled' => 'Cancelled',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'yellow',
            'processing' => 'blue',
            'completed' => 'green',
            'failed' => 'red',
            'cancelled' => 'gray',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getPaymentMethodDisplayAttribute()
    {
        $methods = [
            'cash' => 'Cash',
            'bank_transfer' => 'Bank Transfer',
            'cheque' => 'Cheque',
            'upi' => 'UPI',
            'card' => 'Card',
            'online' => 'Online Payment',
        ];

        return $methods[$this->payment_method] ?? ucfirst(str_replace('_', ' ', $this->payment_method));
    }

    public function getBankDetailsDisplayAttribute()
    {
        if (!$this->bank_details) {
            return 'N/A';
        }

        $details = [];
        if (isset($this->bank_details['bank_name'])) {
            $details[] = $this->bank_details['bank_name'];
        }
        if (isset($this->bank_details['account_number'])) {
            $details[] = 'A/C: ' . substr($this->bank_details['account_number'], -4);
        }
        if (isset($this->bank_details['transaction_id'])) {
            $details[] = 'TXN: ' . $this->bank_details['transaction_id'];
        }

        return implode(' | ', $details);
    }

    /**
     * Business Logic Methods
     */
    public static function generatePaymentNumber()
    {
        $prefix = 'PAY';
        $year = now()->format('y');
        $month = now()->format('m');

        $lastPayment = static::whereYear('created_at', now()->year)
                            ->whereMonth('created_at', now()->month)
                            ->orderBy('id', 'desc')
                            ->first();

        $sequence = $lastPayment ? (int) substr($lastPayment->payment_number, -4) + 1 : 1;

        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function markAsCompleted($transactionId = null, $notes = null)
    {
        $updateData = [
            'status' => 'completed',
            'payment_date' => now(),
        ];

        if ($transactionId) {
            $bankDetails = $this->bank_details ?: [];
            $bankDetails['transaction_id'] = $transactionId;
            $updateData['bank_details'] = $bankDetails;
        }

        if ($notes) {
            $updateData['notes'] = $this->notes ? ($this->notes . "\n\n" . $notes) : $notes;
        }

        $this->update($updateData);

        // Log the completion
        activity()
            ->performedOn($this)
            ->withProperties([
                'transaction_id' => $transactionId,
                'completion_notes' => $notes,
            ])
            ->log('Supplier payment completed');

        return $this;
    }

    public function markAsFailed($reason = null)
    {
        $this->update([
            'status' => 'failed',
            'notes' => $reason ? ($this->notes ? ($this->notes . "\n\nFailure Reason: " . $reason) : "Failure Reason: " . $reason) : $this->notes,
        ]);

        // Log the failure
        activity()
            ->performedOn($this)
            ->withProperties(['failure_reason' => $reason])
            ->log('Supplier payment failed');

        return $this;
    }

    public function cancel($reason = null)
    {
        if ($this->status === 'completed') {
            throw new \Exception('Cannot cancel completed payment');
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? ($this->notes ? ($this->notes . "\n\nCancellation Reason: " . $reason) : "Cancellation Reason: " . $reason) : $this->notes,
        ]);

        // Log the cancellation
        activity()
            ->performedOn($this)
            ->withProperties(['cancellation_reason' => $reason])
            ->log('Supplier payment cancelled');

        return $this;
    }

    public function updateBankDetails($bankDetails)
    {
        $this->update(['bank_details' => array_merge($this->bank_details ?: [], $bankDetails)]);

        return $this;
    }

    public function getSummary()
    {
        return [
            'payment_number' => $this->payment_number,
            'supplier_name' => $this->supplier->name,
            'purchase_order_number' => $this->purchaseOrder?->po_number,
            'amount' => $this->amount,
            'payment_date' => $this->payment_date?->format('Y-m-d'),
            'payment_method' => $this->payment_method_display,
            'reference_number' => $this->reference_number,
            'bank_details' => $this->bank_details_display,
            'status' => $this->status_display,
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'notes' => $this->notes,
        ];
    }
}
