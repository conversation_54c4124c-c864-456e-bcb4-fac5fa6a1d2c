<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;

class CartController extends Controller
{
    /**
     * Display shopping cart
     */
    public function index()
    {
        $cart = $this->getCart();
        $cartItems = $this->getCartItems($cart);
        $cartSummary = $this->getCartSummary($cartItems);

        return view('website.cart.index', compact('cart', 'cartItems', 'cartSummary'));
    }

    /**
     * Add item to cart
     */
    public function add(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'customization' => 'nullable|array',
        ]);

        $product = Product::findOrFail($validated['product_id']);

        // Check if product is available
        if (!$product->is_active || $product->stock_quantity < $validated['quantity']) {
            return response()->json([
                'success' => false,
                'message' => 'Product is not available in the requested quantity.',
            ], 400);
        }

        $cart = $this->getCart();
        $cartKey = $this->generateCartKey($product->id, $validated['customization'] ?? []);

        // Check if item already exists in cart
        if (isset($cart[$cartKey])) {
            $newQuantity = $cart[$cartKey]['quantity'] + $validated['quantity'];

            // Check stock availability
            if ($newQuantity > $product->stock_quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Not enough stock available. Maximum quantity: ' . $product->stock_quantity,
                ], 400);
            }

            $cart[$cartKey]['quantity'] = $newQuantity;
        } else {
            $cart[$cartKey] = [
                'product_id' => $product->id,
                'quantity' => $validated['quantity'],
                'price' => $product->selling_price,
                'customization' => $validated['customization'] ?? [],
                'added_at' => now()->toISOString(),
            ];
        }

        $this->saveCart($cart);

        $cartSummary = $this->getCartSummary($this->getCartItems($cart));

        return response()->json([
            'success' => true,
            'message' => 'Product added to cart successfully.',
            'cart_count' => $cartSummary['total_items'],
            'cart_total' => $cartSummary['formatted_total'],
        ]);
    }

    /**
     * Update cart item quantity
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'cart_key' => 'required|string',
            'quantity' => 'required|integer|min:0',
        ]);

        $cart = $this->getCart();

        if (!isset($cart[$validated['cart_key']])) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found.',
            ], 404);
        }

        $cartItem = $cart[$validated['cart_key']];
        $product = Product::find($cartItem['product_id']);

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found.',
            ], 404);
        }

        // Remove item if quantity is 0
        if ($validated['quantity'] == 0) {
            unset($cart[$validated['cart_key']]);
        } else {
            // Check stock availability
            if ($validated['quantity'] > $product->stock_quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Not enough stock available. Maximum quantity: ' . $product->stock_quantity,
                ], 400);
            }

            $cart[$validated['cart_key']]['quantity'] = $validated['quantity'];
        }

        $this->saveCart($cart);

        $cartItems = $this->getCartItems($cart);
        $cartSummary = $this->getCartSummary($cartItems);

        return response()->json([
            'success' => true,
            'message' => 'Cart updated successfully.',
            'cart_count' => $cartSummary['total_items'],
            'cart_total' => $cartSummary['formatted_total'],
            'item_total' => $validated['quantity'] > 0 ?
                '₹' . number_format($validated['quantity'] * $cartItem['price'], 2) : '₹0.00',
        ]);
    }

    /**
     * Remove item from cart
     */
    public function remove(Request $request)
    {
        $validated = $request->validate([
            'cart_key' => 'required|string',
        ]);

        $cart = $this->getCart();

        if (!isset($cart[$validated['cart_key']])) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found.',
            ], 404);
        }

        unset($cart[$validated['cart_key']]);
        $this->saveCart($cart);

        $cartItems = $this->getCartItems($cart);
        $cartSummary = $this->getCartSummary($cartItems);

        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart.',
            'cart_count' => $cartSummary['total_items'],
            'cart_total' => $cartSummary['formatted_total'],
        ]);
    }

    /**
     * Clear entire cart
     */
    public function clear()
    {
        Session::forget('cart');

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully.',
            'cart_count' => 0,
            'cart_total' => '₹0.00',
        ]);
    }

    /**
     * Get cart count (for header display)
     */
    public function count()
    {
        $cart = $this->getCart();
        $cartItems = $this->getCartItems($cart);
        $cartSummary = $this->getCartSummary($cartItems);

        return response()->json([
            'count' => $cartSummary['total_items'],
            'total' => $cartSummary['formatted_total'],
        ]);
    }

    /**
     * Proceed to checkout
     */
    public function checkout()
    {
        $cart = $this->getCart();

        if (empty($cart)) {
            return redirect()->route('website.cart')
                           ->with('error', 'Your cart is empty.');
        }

        $cartItems = $this->getCartItems($cart);
        $cartSummary = $this->getCartSummary($cartItems);

        // Validate stock availability
        foreach ($cartItems as $item) {
            if ($item['quantity'] > $item['product']->stock_quantity) {
                return redirect()->route('website.cart')
                               ->with('error', "Insufficient stock for {$item['product']->name}. Available: {$item['product']->stock_quantity}");
            }
        }

        // Get authenticated user data for pre-filling form
        $user = auth()->user();

        return view('website.cart.checkout', compact('cartItems', 'cartSummary', 'user'));
    }

    /**
     * Process checkout form submission
     */
    public function processCheckout(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'address_line_1' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:2',
            'delivery_method' => 'required|in:standard,express,pickup',
        ]);

        $cart = $this->getCart();

        if (empty($cart)) {
            return redirect()->route('website.cart.index')
                           ->with('error', 'Your cart is empty.');
        }

        $cartItems = $this->getCartItems($cart);
        $cartSummary = $this->getCartSummary($cartItems);

        // Calculate shipping cost
        $shippingCost = 0;
        if ($request->delivery_method === 'express') {
            $shippingCost = 500;
        }

        // Get authenticated user
        $user = auth()->user();

        // Store checkout data in session for payment processing
        session([
            'checkout_data' => [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_email' => $user->email,
                'customer' => $request->only(['first_name', 'last_name', 'email', 'phone']),
                'shipping' => $request->only(['address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country']),
                'delivery_method' => $request->delivery_method,
                'special_instructions' => $request->special_instructions,
                'cart_items' => $cartItems,
                'cart_summary' => $cartSummary,
                'shipping_cost' => $shippingCost,
                'total_amount' => $cartSummary['total'] + $shippingCost,
                'order_date' => now(),
            ]
        ]);

        // For now, redirect to a success page (in a real app, this would go to payment gateway)
        return redirect()->route('website.cart.success')
                       ->with('success', 'Order placed successfully! You will receive a confirmation email shortly.');
    }

    /**
     * Show order success page
     */
    public function success()
    {
        $checkoutData = session('checkout_data');

        if (!$checkoutData) {
            return redirect()->route('website.home')
                           ->with('error', 'No order data found.');
        }

        // Clear cart after successful order
        session()->forget(['cart', 'checkout_data']);

        return view('website.cart.success', compact('checkoutData'));
    }

    /**
     * Helper methods
     */
    protected function getCart()
    {
        return Session::get('cart', []);
    }

    protected function saveCart($cart)
    {
        Session::put('cart', $cart);
    }

    protected function generateCartKey($productId, $customization = [])
    {
        return $productId . '_' . md5(serialize($customization));
    }

    protected function getCartItems($cart)
    {
        $items = [];
        $productIds = array_column($cart, 'product_id');
        $products = Product::whereIn('id', $productIds)->get()->keyBy('id');

        foreach ($cart as $cartKey => $item) {
            if (isset($products[$item['product_id']])) {
                $product = $products[$item['product_id']];
                $items[$cartKey] = [
                    'cart_key' => $cartKey,
                    'product' => $product,
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'total' => $item['quantity'] * $item['price'],
                    'customization' => $item['customization'],
                ];
            }
        }

        return $items;
    }

    protected function getCartSummary($cartItems)
    {
        $subtotal = array_sum(array_column($cartItems, 'total'));
        $taxRate = 0.18; // 18% GST
        $taxAmount = $subtotal * $taxRate;
        $total = $subtotal + $taxAmount;

        return [
            'total_items' => array_sum(array_column($cartItems, 'quantity')),
            'subtotal' => $subtotal,
            'tax_rate' => $taxRate * 100,
            'tax_amount' => $taxAmount,
            'total' => $total,
            'formatted_subtotal' => '₹' . number_format($subtotal, 2),
            'formatted_tax' => '₹' . number_format($taxAmount, 2),
            'formatted_total' => '₹' . number_format($total, 2),
        ];
    }
}
