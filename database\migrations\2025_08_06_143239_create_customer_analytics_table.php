<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_analytics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');

            // Purchase analytics
            $table->integer('total_orders')->default(0);
            $table->decimal('total_purchase_value', 15, 2)->default(0);
            $table->decimal('average_order_value', 12, 2)->default(0);
            $table->decimal('highest_order_value', 12, 2)->default(0);
            $table->decimal('lowest_order_value', 12, 2)->default(0);
            $table->integer('days_since_last_purchase')->nullable();
            $table->integer('purchase_frequency_days')->nullable(); // Average days between purchases

            // Product preferences (calculated from purchases)
            $table->json('top_categories')->nullable(); // Most purchased categories
            $table->json('top_metals')->nullable(); // Most purchased metals
            $table->json('top_product_types')->nullable(); // Most purchased product types
            $table->string('preferred_price_range')->nullable(); // budget, mid-range, premium, luxury

            // Behavioral analytics
            $table->integer('total_visits')->default(0);
            $table->integer('total_inquiries')->default(0);
            $table->decimal('conversion_rate', 5, 2)->default(0); // Purchases / Visits * 100
            $table->integer('average_decision_time_days')->nullable(); // Days from inquiry to purchase
            $table->enum('customer_lifecycle_stage', ['new', 'active', 'at_risk', 'dormant', 'lost'])->default('new');

            // Engagement analytics
            $table->integer('email_opens')->default(0);
            $table->integer('email_clicks')->default(0);
            $table->integer('sms_responses')->default(0);
            $table->integer('whatsapp_interactions')->default(0);
            $table->decimal('engagement_score', 5, 2)->default(0); // Overall engagement score

            // Loyalty metrics
            $table->integer('loyalty_points')->default(0);
            $table->integer('referrals_made')->default(0);
            $table->integer('referrals_converted')->default(0);
            $table->boolean('is_repeat_customer')->default(false);
            $table->integer('months_as_customer')->default(0);

            // Risk analytics
            $table->decimal('credit_utilization_percentage', 5, 2)->default(0);
            $table->integer('payment_delays')->default(0);
            $table->decimal('payment_delay_average_days', 8, 2)->default(0);
            $table->enum('payment_behavior', ['excellent', 'good', 'fair', 'poor'])->default('good');
            $table->enum('risk_level', ['low', 'medium', 'high'])->default('low');

            // Seasonal patterns
            $table->json('seasonal_purchase_pattern')->nullable(); // Month-wise purchase data
            $table->json('festival_purchase_pattern')->nullable(); // Festival-wise purchase data
            $table->string('peak_shopping_month')->nullable();
            $table->string('peak_shopping_season')->nullable();

            // Customer value metrics
            $table->decimal('customer_lifetime_value', 15, 2)->default(0);
            $table->decimal('predicted_next_purchase_value', 12, 2)->nullable();
            $table->date('predicted_next_purchase_date')->nullable();
            $table->enum('value_segment', ['low', 'medium', 'high', 'vip'])->default('low');

            // Satisfaction metrics
            $table->decimal('average_rating', 3, 2)->nullable(); // If you have rating system
            $table->integer('complaints_count')->default(0);
            $table->integer('compliments_count')->default(0);
            $table->decimal('satisfaction_score', 5, 2)->nullable();

            // Last calculation timestamp
            $table->timestamp('last_calculated_at')->nullable();
            $table->timestamp('next_calculation_due')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index(['customer_id']);
            $table->index(['customer_lifecycle_stage']);
            $table->index(['value_segment']);
            $table->index(['risk_level']);
            $table->index(['total_purchase_value']);
            $table->index(['last_calculated_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_analytics');
    }
};
