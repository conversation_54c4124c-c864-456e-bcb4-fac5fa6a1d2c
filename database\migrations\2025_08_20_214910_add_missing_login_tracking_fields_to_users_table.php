<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add missing login tracking fields
            if (!Schema::hasColumn('users', 'last_login_ip')) {
                $table->string('last_login_ip')->nullable()->after('last_login_at');
            }

            if (!Schema::hasColumn('users', 'password_changed_at')) {
                $table->timestamp('password_changed_at')->nullable()->after('password');
            }

            if (!Schema::hasColumn('users', 'failed_login_attempts')) {
                $table->integer('failed_login_attempts')->default(0)->after('last_login_ip');
            }

            if (!Schema::hasColumn('users', 'locked_until')) {
                $table->timestamp('locked_until')->nullable()->after('failed_login_attempts');
            }

            // Add 2FA fields if they don't exist
            if (!Schema::hasColumn('users', 'two_factor_enabled')) {
                $table->boolean('two_factor_enabled')->default(false)->after('locked_until');
            }

            if (!Schema::hasColumn('users', 'two_factor_secret')) {
                $table->text('two_factor_secret')->nullable()->after('two_factor_enabled');
            }

            if (!Schema::hasColumn('users', 'two_factor_recovery_codes')) {
                $table->text('two_factor_recovery_codes')->nullable()->after('two_factor_secret');
            }
        });

        // Add indexes for performance
        Schema::table('users', function (Blueprint $table) {
            $indexes = collect(\DB::select("SHOW INDEX FROM users"))->pluck('Key_name')->toArray();

            if (!in_array('users_failed_login_attempts_index', $indexes)) {
                $table->index(['failed_login_attempts']);
            }
            if (!in_array('users_locked_until_index', $indexes)) {
                $table->index(['locked_until']);
            }
            if (!in_array('users_two_factor_enabled_index', $indexes)) {
                $table->index(['two_factor_enabled']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['failed_login_attempts']);
            $table->dropIndex(['locked_until']);
            $table->dropIndex(['two_factor_enabled']);

            // Drop columns
            $table->dropColumn([
                'last_login_ip',
                'password_changed_at',
                'failed_login_attempts',
                'locked_until',
                'two_factor_enabled',
                'two_factor_secret',
                'two_factor_recovery_codes',
            ]);
        });
    }
};
