<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\CustomerInteraction;
use App\Models\LoyaltyPoint;
use App\Models\LoyaltyRedemption;
use App\Models\MarketingCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CRMController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_crm')->only(['index', 'dashboard']);
        $this->middleware('permission:manage_customer_interactions')->only(['interactions', 'addInteraction']);
        $this->middleware('permission:manage_loyalty_program')->only(['loyalty', 'awardPoints', 'redeemPoints']);
        $this->middleware('permission:manage_marketing_campaigns')->only(['campaigns', 'createCampaign']);
    }

    /**
     * CRM Dashboard
     */
    public function index()
    {
        $stats = $this->getCRMStats();
        $recentInteractions = $this->getRecentInteractions();
        $upcomingFollowUps = $this->getUpcomingFollowUps();
        $customerSegments = $this->getCustomerSegments();

        return view('admin.crm.index', compact(
            'stats',
            'recentInteractions',
            'upcomingFollowUps',
            'customerSegments'
        ));
    }

    /**
     * Customer 360 View
     */
    public function customerProfile(Customer $customer)
    {
        $customer->load([
            'preferences',
            'analytics',
            'interactions.createdBy',
            'loyaltyPoints',
            'loyaltyRedemptions',
            'invoices',
            'repairServices',
            'posTransactions'
        ]);

        $purchaseHistory = $this->getCustomerPurchaseHistory($customer);
        $interactionTimeline = $this->getCustomerInteractionTimeline($customer);
        $loyaltySummary = $this->getCustomerLoyaltySummary($customer);
        $recommendations = $this->getCustomerRecommendations($customer);

        return view('admin.crm.customer-profile', compact(
            'customer',
            'purchaseHistory',
            'interactionTimeline',
            'loyaltySummary',
            'recommendations'
        ));
    }

    /**
     * Customer Interactions Management
     */
    public function interactions(Request $request)
    {
        $query = CustomerInteraction::with(['customer', 'createdBy', 'assignedTo']);

        // Search and filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($cq) use ($search) {
                      $cq->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('type')) {
            $query->where('interaction_type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        $interactions = $query->latest('interaction_date')->paginate(20);

        return view('admin.crm.interactions', compact('interactions'));
    }

    /**
     * Add Customer Interaction
     */
    public function addInteraction(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'interaction_type' => 'required|string',
            'channel' => 'required|string',
            'subject' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => 'required|in:low,normal,high,urgent',
            'follow_up_required' => 'boolean',
            'follow_up_date' => 'nullable|date|after:today',
            'assigned_to' => 'nullable|exists:users,id',
            'duration_minutes' => 'nullable|integer|min:1',
        ]);

        try {
            $interaction = CustomerInteraction::create([
                'customer_id' => $validated['customer_id'],
                'interaction_type' => $validated['interaction_type'],
                'channel' => $validated['channel'],
                'subject' => $validated['subject'],
                'description' => $validated['description'],
                'priority' => $validated['priority'],
                'follow_up_required' => $validated['follow_up_required'] ?? false,
                'follow_up_date' => $validated['follow_up_date'] ?? null,
                'assigned_to' => $validated['assigned_to'] ?? null,
                'duration_minutes' => $validated['duration_minutes'] ?? null,
                'status' => $validated['assigned_to'] ? 'in_progress' : 'pending',
                'interaction_date' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Interaction added successfully',
                'interaction' => $interaction->getSummary(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Update Interaction
     */
    public function updateInteraction(Request $request, CustomerInteraction $interaction)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'outcome' => 'nullable|string',
            'notes' => 'nullable|string',
            'follow_up_required' => 'boolean',
            'follow_up_date' => 'nullable|date|after:today',
        ]);

        try {
            $interaction->update($validated);

            if ($validated['status'] === 'completed') {
                $interaction->markCompleted($validated['outcome'] ?? null);
            }

            return response()->json([
                'success' => true,
                'message' => 'Interaction updated successfully',
                'interaction' => $interaction->getSummary(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Loyalty Program Management
     */
    public function loyalty(Request $request)
    {
        $stats = $this->getLoyaltyStats();

        $query = LoyaltyPoint::with(['customer', 'createdBy']);

        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('transaction_type')) {
            $query->where('transaction_type', $request->transaction_type);
        }

        $loyaltyPoints = $query->latest()->paginate(20);

        $topCustomers = Customer::withSum(['loyaltyPoints as total_points' => function ($query) {
                                    $query->where('status', 'active');
                                }], 'points')
                               ->orderBy('total_points', 'desc')
                               ->limit(10)
                               ->get();

        return view('admin.crm.loyalty', compact('stats', 'loyaltyPoints', 'topCustomers'));
    }

    /**
     * Award Loyalty Points
     */
    public function awardPoints(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'points' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'expires_at' => 'nullable|date|after:today',
        ]);

        try {
            $customer = Customer::findOrFail($validated['customer_id']);

            $loyaltyPoint = LoyaltyPoint::awardPoints(
                $customer->id,
                $validated['points'],
                $validated['reason'],
                null,
                $validated['expires_at'] ? \Carbon\Carbon::parse($validated['expires_at']) : null
            );

            return response()->json([
                'success' => true,
                'message' => 'Points awarded successfully',
                'loyalty_point' => $loyaltyPoint->getSummary(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Redeem Loyalty Points
     */
    public function redeemPoints(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'points' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
        ]);

        try {
            $customer = Customer::findOrFail($validated['customer_id']);

            $redemption = $customer->redeemLoyaltyPoints(
                $validated['points'],
                $validated['reason']
            );

            return response()->json([
                'success' => true,
                'message' => 'Points redeemed successfully',
                'redemption' => $redemption->getSummary(),
                'remaining_points' => $customer->total_loyalty_points,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Marketing Campaigns
     */
    public function campaigns(Request $request)
    {
        $query = MarketingCampaign::with(['createdBy']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('campaign_type', $request->type);
        }

        if ($request->filled('channel')) {
            $query->where('channel', $request->channel);
        }

        $campaigns = $query->latest()->paginate(20);

        $campaignStats = $this->getCampaignStats();

        return view('admin.crm.campaigns', compact('campaigns', 'campaignStats'));
    }

    /**
     * Get CRM Statistics
     */
    protected function getCRMStats()
    {
        return [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::byStatus('active')->count(),
            'vip_customers' => Customer::vip()->count(),
            'high_value_customers' => Customer::highValue()->count(),
            'pending_interactions' => CustomerInteraction::pending()->count(),
            'overdue_follow_ups' => CustomerInteraction::overdueFollowUp()->count(),
            'total_loyalty_points' => LoyaltyPoint::active()->sum('points'),
            'points_redeemed_this_month' => LoyaltyRedemption::thisMonth()->sum('points_redeemed'),
            'active_campaigns' => MarketingCampaign::active()->count(),
            'customer_satisfaction' => $this->getAverageCustomerSatisfaction(),
        ];
    }

    /**
     * Get Recent Interactions
     */
    protected function getRecentInteractions()
    {
        return CustomerInteraction::with(['customer', 'user'])
                                 ->latest('created_at')
                                 ->limit(10)
                                 ->get();
    }

    /**
     * Get Upcoming Follow-ups
     */
    protected function getUpcomingFollowUps()
    {
        return CustomerInteraction::with(['customer', 'assignedTo'])
                                 ->requiresFollowUp()
                                 ->where('follow_up_date', '>=', now())
                                 ->where('follow_up_date', '<=', now()->addDays(7))
                                 ->orderBy('follow_up_date')
                                 ->get();
    }

    /**
     * Get Customer Segments
     */
    protected function getCustomerSegments()
    {
        return [
            'active' => Customer::byStatus('active')->count(),
            'at_risk' => Customer::byStatus('at_risk')->count(),
            'dormant' => Customer::byStatus('dormant')->count(),
            'lost' => Customer::byStatus('lost')->count(),
            'vip' => Customer::vip()->count(),
            'regular' => Customer::bySegment('regular')->count(),
            'wholesale' => Customer::bySegment('wholesale')->count(),
        ];
    }

    /**
     * Get Customer Purchase History
     */
    protected function getCustomerPurchaseHistory(Customer $customer)
    {
        return $customer->invoices()
                       ->with(['items.product'])
                       ->where('status', 'paid')
                       ->orderBy('invoice_date', 'desc')
                       ->limit(10)
                       ->get();
    }

    /**
     * Get Customer Interaction Timeline
     */
    protected function getCustomerInteractionTimeline(Customer $customer)
    {
        return $customer->interactions()
                       ->with(['createdBy', 'assignedTo'])
                       ->orderBy('interaction_date', 'desc')
                       ->get();
    }

    /**
     * Get Customer Loyalty Summary
     */
    protected function getCustomerLoyaltySummary(Customer $customer)
    {
        return [
            'total_points' => $customer->total_loyalty_points,
            'total_earned' => LoyaltyPoint::getCustomerTotalEarned($customer->id),
            'total_redeemed' => LoyaltyPoint::getCustomerTotalRedeemed($customer->id),
            'expiring_soon' => $customer->loyaltyPoints()->expiringIn(30)->sum('points'),
            'recent_transactions' => $customer->loyaltyPoints()->latest()->limit(5)->get(),
        ];
    }

    /**
     * Get Customer Recommendations
     */
    protected function getCustomerRecommendations(Customer $customer)
    {
        $recommendations = [];

        // Birthday/Anniversary reminders
        if ($customer->date_of_birth && $customer->date_of_birth->isCurrentMonth()) {
            $recommendations[] = [
                'type' => 'birthday',
                'title' => 'Birthday This Month',
                'description' => 'Send birthday wishes and special offers',
                'priority' => 'high',
            ];
        }

        if ($customer->anniversary_date && $customer->anniversary_date->isCurrentMonth()) {
            $recommendations[] = [
                'type' => 'anniversary',
                'title' => 'Anniversary This Month',
                'description' => 'Send anniversary wishes and couple offers',
                'priority' => 'high',
            ];
        }

        // Purchase behavior recommendations
        if ($customer->days_since_last_purchase > 90) {
            $recommendations[] = [
                'type' => 'win_back',
                'title' => 'Win-back Campaign',
                'description' => 'Customer hasn\'t purchased in 90+ days',
                'priority' => 'medium',
            ];
        }

        // Loyalty points expiring
        $expiringPoints = $customer->loyaltyPoints()->expiringIn(30)->sum('points');
        if ($expiringPoints > 0) {
            $recommendations[] = [
                'type' => 'loyalty_expiry',
                'title' => 'Points Expiring Soon',
                'description' => "{$expiringPoints} points expiring in 30 days",
                'priority' => 'medium',
            ];
        }

        return $recommendations;
    }

    /**
     * Get Loyalty Statistics
     */
    protected function getLoyaltyStats()
    {
        return [
            'total_active_points' => LoyaltyPoint::active()->sum('points'),
            'total_redeemed_points' => LoyaltyPoint::redeemed()->sum('points'),
            'points_issued_this_month' => LoyaltyPoint::earned()->whereMonth('created_at', now()->month)->sum('points'),
            'points_redeemed_this_month' => LoyaltyRedemption::thisMonth()->sum('points_redeemed'),
            'customers_with_points' => Customer::whereHas('loyaltyPoints', function ($q) {
                $q->where('status', 'active');
            })->count(),
            'average_points_per_customer' => LoyaltyPoint::active()->avg('points'),
            'expiring_points_30_days' => LoyaltyPoint::expiringIn(30)->sum('points'),
        ];
    }

    /**
     * Get Campaign Statistics
     */
    protected function getCampaignStats()
    {
        return [
            'total_campaigns' => MarketingCampaign::count(),
            'active_campaigns' => MarketingCampaign::active()->count(),
            'completed_campaigns' => MarketingCampaign::completed()->count(),
            'scheduled_campaigns' => MarketingCampaign::scheduled()->count(),
            'average_open_rate' => MarketingCampaign::completed()->avg('open_rate') ?? 0,
            'average_click_rate' => MarketingCampaign::completed()->avg('click_rate') ?? 0,
            'total_recipients_this_month' => MarketingCampaign::whereMonth('started_at', now()->month)
                                                           ->sum('total_recipients'),
        ];
    }

    /**
     * Get Average Customer Satisfaction
     */
    protected function getAverageCustomerSatisfaction()
    {
        // This would be calculated from customer feedback/ratings
        // For now, return a placeholder value
        return 4.2; // out of 5
    }

    /**
     * Get Customer Analytics
     */
    public function getCustomerAnalytics(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = now()->subDays($days);

        $analytics = [
            'new_customers' => Customer::where('created_at', '>=', $startDate)->count(),
            'customer_retention_rate' => $this->calculateRetentionRate($days),
            'average_customer_value' => Customer::avg('total_purchase_amount'),
            'customer_lifetime_value' => $this->calculateAverageLifetimeValue(),
            'churn_rate' => $this->calculateChurnRate($days),
            'customer_acquisition_cost' => $this->calculateAcquisitionCost($days),
            'repeat_purchase_rate' => $this->calculateRepeatPurchaseRate($days),
            'customer_segments' => $this->getCustomerSegments(),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Calculate Customer Retention Rate
     */
    protected function calculateRetentionRate($days)
    {
        $startDate = now()->subDays($days);
        $existingCustomers = Customer::where('created_at', '<', $startDate)->count();
        $returningCustomers = Customer::where('created_at', '<', $startDate)
                                    ->where('last_purchase_date', '>=', $startDate)
                                    ->count();

        return $existingCustomers > 0 ? round(($returningCustomers / $existingCustomers) * 100, 2) : 0;
    }

    /**
     * Calculate Average Lifetime Value
     */
    protected function calculateAverageLifetimeValue()
    {
        return Customer::where('total_purchase_amount', '>', 0)->avg('total_purchase_amount') ?? 0;
    }

    /**
     * Calculate Churn Rate
     */
    protected function calculateChurnRate($days)
    {
        $startDate = now()->subDays($days);
        $totalCustomers = Customer::where('created_at', '<', $startDate)->count();
        $churnedCustomers = Customer::where('created_at', '<', $startDate)
                                  ->where(function ($q) use ($startDate) {
                                      $q->whereNull('last_purchase_date')
                                        ->orWhere('last_purchase_date', '<', $startDate);
                                  })
                                  ->count();

        return $totalCustomers > 0 ? round(($churnedCustomers / $totalCustomers) * 100, 2) : 0;
    }

    /**
     * Calculate Customer Acquisition Cost
     */
    protected function calculateAcquisitionCost($days)
    {
        // This would be calculated from marketing spend data
        // For now, return a placeholder value
        return 500; // ₹500 per customer
    }

    /**
     * Calculate Repeat Purchase Rate
     */
    protected function calculateRepeatPurchaseRate($days)
    {
        $startDate = now()->subDays($days);
        $customersWithPurchases = Customer::whereHas('invoices', function ($q) use ($startDate) {
            $q->where('invoice_date', '>=', $startDate)
              ->where('status', 'paid');
        })->count();

        $customersWithMultiplePurchases = Customer::whereHas('invoices', function ($q) use ($startDate) {
            $q->where('invoice_date', '>=', $startDate)
              ->where('status', 'paid');
        }, '>=', 2)->count();

        return $customersWithPurchases > 0 ? round(($customersWithMultiplePurchases / $customersWithPurchases) * 100, 2) : 0;
    }
}
