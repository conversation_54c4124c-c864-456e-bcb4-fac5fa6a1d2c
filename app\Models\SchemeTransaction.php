<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\GeneratesUniqueFields;

class SchemeTransaction extends Model
{
    use HasFactory, GeneratesUniqueFields;

    protected $fillable = [
        'transaction_number',
        'scheme_id',
        'transaction_type',
        'amount',
        'type',
        'transaction_date',
        'description',
        'notes',
        'processed_by',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'amount' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (!$transaction->transaction_number) {
                $transaction->transaction_number = static::generateTransactionNumber();
            }
            if (!$transaction->processed_by) {
                $transaction->processed_by = auth()->id() ?: 1; // Default to user ID 1 if no auth
            }
        });
    }

    /**
     * Relationships
     */
    public function scheme(): BelongsTo
    {
        return $this->belongsTo(SavingScheme::class, 'scheme_id');
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'processed_by');
    }

    /**
     * Scopes
     */
    public function scopeByScheme($query, $schemeId)
    {
        return $query->where('scheme_id', $schemeId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }

    public function scopeCredits($query)
    {
        return $query->where('type', 'credit');
    }

    public function scopeDebits($query)
    {
        return $query->where('type', 'debit');
    }

    /**
     * Accessors
     */
    public function getFormattedAmountAttribute(): string
    {
        return '₹' . number_format($this->amount, 2);
    }

    public function getTransactionTypeDisplayAttribute(): string
    {
        $types = [
            'interest' => 'Interest',
            'bonus' => 'Bonus',
            'penalty' => 'Penalty',
            'adjustment' => 'Adjustment',
            'withdrawal' => 'Withdrawal',
        ];

        return $types[$this->transaction_type] ?? ucfirst($this->transaction_type);
    }

    public function getTypeDisplayAttribute(): string
    {
        return ucfirst($this->type);
    }

    /**
     * Business Logic Methods
     */
    public static function generateTransactionNumber(): string
    {
        $maxAttempts = 10;
        $attempt = 0;

        while ($attempt < $maxAttempts) {
            $prefix = 'ST';

            // Use microseconds for better uniqueness
            $microtime = microtime(true);
            $timestamp = date('ymdHis', $microtime);
            $microseconds = sprintf('%03d', ($microtime - floor($microtime)) * 1000);

            // Add random component
            $random = rand(100000, 999999);

            $number = $prefix . $timestamp . $microseconds . $random;

            // Check if this number already exists
            if (!static::where('transaction_number', $number)->exists()) {
                return $number;
            }

            $attempt++;
            // Small delay to avoid rapid collisions
            usleep(1000); // 1ms
        }

        // Fallback: use UUID-based approach if all attempts failed
        $uuid = str_replace('-', '', \Illuminate\Support\Str::uuid());
        $prefix = 'ST';
        $timestamp = now()->format('ymdHis');

        return $prefix . $timestamp . substr($uuid, 0, 8);
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'transaction_number' => $this->transaction_number,
            'scheme_number' => $this->scheme->scheme_number,
            'transaction_type' => $this->transaction_type_display,
            'amount' => $this->formatted_amount,
            'type' => $this->type_display,
            'transaction_date' => $this->transaction_date->format('Y-m-d'),
            'description' => $this->description,
            'processed_by' => $this->processedBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
