<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('printer_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100); // Printer name/identifier
            $table->string('type', 50); // thermal, laser, inkjet, etc.
            $table->enum('connection_type', ['usb', 'network', 'bluetooth', 'serial', 'file'])->default('usb');

            // Connection details
            $table->string('connection_string')->nullable(); // IP address, COM port, device path, etc.
            $table->integer('port')->nullable(); // Network port
            $table->string('device_path')->nullable(); // USB device path or file path
            $table->json('connection_options')->nullable(); // Additional connection options

            // Printer specifications
            $table->integer('paper_width_mm')->default(80); // Paper width in mm
            $table->integer('characters_per_line')->default(48); // Characters per line
            $table->boolean('supports_graphics')->default(true);
            $table->boolean('supports_barcode')->default(true);
            $table->boolean('supports_qr_code')->default(true);
            $table->boolean('supports_cut')->default(true);
            $table->boolean('supports_drawer')->default(false); // Cash drawer support

            // Print settings
            $table->json('default_settings')->nullable(); // Default print settings
            $table->integer('print_density')->default(8); // Print density (1-15)
            $table->integer('print_speed')->default(4); // Print speed (1-9)
            $table->boolean('auto_cut')->default(true);
            $table->integer('cut_lines')->default(3); // Lines to feed before cut

            // Usage configuration
            $table->json('print_types')->nullable(); // Array of supported print types
            $table->boolean('is_default_invoice')->default(false);
            $table->boolean('is_default_receipt')->default(false);
            $table->boolean('is_default_label')->default(false);
            $table->boolean('is_default_tag')->default(false);

            // Location and access
            $table->foreignId('location_id')->nullable()->constrained()->onDelete('set null');
            $table->json('allowed_users')->nullable(); // Array of user IDs who can use this printer
            $table->json('allowed_roles')->nullable(); // Array of roles that can use this printer

            // Status and monitoring
            $table->boolean('is_active')->default(true);
            $table->boolean('is_online')->default(false);
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('last_checked_at')->nullable();
            $table->text('last_error')->nullable();
            $table->integer('total_prints')->default(0);
            $table->integer('successful_prints')->default(0);
            $table->integer('failed_prints')->default(0);

            // Maintenance
            $table->date('last_maintenance_date')->nullable();
            $table->date('next_maintenance_due')->nullable();
            $table->text('maintenance_notes')->nullable();

            // Audit fields
            $table->text('description')->nullable();
            $table->json('custom_settings')->nullable(); // Custom printer-specific settings
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Indexes
            $table->index(['type', 'is_active']);
            $table->index(['location_id', 'is_active']);
            $table->index(['connection_type']);
            $table->index(['is_default_invoice']);
            $table->index(['is_default_receipt']);
            $table->index(['is_default_label']);
            $table->index(['is_default_tag']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('printer_configurations');
    }
};
