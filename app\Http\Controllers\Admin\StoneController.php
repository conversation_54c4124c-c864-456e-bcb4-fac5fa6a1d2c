<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Stone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StoneController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Stone::withCount('products');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $stones = $query->orderBy('sort_order')->orderBy('name')->paginate(20);

        // Get stone types for filter
        $stoneTypes = Stone::distinct()->pluck('type')->filter()->sort();

        // Get statistics
        $stats = [
            'total_stones' => Stone::count(),
            'active_stones' => Stone::active()->count(),
            'stones_with_products' => Stone::has('products')->count(),
            'unique_types' => Stone::distinct()->whereNotNull('type')->count('type'),
        ];

        return view('admin.stones.index', compact('stones', 'stoneTypes', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $stoneTypes = Stone::distinct()->pluck('type')->filter()->sort();

        return view('admin.stones.create', compact('stoneTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:stones,name',
            'type' => 'required|string|max:100',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|max:100',
            'clarity' => 'nullable|string|max:100',
            'cut' => 'nullable|string|max:100',
            'carat_weight' => 'nullable|numeric|min:0',
            'price_per_carat' => 'nullable|numeric|min:0',
            'price_per_piece' => 'nullable|numeric|min:0',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // Set sort order if not provided
            if (!isset($validated['sort_order'])) {
                $validated['sort_order'] = (Stone::max('sort_order') ?? 0) + 1;
            }

            Stone::create([
                'name' => $validated['name'],
                'type' => $validated['type'],
                'description' => $validated['description'],
                'color' => $validated['color'],
                'clarity' => $validated['clarity'],
                'cut' => $validated['cut'],
                'carat_weight' => $validated['carat_weight'],
                'price_per_carat' => $validated['price_per_carat'],
                'price_per_piece' => $validated['price_per_piece'],
                'sort_order' => $validated['sort_order'],
                'is_active' => $validated['is_active'] ?? true,
                'created_by' => auth()->id(),
            ]);

            DB::commit();

            return redirect()->route('admin.stones.index')
                ->with('success', 'Stone created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error creating stone: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Stone $stone)
    {
        $stone->load(['products.inventory']);

        // Get stone statistics
        $stats = [
            'total_products' => $stone->products()->count(),
            'active_products' => $stone->products()->active()->count(),
            'total_inventory_value' => $stone->products()
                ->join('inventories', 'products.id', '=', 'inventories.product_id')
                ->sum(DB::raw('inventories.quantity_available * inventories.cost_price')),
            'avg_stone_charges' => $stone->products()->avg('stone_charges'),
        ];

        return view('admin.stones.show', compact('stone', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Stone $stone)
    {
        $stoneTypes = Stone::distinct()->pluck('type')->filter()->sort();

        return view('admin.stones.edit', compact('stone', 'stoneTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Stone $stone)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:stones,name,' . $stone->id,
            'type' => 'required|string|max:100',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|max:100',
            'clarity' => 'nullable|string|max:100',
            'cut' => 'nullable|string|max:100',
            'carat_weight' => 'nullable|numeric|min:0',
            'price_per_carat' => 'nullable|numeric|min:0',
            'price_per_piece' => 'nullable|numeric|min:0',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $validated['updated_by'] = auth()->id();

            $stone->update($validated);

            DB::commit();

            return redirect()->route('admin.stones.index')
                ->with('success', 'Stone updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error updating stone: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Stone $stone)
    {
        // Check if stone has products
        if ($stone->products()->count() > 0) {
            return back()->with('error', 'Cannot delete stone with existing products. Please reassign or delete products first.');
        }

        try {
            $stone->delete();

            return redirect()->route('admin.stones.index')
                ->with('success', 'Stone deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Error deleting stone: ' . $e->getMessage());
        }
    }

    /**
     * Toggle stone status
     */
    public function toggleStatus(Stone $stone)
    {
        try {
            $stone->update([
                'is_active' => !$stone->is_active,
                'updated_by' => auth()->id(),
            ]);

            $status = $stone->is_active ? 'activated' : 'deactivated';

            return response()->json([
                'success' => true,
                'message' => "Stone {$status} successfully.",
                'is_active' => $stone->is_active,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating stone status: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get stones for select dropdown
     */
    public function getForSelect(Request $request)
    {
        $query = Stone::active();

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $stones = $query->orderBy('sort_order')->orderBy('name')->get();

        return response()->json($stones);
    }

    /**
     * Bulk operations
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'stone_ids' => 'required|array|min:1',
            'stone_ids.*' => 'exists:stones,id',
        ]);

        try {
            DB::beginTransaction();

            $stones = Stone::whereIn('id', $validated['stone_ids'])->get();

            switch ($validated['action']) {
                case 'activate':
                    Stone::whereIn('id', $validated['stone_ids'])
                        ->update(['is_active' => true, 'updated_by' => auth()->id()]);
                    $message = 'Stones activated successfully.';
                    break;

                case 'deactivate':
                    Stone::whereIn('id', $validated['stone_ids'])
                        ->update(['is_active' => false, 'updated_by' => auth()->id()]);
                    $message = 'Stones deactivated successfully.';
                    break;

                case 'delete':
                    // Check for products
                    foreach ($stones as $stone) {
                        if ($stone->products()->count() > 0) {
                            throw new \Exception("Cannot delete stone '{$stone->name}' - it has associated products.");
                        }
                    }

                    Stone::whereIn('id', $validated['stone_ids'])->delete();
                    $message = 'Stones deleted successfully.';
                    break;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $message,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error performing bulk action: ' . $e->getMessage(),
            ]);
        }
    }
}
