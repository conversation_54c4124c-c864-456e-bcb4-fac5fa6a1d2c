<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Inventory;
use App\Models\Location;
use App\Services\GstCalculationService;
use App\Services\InvoiceNumberService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InvoiceController extends Controller
{
    protected $gstService;
    protected $invoiceNumberService;

    public function __construct(GstCalculationService $gstService, InvoiceNumberService $invoiceNumberService)
    {
        $this->gstService = $gstService;
        $this->invoiceNumberService = $invoiceNumberService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Invoice::with(['customer', 'location', 'items.product'])
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('first_name', 'like', "%{$search}%")
                                   ->orWhere('last_name', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('invoice_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('invoice_date', '<=', $request->date_to);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Payment status filter
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Location filter
        if ($request->filled('location')) {
            $query->where('location_id', $request->location);
        }

        $invoices = $query->paginate(20);
        $locations = Location::active()->get();

        // Get summary statistics
        $stats = [
            'total_invoices' => Invoice::count(),
            'pending_invoices' => Invoice::where('status', 'pending')->count(),
            'paid_invoices' => Invoice::where('payment_status', 'paid')->count(),
            'total_amount' => Invoice::where('status', '!=', 'cancelled')->sum('total_amount'),
            'pending_amount' => Invoice::where('payment_status', 'pending')->sum('total_amount'),
        ];

        return view('admin.invoices.index', compact('invoices', 'locations', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $customers = Customer::active()->orderBy('first_name')->get();
        $locations = Location::active()->get();

        // If customer is pre-selected
        $selectedCustomer = null;
        if ($request->filled('customer_id')) {
            $selectedCustomer = Customer::find($request->customer_id);
        }

        return view('admin.invoices.create', compact('customers', 'locations', 'selectedCustomer'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'location_id' => 'required|exists:locations,id',
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date|after_or_equal:invoice_date',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.making_charges' => 'nullable|numeric|min:0',
            'items.*.stone_charges' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'shipping_charges' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            // Generate invoice number
            $invoiceNumber = $this->invoiceNumberService->generateInvoiceNumber($validated['location_id']);

            // Calculate totals and GST
            $calculation = $this->gstService->calculateInvoiceTotal($validated);

            // Create invoice
            $invoice = Invoice::create([
                'invoice_number' => $invoiceNumber,
                'customer_id' => $validated['customer_id'],
                'location_id' => $validated['location_id'],
                'invoice_date' => $validated['invoice_date'],
                'due_date' => $validated['due_date'] ?? Carbon::parse($validated['invoice_date'])->addDays(30),
                'subtotal' => $calculation['subtotal'],
                'discount_amount' => $validated['discount_amount'] ?? 0,
                'shipping_charges' => $validated['shipping_charges'] ?? 0,
                'cgst_amount' => $calculation['cgst_amount'],
                'sgst_amount' => $calculation['sgst_amount'],
                'igst_amount' => $calculation['igst_amount'],
                'total_gst' => $calculation['total_gst'],
                'total_amount' => $calculation['total_amount'],
                'status' => 'pending',
                'payment_status' => 'pending',
                'notes' => $validated['notes'],
                'created_by' => auth()->id(),
            ]);

            // Create invoice items
            foreach ($validated['items'] as $item) {
                $product = Product::find($item['product_id']);
                $itemCalculation = $this->gstService->calculateItemGst($item, $product);

                $invoice->items()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'discount_percentage' => $item['discount_percentage'] ?? 0,
                    'discount_amount' => $itemCalculation['discount_amount'],
                    'making_charges' => $item['making_charges'] ?? 0,
                    'stone_charges' => $item['stone_charges'] ?? 0,
                    'taxable_amount' => $itemCalculation['taxable_amount'],
                    'gst_rate' => $itemCalculation['gst_rate'],
                    'cgst_amount' => $itemCalculation['cgst_amount'],
                    'sgst_amount' => $itemCalculation['sgst_amount'],
                    'igst_amount' => $itemCalculation['igst_amount'],
                    'total_amount' => $itemCalculation['total_amount'],
                ]);

                // Update inventory
                $inventory = Inventory::where('product_id', $item['product_id'])
                                   ->where('location_id', $validated['location_id'])
                                   ->first();

                if ($inventory) {
                    $inventory->adjustStock(-$item['quantity'], 'sale', "Sale - Invoice #{$invoiceNumber}", auth()->id());
                }
            }

            DB::commit();

            return redirect()->route('admin.invoices.show', $invoice)
                ->with('success', 'Invoice created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error creating invoice: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Invoice $invoice)
    {
        $invoice->load(['customer', 'location', 'items.product.category', 'createdBy']);

        return view('admin.invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Invoice $invoice)
    {
        if ($invoice->status === 'paid' || $invoice->status === 'cancelled') {
            return redirect()->route('admin.invoices.show', $invoice)
                ->with('error', 'Cannot edit paid or cancelled invoices.');
        }

        $customers = Customer::active()->orderBy('first_name')->get();
        $locations = Location::active()->get();
        $invoice->load(['items.product']);

        return view('admin.invoices.edit', compact('invoice', 'customers', 'locations'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Invoice $invoice)
    {
        if ($invoice->status === 'paid' || $invoice->status === 'cancelled') {
            return redirect()->route('admin.invoices.show', $invoice)
                ->with('error', 'Cannot update paid or cancelled invoices.');
        }

        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'location_id' => 'required|exists:locations,id',
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date|after_or_equal:invoice_date',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.making_charges' => 'nullable|numeric|min:0',
            'items.*.stone_charges' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'shipping_charges' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            // Restore inventory for existing items
            foreach ($invoice->items as $existingItem) {
                $inventory = Inventory::where('product_id', $existingItem->product_id)
                                   ->where('location_id', $invoice->location_id)
                                   ->first();

                if ($inventory) {
                    $inventory->adjustStock($existingItem->quantity, 'sale_reversal',
                        "Invoice update - restoring stock for Invoice #{$invoice->invoice_number}", auth()->id());
                }
            }

            // Delete existing items
            $invoice->items()->delete();

            // Calculate new totals and GST
            $calculation = $this->gstService->calculateInvoiceTotal($validated);

            // Update invoice
            $invoice->update([
                'customer_id' => $validated['customer_id'],
                'location_id' => $validated['location_id'],
                'invoice_date' => $validated['invoice_date'],
                'due_date' => $validated['due_date'] ?? Carbon::parse($validated['invoice_date'])->addDays(30),
                'subtotal' => $calculation['subtotal'],
                'discount_amount' => $validated['discount_amount'] ?? 0,
                'shipping_charges' => $validated['shipping_charges'] ?? 0,
                'cgst_amount' => $calculation['cgst_amount'],
                'sgst_amount' => $calculation['sgst_amount'],
                'igst_amount' => $calculation['igst_amount'],
                'total_gst' => $calculation['total_gst'],
                'total_amount' => $calculation['total_amount'],
                'notes' => $validated['notes'],
                'updated_by' => auth()->id(),
            ]);

            // Create new invoice items
            foreach ($validated['items'] as $item) {
                $product = Product::find($item['product_id']);
                $itemCalculation = $this->gstService->calculateItemGst($item, $product);

                $invoice->items()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'discount_percentage' => $item['discount_percentage'] ?? 0,
                    'discount_amount' => $itemCalculation['discount_amount'],
                    'making_charges' => $item['making_charges'] ?? 0,
                    'stone_charges' => $item['stone_charges'] ?? 0,
                    'taxable_amount' => $itemCalculation['taxable_amount'],
                    'gst_rate' => $itemCalculation['gst_rate'],
                    'cgst_amount' => $itemCalculation['cgst_amount'],
                    'sgst_amount' => $itemCalculation['sgst_amount'],
                    'igst_amount' => $itemCalculation['igst_amount'],
                    'total_amount' => $itemCalculation['total_amount'],
                ]);

                // Update inventory
                $inventory = Inventory::where('product_id', $item['product_id'])
                                   ->where('location_id', $validated['location_id'])
                                   ->first();

                if ($inventory) {
                    $inventory->adjustStock(-$item['quantity'], 'sale', "Sale - Invoice #{$invoice->invoice_number}", auth()->id());
                }
            }

            DB::commit();

            return redirect()->route('admin.invoices.show', $invoice)
                ->with('success', 'Invoice updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error updating invoice: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Invoice $invoice)
    {
        if ($invoice->status === 'paid') {
            return back()->with('error', 'Cannot delete paid invoices.');
        }

        try {
            DB::beginTransaction();

            // Restore inventory for all items
            foreach ($invoice->items as $item) {
                $inventory = Inventory::where('product_id', $item->product_id)
                                   ->where('location_id', $invoice->location_id)
                                   ->first();

                if ($inventory) {
                    $inventory->adjustStock($item->quantity, 'sale_reversal',
                        "Invoice deletion - restoring stock for Invoice #{$invoice->invoice_number}", auth()->id());
                }
            }

            // Delete invoice items
            $invoice->items()->delete();

            // Delete invoice
            $invoice->delete();

            DB::commit();

            return redirect()->route('admin.invoices.index')
                ->with('success', 'Invoice deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting invoice: ' . $e->getMessage());
        }
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus(Request $request, Invoice $invoice)
    {
        $validated = $request->validate([
            'payment_status' => 'required|in:pending,partial,paid,overdue',
            'payment_method' => 'nullable|string|max:50',
            'payment_reference' => 'nullable|string|max:100',
            'payment_notes' => 'nullable|string|max:500',
        ]);

        try {
            $invoice->update([
                'payment_status' => $validated['payment_status'],
                'payment_method' => $validated['payment_method'],
                'payment_reference' => $validated['payment_reference'],
                'payment_notes' => $validated['payment_notes'],
                'paid_at' => $validated['payment_status'] === 'paid' ? now() : null,
                'updated_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment status updated successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating payment status: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Cancel invoice
     */
    public function cancel(Invoice $invoice)
    {
        if ($invoice->status === 'paid') {
            return back()->with('error', 'Cannot cancel paid invoices.');
        }

        try {
            DB::beginTransaction();

            // Restore inventory for all items
            foreach ($invoice->items as $item) {
                $inventory = Inventory::where('product_id', $item->product_id)
                                   ->where('location_id', $invoice->location_id)
                                   ->first();

                if ($inventory) {
                    $inventory->adjustStock($item->quantity, 'sale_reversal',
                        "Invoice cancellation - restoring stock for Invoice #{$invoice->invoice_number}", auth()->id());
                }
            }

            // Update invoice status
            $invoice->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancelled_by' => auth()->id(),
            ]);

            DB::commit();

            return redirect()->route('admin.invoices.show', $invoice)
                ->with('success', 'Invoice cancelled successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error cancelling invoice: ' . $e->getMessage());
        }
    }

    /**
     * Print invoice
     */
    public function print(Invoice $invoice)
    {
        $invoice->load(['customer', 'location', 'items.product.category', 'createdBy']);

        return view('admin.invoices.print', compact('invoice'));
    }

    /**
     * Download invoice PDF
     */
    public function downloadPdf(Invoice $invoice)
    {
        // This would implement PDF generation
        return response()->json(['message' => 'PDF download functionality to be implemented']);
    }

    /**
     * Get products for invoice creation
     */
    public function getProducts(Request $request)
    {
        $locationId = $request->location_id;

        $products = Product::with(['category', 'metal', 'inventory' => function ($query) use ($locationId) {
                $query->where('location_id', $locationId);
            }])
            ->whereHas('inventory', function ($query) use ($locationId) {
                $query->where('location_id', $locationId)
                      ->where('quantity_available', '>', 0);
            })
            ->where('is_active', true)
            ->get();

        return response()->json($products);
    }

    /**
     * Calculate invoice totals
     */
    public function calculateTotals(Request $request)
    {
        try {
            $calculation = $this->gstService->calculateInvoiceTotal($request->all());

            return response()->json([
                'success' => true,
                'calculation' => $calculation
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating totals: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export invoices
     */
    public function export(Request $request)
    {
        // This would implement CSV/Excel export functionality
        return response()->json(['message' => 'Export functionality to be implemented']);
    }
}
