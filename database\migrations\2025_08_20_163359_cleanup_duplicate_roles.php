<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Define the standard role names we want to keep
        $standardRoles = [
            'SuperAdmin' => ['super-admin', 'Super Admin'],
            'Manager' => ['Store Manager'],
            'Cashier' => [],
            'SalesStaff' => ['Sales Staff'],
            'Accountant' => [],
            'Inventory Manager' => [],
            'Repair Technician' => [],
            'Customer' => [],
        ];

        // Remove the old 'admin' role as it's not in our standard set
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            // Move users from 'admin' role to 'SuperAdmin' role
            $superAdminRole = Role::where('name', 'SuperAdmin')->first();
            if ($superAdminRole) {
                $adminUsers = $adminRole->users;
                foreach ($adminUsers as $user) {
                    $user->removeRole('admin');
                    $user->assignRole('SuperAdmin');
                }
            }
            $adminRole->delete();
        }

        // Clean up duplicate roles
        foreach ($standardRoles as $keepRole => $duplicateRoles) {
            $mainRole = Role::where('name', $keepRole)->first();

            if (!$mainRole) {
                continue; // Skip if main role doesn't exist
            }

            foreach ($duplicateRoles as $duplicateName) {
                $duplicateRole = Role::where('name', $duplicateName)->first();
                if ($duplicateRole) {
                    // Move all users from duplicate role to main role
                    $users = $duplicateRole->users;
                    foreach ($users as $user) {
                        $user->removeRole($duplicateName);
                        if (!$user->hasRole($keepRole)) {
                            $user->assignRole($keepRole);
                        }
                    }

                    // Delete the duplicate role
                    $duplicateRole->delete();
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not reversible as it involves data cleanup
        // If needed, re-run the seeders to recreate roles
    }
};
