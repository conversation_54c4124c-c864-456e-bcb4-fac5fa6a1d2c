<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+91 98765 43210',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create SuperAdmin role if it doesn't exist (using standard naming)
        $superAdminRole = Role::firstOrCreate(['name' => 'SuperAdmin']);

        // Assign SuperAdmin role to the admin user
        if (!$admin->hasRole('SuperAdmin')) {
            $admin->assignRole('SuperAdmin');
        }

        // Create a test website user
        $websiteUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+91 98765 43211',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('Admin users created successfully!');
        $this->command->info('');
        $this->command->info('=== ADMIN CREDENTIALS ===');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password123');
        $this->command->info('Role: Super Admin');
        $this->command->info('');
        $this->command->info('=== TEST USER CREDENTIALS ===');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password123');
        $this->command->info('Role: Regular User');
        $this->command->info('');
    }
}
