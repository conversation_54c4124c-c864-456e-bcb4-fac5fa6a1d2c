<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'invoice_number',
        'customer_id',
        'location_id',
        'estimate_id',
        'invoice_date',
        'invoice_type',
        'status',
        'subtotal',
        'discount_amount',
        'shipping_charges',
        'cgst_amount',
        'sgst_amount',
        'igst_amount',
        'total_gst',
        'total_tax_amount',
        'round_off_amount',
        'total_amount',
        'paid_amount',
        'balance_amount',
        'due_date',
        'payment_status',
        'payment_method',
        'payment_reference',
        'payment_notes',
        'paid_at',
        'cancelled_at',
        'cancelled_by',
        'notes',
        'is_printed',
        'is_emailed',
        'is_whatsapp_sent',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'paid_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'shipping_charges' => 'decimal:2',
        'cgst_amount' => 'decimal:2',
        'sgst_amount' => 'decimal:2',
        'igst_amount' => 'decimal:2',
        'total_gst' => 'decimal:2',
        'total_tax_amount' => 'decimal:2',
        'round_off_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'balance_amount' => 'decimal:2',
        'is_printed' => 'boolean',
        'is_emailed' => 'boolean',
        'is_whatsapp_sent' => 'boolean',
    ];

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function estimate(): BelongsTo
    {
        return $this->belongsTo(Estimate::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function oldGoldTransactions(): HasMany
    {
        return $this->hasMany(OldGoldTransaction::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Business logic methods
    public function calculateTotals(): void
    {
        $subtotal = $this->items->sum('total_amount');
        $discountAmount = $this->discount_amount ?? 0;
        $taxableAmount = $subtotal - $discountAmount;

        // Calculate GST amounts
        $cgstAmount = $this->items->sum('cgst_amount');
        $sgstAmount = $this->items->sum('sgst_amount');
        $igstAmount = $this->items->sum('igst_amount');
        $totalTaxAmount = $cgstAmount + $sgstAmount + $igstAmount;

        $totalBeforeRounding = $taxableAmount + $totalTaxAmount;
        $roundedTotal = round($totalBeforeRounding);
        $roundOffAmount = $roundedTotal - $totalBeforeRounding;

        $this->update([
            'subtotal' => $subtotal,
            'cgst_amount' => $cgstAmount,
            'sgst_amount' => $sgstAmount,
            'igst_amount' => $igstAmount,
            'total_tax_amount' => $totalTaxAmount,
            'round_off_amount' => $roundOffAmount,
            'total_amount' => $roundedTotal,
            'balance_amount' => $roundedTotal - $this->paid_amount,
        ]);
    }

    public function addPayment(float $amount, int $paymentMethodId, array $details = []): Payment
    {
        $payment = $this->payments()->create([
            'payment_number' => $this->generatePaymentNumber(),
            'customer_id' => $this->customer_id,
            'payment_method_id' => $paymentMethodId,
            'payment_date' => now()->toDateString(),
            'amount' => $amount,
            'reference_number' => $details['reference_number'] ?? null,
            'notes' => $details['notes'] ?? null,
            'created_by' => auth()->id(),
        ]);

        $this->updatePaymentStatus();

        return $payment;
    }

    public function updatePaymentStatus(): void
    {
        $totalPaid = $this->payments()->where('status', 'completed')->sum('amount');
        $balance = $this->total_amount - $totalPaid;

        $status = 'draft';
        if ($totalPaid >= $this->total_amount) {
            $status = 'paid';
        } elseif ($totalPaid > 0) {
            $status = 'partially_paid';
        } elseif ($this->due_date && $this->due_date->isPast()) {
            $status = 'overdue';
        } else {
            $status = 'sent';
        }

        $this->update([
            'paid_amount' => $totalPaid,
            'balance_amount' => $balance,
            'status' => $status,
        ]);

        // Update customer purchase stats
        if ($status === 'paid') {
            $this->customer->updatePurchaseStats($this->total_amount);
        }
    }

    public function getFormattedInvoiceNumberAttribute(): string
    {
        return 'INV-' . $this->invoice_number;
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && $this->due_date->isPast() && $this->balance_amount > 0;
    }

    public function getFormattedTotalAttribute(): string
    {
        return '₹' . number_format($this->total_amount, 2);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->whereIn('status', ['sent', 'partially_paid']);
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
                    ->where('due_date', '<', now());
    }

    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    // Auto-generate invoice number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invoice) {
            if (empty($invoice->invoice_number)) {
                $invoice->invoice_number = static::generateInvoiceNumber();
            }
        });
    }

    private static function generateInvoiceNumber(): string
    {
        $year = now()->format('Y');
        $month = now()->format('m');

        $lastInvoice = static::whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastInvoice ? (intval(substr($lastInvoice->invoice_number, -4)) + 1) : 1;

        return $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    private function generatePaymentNumber(): string
    {
        $prefix = 'PAY';
        $timestamp = now()->format('ymdHis');
        $random = rand(100, 999);

        return $prefix . $timestamp . $random;
    }
}
