<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'alternate_phone' => $this->alternate_phone,
            'date_of_birth' => $this->date_of_birth,
            'anniversary_date' => $this->anniversary_date,
            'gender' => $this->gender,
            
            // Address
            'address' => [
                'line_1' => $this->address_line_1,
                'line_2' => $this->address_line_2,
                'city' => $this->city,
                'state' => $this->state,
                'postal_code' => $this->postal_code,
                'country' => $this->country,
            ],
            
            // KYC Information (only for authenticated users)
            'kyc' => $this->when($request->user(), [
                'pan_number' => $this->pan_number,
                'aadhar_number' => $this->aadhar_number,
                'kyc_status' => $this->kyc_status,
                'kyc_verified_at' => $this->kyc_verified_at,
            ]),
            
            // Customer Status
            'customer_type' => $this->customer_type,
            'credit_limit' => $this->credit_limit,
            'is_active' => $this->is_active,
            
            // Relationships
            'addresses' => CustomerAddressResource::collection($this->whenLoaded('addresses')),
            'documents' => CustomerDocumentResource::collection($this->whenLoaded('documents')),
            'preferences' => new CustomerPreferenceResource($this->whenLoaded('preferences')),
            
            // Statistics (only for admin/staff)
            'statistics' => $this->when($request->user()?->hasRole(['SuperAdmin', 'Manager', 'SalesStaff']), [
                'total_purchases' => $this->whenLoaded('invoices', function () {
                    return $this->invoices->sum('total_amount');
                }),
                'total_orders' => $this->whenLoaded('invoices', function () {
                    return $this->invoices->count();
                }),
                'last_purchase_date' => $this->whenLoaded('invoices', function () {
                    return $this->invoices->max('created_at');
                }),
                'loyalty_points' => $this->whenLoaded('loyaltyPoints', function () {
                    return $this->loyaltyPoints->sum('points');
                }),
            ]),
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Authentication fields (only for the customer themselves)
            'email_verified_at' => $this->when($request->user()?->id === $this->id, $this->email_verified_at),
        ];
    }
}
