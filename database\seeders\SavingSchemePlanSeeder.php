<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SavingSchemePlan;

class SavingSchemePlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user or create a default admin user
        $adminUser = \App\Models\User::first();
        if (!$adminUser) {
            $adminUser = \App\Models\User::create([
                'name' => 'System Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        $plans = [
            [
                'plan_name' => 'Gold Monthly Saver',
                'description' => 'Save monthly towards your dream gold jewelry with attractive returns.',
                'plan_type' => 'monthly',
                'duration_months' => 12,
                'minimum_amount' => 1000.00,
                'maximum_amount' => 10000.00,
                'interest_rate' => 8.00,
                'bonus_percentage' => 5.00,
                'allow_partial_withdrawal' => false,
                'allow_premature_closure' => true,
                'premature_closure_penalty' => 2.00,
                'grace_period_days' => 7,
                'late_fee_amount' => 100.00,
                'is_active' => true,
                'terms_conditions' => [
                    'Minimum monthly payment of ₹1,000',
                    'Interest calculated annually at 8%',
                    'Bonus of 5% on maturity',
                    'Grace period of 7 days for payments',
                    'Late fee of ₹100 after grace period',
                    'Premature closure allowed with 2% penalty'
                ],
                'created_by' => $adminUser->id,
            ],
            [
                'plan_name' => 'Diamond Quarterly Plan',
                'description' => 'Quarterly savings plan for premium diamond jewelry purchases.',
                'plan_type' => 'quarterly',
                'duration_months' => 24,
                'minimum_amount' => 5000.00,
                'maximum_amount' => 25000.00,
                'interest_rate' => 10.00,
                'bonus_percentage' => 8.00,
                'allow_partial_withdrawal' => false,
                'allow_premature_closure' => true,
                'premature_closure_penalty' => 3.00,
                'grace_period_days' => 10,
                'late_fee_amount' => 250.00,
                'is_active' => true,
                'terms_conditions' => [
                    'Minimum quarterly payment of ₹5,000',
                    'Interest calculated annually at 10%',
                    'Bonus of 8% on maturity',
                    'Grace period of 10 days for payments',
                    'Late fee of ₹250 after grace period',
                    'Premature closure allowed with 3% penalty'
                ],
                'created_by' => $adminUser->id,
            ],
            [
                'plan_name' => 'Platinum Annual Scheme',
                'description' => 'Annual savings scheme for luxury platinum jewelry with maximum returns.',
                'plan_type' => 'yearly',
                'duration_months' => 36,
                'minimum_amount' => 25000.00,
                'maximum_amount' => 100000.00,
                'interest_rate' => 12.00,
                'bonus_percentage' => 10.00,
                'allow_partial_withdrawal' => true,
                'allow_premature_closure' => true,
                'premature_closure_penalty' => 1.00,
                'grace_period_days' => 15,
                'late_fee_amount' => 500.00,
                'is_active' => true,
                'terms_conditions' => [
                    'Minimum annual payment of ₹25,000',
                    'Interest calculated annually at 12%',
                    'Bonus of 10% on maturity',
                    'Partial withdrawal allowed after 1 year',
                    'Grace period of 15 days for payments',
                    'Late fee of ₹500 after grace period',
                    'Premature closure allowed with 1% penalty'
                ],
                'created_by' => $adminUser->id,
            ],
            [
                'plan_name' => 'Wedding Special Plan',
                'description' => 'Special 18-month plan for wedding jewelry with exclusive benefits.',
                'plan_type' => 'monthly',
                'duration_months' => 18,
                'minimum_amount' => 2000.00,
                'maximum_amount' => 15000.00,
                'interest_rate' => 9.00,
                'bonus_percentage' => 12.00,
                'allow_partial_withdrawal' => false,
                'allow_premature_closure' => true,
                'premature_closure_penalty' => 2.50,
                'grace_period_days' => 5,
                'late_fee_amount' => 150.00,
                'is_active' => true,
                'terms_conditions' => [
                    'Minimum monthly payment of ₹2,000',
                    'Interest calculated annually at 9%',
                    'Special wedding bonus of 12% on maturity',
                    'Exclusive access to wedding collection',
                    'Grace period of 5 days for payments',
                    'Late fee of ₹150 after grace period',
                    'Premature closure allowed with 2.5% penalty'
                ],
                'created_by' => $adminUser->id,
            ],
            [
                'plan_name' => 'Flexible Saver',
                'description' => 'Flexible monthly plan with lower commitment and easy terms.',
                'plan_type' => 'monthly',
                'duration_months' => 6,
                'minimum_amount' => 500.00,
                'maximum_amount' => 5000.00,
                'interest_rate' => 6.00,
                'bonus_percentage' => 3.00,
                'allow_partial_withdrawal' => true,
                'allow_premature_closure' => true,
                'premature_closure_penalty' => 1.00,
                'grace_period_days' => 10,
                'late_fee_amount' => 50.00,
                'is_active' => true,
                'terms_conditions' => [
                    'Minimum monthly payment of ₹500',
                    'Interest calculated annually at 6%',
                    'Bonus of 3% on maturity',
                    'Partial withdrawal allowed anytime',
                    'Grace period of 10 days for payments',
                    'Late fee of ₹50 after grace period',
                    'Premature closure allowed with 1% penalty'
                ],
                'created_by' => $adminUser->id,
            ],
        ];

        \DB::transaction(function () use ($plans, $adminUser) {
            foreach ($plans as $plan) {
                // Check if plan already exists
                $existingPlan = SavingSchemePlan::where('plan_name', $plan['plan_name'])->first();

                if (!$existingPlan) {
                    try {
                        // Use retry logic for creation
                        $createdPlan = SavingSchemePlan::createWithRetry($plan);
                        if ($createdPlan) {
                            echo "Created plan: {$plan['plan_name']}\n";
                        }
                    } catch (\Exception $e) {
                        echo "Failed to create plan {$plan['plan_name']}: " . $e->getMessage() . "\n";
                        // Continue with next plan instead of failing completely
                        continue;
                    }
                } else {
                    echo "Plan already exists: {$plan['plan_name']}\n";
                }
            }
        });
    }
}
