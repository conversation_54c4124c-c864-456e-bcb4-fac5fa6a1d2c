<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TestCase;
use App\Models\TestRun;
use App\Models\TestResult;
use App\Models\BugReport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class TestingController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_testing_dashboard')->only(['index']);
        $this->middleware('permission:manage_test_cases')->only(['testCases', 'createTestCase', 'updateTestCase', 'deleteTestCase']);
        $this->middleware('permission:run_tests')->only(['runTests', 'runTestSuite']);
        $this->middleware('permission:view_test_results')->only(['testResults', 'testHistory']);
        $this->middleware('permission:manage_bug_reports')->only(['bugReports', 'createBugReport', 'updateBugReport']);
    }

    /**
     * Testing dashboard
     */
    public function index()
    {
        $testingStats = [
            'total_test_cases' => TestCase::count(),
            'active_test_cases' => TestCase::where('is_active', true)->count(),
            'total_test_runs' => TestRun::count(),
            'test_runs_today' => TestRun::whereDate('created_at', today())->count(),
            'passed_tests_today' => TestResult::whereDate('created_at', today())
                                            ->where('status', 'passed')
                                            ->count(),
            'failed_tests_today' => TestResult::whereDate('created_at', today())
                                            ->where('status', 'failed')
                                            ->count(),
            'open_bugs' => BugReport::where('status', 'open')->count(),
            'critical_bugs' => BugReport::where('severity', 'critical')
                                      ->where('status', 'open')
                                      ->count(),
        ];

        $recentTestRuns = TestRun::with(['testResults', 'createdBy'])
                                ->orderByDesc('created_at')
                                ->limit(10)
                                ->get();

        $testCoverage = $this->getTestCoverage();
        $qualityMetrics = $this->getQualityMetrics();

        return view('admin.testing.index', compact(
            'testingStats', 'recentTestRuns', 'testCoverage', 'qualityMetrics'
        ));
    }

    /**
     * Test cases management
     */
    public function testCases(Request $request)
    {
        $query = TestCase::with(['testResults' => function ($q) {
            $q->latest()->limit(1);
        }]);

        // Filters
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('test_steps', 'like', "%{$search}%");
            });
        }

        $testCases = $query->orderBy('priority', 'desc')
                          ->orderBy('name')
                          ->paginate(20);

        $testCaseStats = [
            'total_cases' => TestCase::count(),
            'active_cases' => TestCase::where('is_active', true)->count(),
            'automated_cases' => TestCase::where('is_automated', true)->count(),
            'manual_cases' => TestCase::where('is_automated', false)->count(),
            'high_priority' => TestCase::where('priority', 'high')->count(),
            'medium_priority' => TestCase::where('priority', 'medium')->count(),
            'low_priority' => TestCase::where('priority', 'low')->count(),
        ];

        return view('admin.testing.test-cases', compact('testCases', 'testCaseStats'));
    }

    /**
     * Create test case
     */
    public function createTestCase(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|max:100',
            'priority' => 'required|in:low,medium,high,critical',
            'test_steps' => 'required|string',
            'expected_result' => 'required|string',
            'preconditions' => 'nullable|string',
            'test_data' => 'nullable|string',
            'is_automated' => 'boolean',
            'automation_script' => 'nullable|string',
        ]);

        $testCase = TestCase::create([
            'name' => $request->name,
            'description' => $request->description,
            'category' => $request->category,
            'priority' => $request->priority,
            'test_steps' => $request->test_steps,
            'expected_result' => $request->expected_result,
            'preconditions' => $request->preconditions,
            'test_data' => $request->test_data,
            'is_automated' => $request->boolean('is_automated'),
            'automation_script' => $request->automation_script,
            'created_by' => auth()->id(),
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Test case created successfully',
            'test_case_id' => $testCase->id,
        ]);
    }

    /**
     * Run test suite
     */
    public function runTestSuite(Request $request)
    {
        $request->validate([
            'test_suite' => 'required|string',
            'environment' => 'required|string',
            'test_case_ids' => 'nullable|array',
            'test_case_ids.*' => 'exists:test_cases,id',
        ]);

        try {
            $testRun = TestRun::create([
                'name' => $request->test_suite,
                'environment' => $request->environment,
                'status' => 'running',
                'started_at' => now(),
                'created_by' => auth()->id(),
            ]);

            // Get test cases to run
            $testCaseIds = $request->test_case_ids ?? TestCase::where('is_active', true)->pluck('id')->toArray();

            // Queue test execution
            dispatch(new \App\Jobs\ExecuteTestSuite($testRun->id, $testCaseIds));

            return response()->json([
                'success' => true,
                'message' => 'Test suite execution started',
                'test_run_id' => $testRun->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Test suite execution failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to start test suite execution: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Test results
     */
    public function testResults(Request $request)
    {
        $query = TestResult::with(['testCase', 'testRun', 'executedBy']);

        // Filters
        if ($request->filled('test_run_id')) {
            $query->where('test_run_id', $request->test_run_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('executed_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('executed_at', '<=', $request->date_to);
        }

        $testResults = $query->orderByDesc('executed_at')->paginate(50);

        $resultStats = [
            'total_results' => TestResult::count(),
            'passed_results' => TestResult::where('status', 'passed')->count(),
            'failed_results' => TestResult::where('status', 'failed')->count(),
            'skipped_results' => TestResult::where('status', 'skipped')->count(),
            'pass_rate' => $this->calculatePassRate(),
        ];

        return view('admin.testing.test-results', compact('testResults', 'resultStats'));
    }

    /**
     * Bug reports
     */
    public function bugReports(Request $request)
    {
        $query = BugReport::with(['reportedBy', 'assignedTo']);

        // Filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        $bugReports = $query->orderByDesc('created_at')->paginate(20);

        $bugStats = [
            'total_bugs' => BugReport::count(),
            'open_bugs' => BugReport::where('status', 'open')->count(),
            'in_progress_bugs' => BugReport::where('status', 'in_progress')->count(),
            'resolved_bugs' => BugReport::where('status', 'resolved')->count(),
            'critical_bugs' => BugReport::where('severity', 'critical')->count(),
            'high_bugs' => BugReport::where('severity', 'high')->count(),
        ];

        return view('admin.testing.bug-reports', compact('bugReports', 'bugStats'));
    }

    /**
     * Create bug report
     */
    public function createBugReport(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'steps_to_reproduce' => 'required|string',
            'expected_behavior' => 'required|string',
            'actual_behavior' => 'required|string',
            'severity' => 'required|in:low,medium,high,critical',
            'priority' => 'required|in:low,medium,high,critical',
            'environment' => 'required|string|max:100',
            'browser' => 'nullable|string|max:100',
            'os' => 'nullable|string|max:100',
            'assigned_to' => 'nullable|exists:users,id',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max
        ]);

        $bugReport = BugReport::create([
            'title' => $request->title,
            'description' => $request->description,
            'steps_to_reproduce' => $request->steps_to_reproduce,
            'expected_behavior' => $request->expected_behavior,
            'actual_behavior' => $request->actual_behavior,
            'severity' => $request->severity,
            'priority' => $request->priority,
            'environment' => $request->environment,
            'browser' => $request->browser,
            'os' => $request->os,
            'status' => 'open',
            'reported_by' => auth()->id(),
            'assigned_to' => $request->assigned_to,
        ]);

        // Handle file attachments
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('bug-reports', 'public');
                $bugReport->attachments()->create([
                    'file_path' => $path,
                    'file_name' => $file->getClientOriginalName(),
                    'file_size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Bug report created successfully',
            'bug_report_id' => $bugReport->id,
        ]);
    }

    /**
     * Performance testing
     */
    public function performanceTesting()
    {
        $performanceMetrics = [
            'page_load_times' => $this->getPageLoadTimes(),
            'api_response_times' => $this->getApiResponseTimes(),
            'database_query_times' => $this->getDatabaseQueryTimes(),
            'memory_usage' => $this->getMemoryUsage(),
            'cpu_usage' => $this->getCpuUsage(),
        ];

        $performanceTests = [
            'load_testing' => $this->getLoadTestResults(),
            'stress_testing' => $this->getStressTestResults(),
            'spike_testing' => $this->getSpikeTestResults(),
            'volume_testing' => $this->getVolumeTestResults(),
        ];

        return view('admin.testing.performance', compact('performanceMetrics', 'performanceTests'));
    }

    /**
     * Security testing
     */
    public function securityTesting()
    {
        $securityTests = [
            'vulnerability_scan' => $this->getVulnerabilityScanResults(),
            'penetration_test' => $this->getPenetrationTestResults(),
            'authentication_test' => $this->getAuthenticationTestResults(),
            'authorization_test' => $this->getAuthorizationTestResults(),
            'input_validation_test' => $this->getInputValidationTestResults(),
            'sql_injection_test' => $this->getSqlInjectionTestResults(),
            'xss_test' => $this->getXssTestResults(),
            'csrf_test' => $this->getCsrfTestResults(),
        ];

        $securityMetrics = [
            'security_score' => $this->calculateSecurityScore(),
            'vulnerabilities_found' => $this->getVulnerabilitiesCount(),
            'security_issues_resolved' => $this->getResolvedSecurityIssues(),
            'last_security_scan' => $this->getLastSecurityScanDate(),
        ];

        return view('admin.testing.security', compact('securityTests', 'securityMetrics'));
    }

    /**
     * Code quality analysis
     */
    public function codeQuality()
    {
        $codeQualityMetrics = [
            'code_coverage' => $this->getCodeCoverage(),
            'cyclomatic_complexity' => $this->getCyclomaticComplexity(),
            'code_duplication' => $this->getCodeDuplication(),
            'maintainability_index' => $this->getMaintainabilityIndex(),
            'technical_debt' => $this->getTechnicalDebt(),
        ];

        $codeAnalysis = [
            'static_analysis' => $this->getStaticAnalysisResults(),
            'code_style_check' => $this->getCodeStyleResults(),
            'dependency_analysis' => $this->getDependencyAnalysis(),
            'security_analysis' => $this->getSecurityAnalysisResults(),
        ];

        return view('admin.testing.code-quality', compact('codeQualityMetrics', 'codeAnalysis'));
    }

    /**
     * Run automated tests
     */
    public function runAutomatedTests(Request $request)
    {
        $testType = $request->get('test_type', 'unit'); // unit, feature, integration

        try {
            $output = '';
            $exitCode = 0;

            switch ($testType) {
                case 'unit':
                    Artisan::call('test', ['--testsuite' => 'Unit']);
                    break;
                case 'feature':
                    Artisan::call('test', ['--testsuite' => 'Feature']);
                    break;
                case 'integration':
                    Artisan::call('test', ['--testsuite' => 'Integration']);
                    break;
                default:
                    Artisan::call('test');
            }

            $output = Artisan::output();

            return response()->json([
                'success' => true,
                'message' => 'Automated tests completed',
                'output' => $output,
                'test_type' => $testType,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Test execution failed: ' . $e->getMessage(),
            ]);
        }
    }

    // Helper Methods
    private function getTestCoverage()
    {
        // This would integrate with code coverage tools
        return [
            'overall_coverage' => 85.5,
            'line_coverage' => 87.2,
            'branch_coverage' => 82.1,
            'function_coverage' => 91.3,
        ];
    }

    private function getQualityMetrics()
    {
        return [
            'code_quality_score' => 8.5,
            'maintainability_score' => 7.8,
            'reliability_score' => 9.1,
            'security_score' => 8.9,
        ];
    }

    private function calculatePassRate()
    {
        $totalTests = TestResult::count();
        $passedTests = TestResult::where('status', 'passed')->count();
        
        return $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;
    }

    // Placeholder methods for performance and security testing
    private function getPageLoadTimes() { return []; }
    private function getApiResponseTimes() { return []; }
    private function getDatabaseQueryTimes() { return []; }
    private function getMemoryUsage() { return []; }
    private function getCpuUsage() { return []; }
    private function getLoadTestResults() { return []; }
    private function getStressTestResults() { return []; }
    private function getSpikeTestResults() { return []; }
    private function getVolumeTestResults() { return []; }
    private function getVulnerabilityScanResults() { return []; }
    private function getPenetrationTestResults() { return []; }
    private function getAuthenticationTestResults() { return []; }
    private function getAuthorizationTestResults() { return []; }
    private function getInputValidationTestResults() { return []; }
    private function getSqlInjectionTestResults() { return []; }
    private function getXssTestResults() { return []; }
    private function getCsrfTestResults() { return []; }
    private function calculateSecurityScore() { return 85; }
    private function getVulnerabilitiesCount() { return 0; }
    private function getResolvedSecurityIssues() { return 15; }
    private function getLastSecurityScanDate() { return now()->subDays(1); }
    private function getCodeCoverage() { return 85.5; }
    private function getCyclomaticComplexity() { return 2.3; }
    private function getCodeDuplication() { return 3.2; }
    private function getMaintainabilityIndex() { return 78; }
    private function getTechnicalDebt() { return '2.5 hours'; }
    private function getStaticAnalysisResults() { return []; }
    private function getCodeStyleResults() { return []; }
    private function getDependencyAnalysis() { return []; }
    private function getSecurityAnalysisResults() { return []; }
}
