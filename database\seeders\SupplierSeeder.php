<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Supplier;

class SupplierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $suppliers = [
            [
                'name' => 'Mumbai Gold Suppliers',
                'company_name' => 'Mumbai Gold Suppliers Pvt Ltd',
                'gst_number' => '27AAAAA1234A1Z5',
                'phone' => '+91-9876543210',
                'email' => '<EMAIL>',
                'address' => '123 Zaveri Bazaar, Mumbai - 400002',
                'city' => 'Mumbai',
                'state' => 'Maharashtra',
                'pincode' => '400002',
                'contact_person' => '<PERSON><PERSON>',
                'credit_limit' => 500000.00,
                'credit_days' => 30,
                'notes' => 'Primary gold supplier with excellent quality',
                'is_active' => true,
            ],
            [
                'name' => 'Silver Craft Industries',
                'company_name' => 'Silver Craft Industries Ltd',
                'gst_number' => '27BBBBB5678B2Z6',
                'phone' => '+91-9876543211',
                'email' => '<EMAIL>',
                'address' => '456 Silver Market, Mumbai - 400003',
                'city' => 'Mumbai',
                'state' => 'Maharashtra',
                'pincode' => '400003',
                'contact_person' => 'Amit Patel',
                'credit_limit' => 200000.00,
                'credit_days' => 15,
                'notes' => 'Reliable silver supplier with competitive rates',
                'is_active' => true,
            ],
            [
                'name' => 'Diamond House',
                'company_name' => 'Diamond House Pvt Ltd',
                'gst_number' => '24CCCCC9012C3Z7',
                'phone' => '+91-9876543212',
                'email' => '<EMAIL>',
                'address' => '789 Diamond District, Surat - 395003',
                'city' => 'Surat',
                'state' => 'Gujarat',
                'pincode' => '395003',
                'contact_person' => 'Kiran Modi',
                'credit_limit' => 1000000.00,
                'credit_days' => 45,
                'notes' => 'Premium diamond supplier with certified stones',
                'is_active' => true,
            ],
            [
                'name' => 'Packaging Solutions',
                'company_name' => 'Packaging Solutions India',
                'gst_number' => '27DDDDD3456D4Z8',
                'phone' => '+91-9876543213',
                'email' => '<EMAIL>',
                'address' => '321 Industrial Area, Mumbai - 400004',
                'city' => 'Mumbai',
                'state' => 'Maharashtra',
                'pincode' => '400004',
                'contact_person' => 'Suresh Kumar',
                'credit_limit' => 50000.00,
                'credit_days' => 7,
                'notes' => 'Jewelry boxes, pouches, and packaging materials',
                'is_active' => true,
            ],
            [
                'name' => 'Tools & Equipment Co',
                'company_name' => 'Tools & Equipment Company',
                'gst_number' => '27EEEEE7890E5Z9',
                'phone' => '+91-9876543214',
                'email' => '<EMAIL>',
                'address' => '654 Tool Market, Mumbai - 400005',
                'city' => 'Mumbai',
                'state' => 'Maharashtra',
                'pincode' => '400005',
                'contact_person' => 'Vikram Singh',
                'credit_limit' => 100000.00,
                'credit_days' => 20,
                'notes' => 'Jewelry making tools and equipment',
                'is_active' => true,
            ],
        ];

        foreach ($suppliers as $supplier) {
            Supplier::create($supplier);
        }
    }
}
