<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\ActivityLog;
use App\Models\SecurityEvent;
use App\Models\LoginAttempt;
use App\Models\BlockedIp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SecurityController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_security_dashboard')->only(['index']);
        $this->middleware('permission:manage_user_access')->only(['accessControl', 'updateUserAccess']);
        $this->middleware('permission:view_security_logs')->only(['loginAttempts', 'securityEvents', 'auditLogs']);
        $this->middleware('permission:manage_ip_blocking')->only(['ipBlocking', 'blockIp', 'unblockIp']);
        $this->middleware('permission:manage_security_settings')->only(['settings', 'updateSettings']);
        $this->middleware('permission:security_management');
    }

    /**
     * Security dashboard
     */
    public function index()
    {
        $securityMetrics = [
            'failed_logins_today' => LoginAttempt::where('successful', false)
                                               ->whereDate('created_at', today())
                                               ->count(),
            'blocked_ips_count' => BlockedIp::where('is_active', true)->count(),
            'security_events_today' => SecurityEvent::whereDate('created_at', today())->count(),
            'suspicious_activities' => $this->getSuspiciousActivitiesCount(),
            'password_strength_score' => $this->getAveragePasswordStrength(),
            'two_factor_adoption' => $this->getTwoFactorAdoptionRate(),
        ];

        $recentSecurityEvents = SecurityEvent::with('user')
                                           ->orderByDesc('created_at')
                                           ->limit(10)
                                           ->get();

        $loginAttempts = $this->getRecentLoginAttempts();
        $securityAlerts = $this->getActiveSecurityAlerts();

        return view('admin.security.index', compact(
            'securityMetrics', 'recentSecurityEvents', 'loginAttempts', 'securityAlerts'
        ));
    }

    /**
     * User access control
     */
    public function accessControl(Request $request)
    {
        $query = User::with(['roles', 'permissions']);

        // Search and filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        $users = $query->paginate(20);

        $accessStats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'locked_users' => User::where('is_locked', true)->count(),
            'users_with_2fa' => User::whereNotNull('two_factor_secret')->count(),
        ];

        return view('admin.security.access-control', compact('users', 'accessStats'));
    }

    /**
     * Login attempts monitoring
     */
    public function loginAttempts(Request $request)
    {
        $dateRange = $request->get('date_range', '7');
        $status = $request->get('status', 'all'); // all, successful, failed

        $query = LoginAttempt::with('user');

        if ($dateRange !== 'all') {
            $query->where('created_at', '>=', now()->subDays($dateRange));
        }

        if ($status !== 'all') {
            $query->where('successful', $status === 'successful');
        }

        $attempts = $query->orderByDesc('created_at')->paginate(50);

        $attemptStats = [
            'total_attempts' => LoginAttempt::where('created_at', '>=', now()->subDays($dateRange))->count(),
            'successful_attempts' => LoginAttempt::where('successful', true)
                                               ->where('created_at', '>=', now()->subDays($dateRange))
                                               ->count(),
            'failed_attempts' => LoginAttempt::where('successful', false)
                                           ->where('created_at', '>=', now()->subDays($dateRange))
                                           ->count(),
            'unique_ips' => LoginAttempt::where('created_at', '>=', now()->subDays($dateRange))
                                      ->distinct('ip_address')
                                      ->count(),
        ];

        return view('admin.security.login-attempts', compact('attempts', 'attemptStats', 'dateRange', 'status'));
    }

    /**
     * IP blocking management
     */
    public function ipBlocking(Request $request)
    {
        if ($request->isMethod('post')) {
            return $this->blockIp($request);
        }

        $blockedIps = BlockedIp::orderByDesc('created_at')->paginate(20);
        
        $blockingStats = [
            'total_blocked' => BlockedIp::count(),
            'active_blocks' => BlockedIp::where('is_active', true)->count(),
            'auto_blocks' => BlockedIp::where('blocked_by_system', true)->count(),
            'manual_blocks' => BlockedIp::where('blocked_by_system', false)->count(),
        ];

        return view('admin.security.ip-blocking', compact('blockedIps', 'blockingStats'));
    }

    /**
     * Block an IP address
     */
    public function blockIp(Request $request)
    {
        $request->validate([
            'ip_address' => 'required|ip',
            'reason' => 'required|string|max:255',
            'duration' => 'nullable|integer|min:1',
            'duration_unit' => 'required|in:minutes,hours,days,permanent',
        ]);

        $expiresAt = null;
        if ($request->duration_unit !== 'permanent') {
            $expiresAt = match($request->duration_unit) {
                'minutes' => now()->addMinutes($request->duration),
                'hours' => now()->addHours($request->duration),
                'days' => now()->addDays($request->duration),
            };
        }

        BlockedIp::create([
            'ip_address' => $request->ip_address,
            'reason' => $request->reason,
            'blocked_by_user_id' => auth()->id(),
            'blocked_by_system' => false,
            'expires_at' => $expiresAt,
            'is_active' => true,
        ]);

        // Log security event
        SecurityEvent::create([
            'event_type' => 'ip_blocked',
            'description' => "IP address {$request->ip_address} blocked manually",
            'ip_address' => $request->ip_address,
            'user_id' => auth()->id(),
            'severity' => 'medium',
        ]);

        return redirect()->route('admin.security.ip-blocking')
                        ->with('success', 'IP address blocked successfully.');
    }

    /**
     * Unblock an IP address
     */
    public function unblockIp(BlockedIp $blockedIp)
    {
        $blockedIp->update(['is_active' => false]);

        SecurityEvent::create([
            'event_type' => 'ip_unblocked',
            'description' => "IP address {$blockedIp->ip_address} unblocked",
            'ip_address' => $blockedIp->ip_address,
            'user_id' => auth()->id(),
            'severity' => 'low',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'IP address unblocked successfully.'
        ]);
    }

    /**
     * Security events log
     */
    public function securityEvents(Request $request)
    {
        $eventType = $request->get('event_type', 'all');
        $severity = $request->get('severity', 'all');
        $dateRange = $request->get('date_range', '7');

        $query = SecurityEvent::with('user');

        if ($eventType !== 'all') {
            $query->where('event_type', $eventType);
        }

        if ($severity !== 'all') {
            $query->where('severity', $severity);
        }

        if ($dateRange !== 'all') {
            $query->where('created_at', '>=', now()->subDays($dateRange));
        }

        $events = $query->orderByDesc('created_at')->paginate(50);

        $eventStats = [
            'total_events' => SecurityEvent::where('created_at', '>=', now()->subDays($dateRange))->count(),
            'high_severity' => SecurityEvent::where('severity', 'high')
                                          ->where('created_at', '>=', now()->subDays($dateRange))
                                          ->count(),
            'medium_severity' => SecurityEvent::where('severity', 'medium')
                                            ->where('created_at', '>=', now()->subDays($dateRange))
                                            ->count(),
            'low_severity' => SecurityEvent::where('severity', 'low')
                                         ->where('created_at', '>=', now()->subDays($dateRange))
                                         ->count(),
        ];

        return view('admin.security.events', compact('events', 'eventStats', 'eventType', 'severity', 'dateRange'));
    }

    /**
     * Password policy management
     */
    public function passwordPolicy(Request $request)
    {
        if ($request->isMethod('post')) {
            return $this->updatePasswordPolicy($request);
        }

        $currentPolicy = [
            'min_length' => config('security.password.min_length', 8),
            'require_uppercase' => config('security.password.require_uppercase', true),
            'require_lowercase' => config('security.password.require_lowercase', true),
            'require_numbers' => config('security.password.require_numbers', true),
            'require_symbols' => config('security.password.require_symbols', true),
            'max_age_days' => config('security.password.max_age_days', 90),
            'history_count' => config('security.password.history_count', 5),
        ];

        $passwordStats = [
            'users_with_weak_passwords' => $this->getUsersWithWeakPasswords(),
            'users_with_expired_passwords' => $this->getUsersWithExpiredPasswords(),
            'average_password_age' => $this->getAveragePasswordAge(),
            'password_reuse_violations' => $this->getPasswordReuseViolations(),
        ];

        return view('admin.security.password-policy', compact('currentPolicy', 'passwordStats'));
    }

    /**
     * Two-factor authentication management
     */
    public function twoFactorAuth(Request $request)
    {
        $users = User::with('roles')->paginate(20);
        
        $twoFactorStats = [
            'total_users' => User::count(),
            'users_with_2fa' => User::whereNotNull('two_factor_secret')->count(),
            'adoption_rate' => $this->getTwoFactorAdoptionRate(),
            'recent_2fa_setups' => User::whereNotNull('two_factor_secret')
                                     ->where('two_factor_confirmed_at', '>=', now()->subDays(30))
                                     ->count(),
        ];

        return view('admin.security.two-factor-auth', compact('users', 'twoFactorStats'));
    }

    /**
     * Force password reset for user
     */
    public function forcePasswordReset(User $user)
    {
        $user->update([
            'password_expires_at' => now(),
            'must_change_password' => true,
        ]);

        SecurityEvent::create([
            'event_type' => 'password_reset_forced',
            'description' => "Password reset forced for user: {$user->email}",
            'user_id' => $user->id,
            'admin_user_id' => auth()->id(),
            'severity' => 'medium',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password reset forced successfully.'
        ]);
    }

    /**
     * Lock/unlock user account
     */
    public function toggleUserLock(User $user)
    {
        $user->update(['is_locked' => !$user->is_locked]);

        $action = $user->is_locked ? 'locked' : 'unlocked';
        
        SecurityEvent::create([
            'event_type' => "account_{$action}",
            'description' => "User account {$action}: {$user->email}",
            'user_id' => $user->id,
            'admin_user_id' => auth()->id(),
            'severity' => 'medium',
        ]);

        return response()->json([
            'success' => true,
            'message' => "User account {$action} successfully.",
            'is_locked' => $user->is_locked
        ]);
    }

    /**
     * Security audit report
     */
    public function auditReport(Request $request)
    {
        $reportType = $request->get('type', 'summary'); // summary, detailed, compliance
        $dateRange = $request->get('date_range', '30');

        $auditData = [
            'security_events' => $this->getSecurityEventsForAudit($dateRange),
            'login_analysis' => $this->getLoginAnalysisForAudit($dateRange),
            'access_violations' => $this->getAccessViolationsForAudit($dateRange),
            'policy_compliance' => $this->getPolicyComplianceForAudit(),
            'recommendations' => $this->getSecurityRecommendations(),
        ];

        if ($request->get('format') === 'pdf') {
            return $this->generateAuditReportPDF($auditData, $reportType, $dateRange);
        }

        return view('admin.security.audit-report', compact('auditData', 'reportType', 'dateRange'));
    }

    // Helper Methods
    private function getSuspiciousActivitiesCount()
    {
        return SecurityEvent::where('severity', 'high')
                           ->whereDate('created_at', today())
                           ->count();
    }

    private function getAveragePasswordStrength()
    {
        // Implementation for password strength calculation
        return 75; // Placeholder
    }

    private function getTwoFactorAdoptionRate()
    {
        $totalUsers = User::count();
        $usersWithTwoFactor = User::whereNotNull('two_factor_secret')->count();
        
        return $totalUsers > 0 ? round(($usersWithTwoFactor / $totalUsers) * 100, 1) : 0;
    }

    private function getRecentLoginAttempts()
    {
        return LoginAttempt::with('user')
                         ->orderByDesc('created_at')
                         ->limit(10)
                         ->get();
    }

    private function getActiveSecurityAlerts()
    {
        return SecurityEvent::where('severity', 'high')
                           ->where('created_at', '>=', now()->subHours(24))
                           ->orderByDesc('created_at')
                           ->get();
    }

    private function updatePasswordPolicy(Request $request)
    {
        // Implementation for updating password policy
        return redirect()->route('admin.security.password-policy')
                        ->with('success', 'Password policy updated successfully.');
    }

    private function getUsersWithWeakPasswords()
    {
        // Implementation for identifying users with weak passwords
        return 0;
    }

    private function getUsersWithExpiredPasswords()
    {
        return User::where('password_expires_at', '<', now())->count();
    }

    private function getAveragePasswordAge()
    {
        // Implementation for calculating average password age
        return 45; // days
    }

    private function getPasswordReuseViolations()
    {
        // Implementation for password reuse violations
        return 0;
    }

    private function getSecurityEventsForAudit($days) { return []; }
    private function getLoginAnalysisForAudit($days) { return []; }
    private function getAccessViolationsForAudit($days) { return []; }
    private function getPolicyComplianceForAudit() { return []; }
    private function getSecurityRecommendations() { return []; }
    private function generateAuditReportPDF($data, $type, $range) { return response()->json(['message' => 'PDF generation not implemented']); }
}
