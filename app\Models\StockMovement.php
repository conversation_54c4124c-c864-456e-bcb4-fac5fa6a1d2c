<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StockMovement extends Model
{
    protected $fillable = [
        'inventory_id',
        'movement_type',
        'quantity',
        'reference_type',
        'reference_id',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function inventory(): BelongsTo
    {
        return $this->belongsTo(Inventory::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeStockIn($query)
    {
        return $query->whereIn('movement_type', ['stock_in', 'initial_stock', 'purchase', 'return']);
    }

    public function scopeStockOut($query)
    {
        return $query->whereIn('movement_type', ['stock_out', 'sale', 'damage', 'adjustment']);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('movement_type', $type);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    // Accessors
    public function getMovementTypeDisplayAttribute()
    {
        $types = [
            'initial_stock' => 'Initial Stock',
            'stock_in' => 'Stock In',
            'stock_out' => 'Stock Out',
            'purchase' => 'Purchase',
            'sale' => 'Sale',
            'return' => 'Return',
            'damage' => 'Damage',
            'adjustment' => 'Adjustment',
            'transfer' => 'Transfer',
            'bulk_update' => 'Bulk Update',
        ];

        return $types[$this->movement_type] ?? ucfirst(str_replace('_', ' ', $this->movement_type));
    }

    public function getIsStockInAttribute()
    {
        return in_array($this->movement_type, ['stock_in', 'initial_stock', 'purchase', 'return']);
    }

    public function getIsStockOutAttribute()
    {
        return in_array($this->movement_type, ['stock_out', 'sale', 'damage', 'adjustment']);
    }
}
