<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CustomerInteraction extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'user_id',
        'interaction_type',
        'interaction_channel',
        'interaction_direction',
        'subject',
        'description',
        'products_discussed',
        'categories_discussed',
        'budget_discussed',
        'outcome',
        'customer_satisfaction',
        'requires_follow_up',
        'follow_up_date',
        'follow_up_notes',
        'duration_minutes',
        'effort_level',
        'related_invoice_id',
        'related_estimate_id',
        'related_reference_number',
        'attachments',
        'recordings',
        'is_first_interaction',
        'interaction_sequence_number',
        'scheduled_at',
        'completed_at',
        'tags',
        'internal_notes',
    ];

    protected $casts = [
        'products_discussed' => 'array',
        'categories_discussed' => 'array',
        'budget_discussed' => 'decimal:2',
        'requires_follow_up' => 'boolean',
        'follow_up_date' => 'date',
        'attachments' => 'array',
        'recordings' => 'array',
        'is_first_interaction' => 'boolean',
        'scheduled_at' => 'datetime',
        'completed_at' => 'datetime',
        'tags' => 'array',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($interaction) {
            // Set sequence number
            $lastSequence = static::where('customer_id', $interaction->customer_id)
                                 ->max('interaction_sequence_number') ?? 0;
            $interaction->interaction_sequence_number = $lastSequence + 1;

            // Check if this is first interaction
            $interaction->is_first_interaction = $interaction->interaction_sequence_number === 1;

            // Set completed_at if not scheduled
            if (!$interaction->scheduled_at) {
                $interaction->completed_at = now();
            }
        });
    }

    /**
     * Relationships
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function relatedInvoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'related_invoice_id');
    }

    public function relatedEstimate(): BelongsTo
    {
        return $this->belongsTo(Estimate::class, 'related_estimate_id');
    }

    /**
     * Scopes
     */
    public function scopeRequiresFollowUp($query)
    {
        return $query->where('requires_follow_up', true)
                    ->whereNull('completed_at');
    }

    public function scopeOverdueFollowUp($query)
    {
        return $query->where('requires_follow_up', true)
                    ->where('follow_up_date', '<', now())
                    ->whereNull('completed_at');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('interaction_type', $type);
    }

    public function scopeByChannel($query, $channel)
    {
        return $query->where('interaction_channel', $channel);
    }

    public function scopeInbound($query)
    {
        return $query->where('interaction_direction', 'inbound');
    }

    public function scopeOutbound($query)
    {
        return $query->where('interaction_direction', 'outbound');
    }

    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    public function scopeScheduled($query)
    {
        return $query->whereNotNull('scheduled_at')
                    ->whereNull('completed_at');
    }

    public function scopePending($query)
    {
        return $query->whereNotNull('scheduled_at')
                    ->whereNull('completed_at');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Accessors
     */
    public function getInteractionTypeDisplayAttribute()
    {
        $types = [
            'visit' => 'Store Visit',
            'phone_call' => 'Phone Call',
            'email' => 'Email',
            'sms' => 'SMS',
            'whatsapp' => 'WhatsApp',
            'inquiry' => 'Inquiry',
            'complaint' => 'Complaint',
            'compliment' => 'Compliment',
            'follow_up' => 'Follow-up',
            'appointment' => 'Appointment',
            'purchase' => 'Purchase',
            'return' => 'Return',
            'exchange' => 'Exchange',
            'consultation' => 'Consultation',
            'custom_design' => 'Custom Design',
            'repair_service' => 'Repair Service',
        ];

        return $types[$this->interaction_type] ?? ucfirst(str_replace('_', ' ', $this->interaction_type));
    }

    public function getInteractionChannelDisplayAttribute()
    {
        $channels = [
            'in_store' => 'In Store',
            'phone' => 'Phone',
            'email' => 'Email',
            'sms' => 'SMS',
            'whatsapp' => 'WhatsApp',
            'website' => 'Website',
            'social_media' => 'Social Media',
        ];

        return $channels[$this->interaction_channel] ?? ucfirst(str_replace('_', ' ', $this->interaction_channel));
    }

    public function getOutcomeDisplayAttribute()
    {
        $outcomes = [
            'information_provided' => 'Information Provided',
            'appointment_scheduled' => 'Appointment Scheduled',
            'quote_given' => 'Quote Given',
            'purchase_made' => 'Purchase Made',
            'follow_up_required' => 'Follow-up Required',
            'no_interest' => 'No Interest',
            'complaint_resolved' => 'Complaint Resolved',
            'issue_escalated' => 'Issue Escalated',
            'custom_order_placed' => 'Custom Order Placed',
        ];

        return $outcomes[$this->outcome] ?? ucfirst(str_replace('_', ' ', $this->outcome ?? ''));
    }

    public function getCustomerSatisfactionDisplayAttribute()
    {
        $satisfaction = [
            'very_satisfied' => 'Very Satisfied',
            'satisfied' => 'Satisfied',
            'neutral' => 'Neutral',
            'dissatisfied' => 'Dissatisfied',
            'very_dissatisfied' => 'Very Dissatisfied',
        ];

        return $satisfaction[$this->customer_satisfaction] ?? 'Not Rated';
    }

    public function getEffortLevelDisplayAttribute()
    {
        $levels = [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
        ];

        return $levels[$this->effort_level] ?? 'Medium';
    }

    public function getInteractionTypeColorAttribute()
    {
        $colors = [
            'visit' => 'blue',
            'phone_call' => 'green',
            'email' => 'purple',
            'sms' => 'yellow',
            'whatsapp' => 'green',
            'inquiry' => 'blue',
            'complaint' => 'red',
            'compliment' => 'green',
            'follow_up' => 'orange',
            'appointment' => 'purple',
            'purchase' => 'green',
            'return' => 'red',
            'exchange' => 'yellow',
            'consultation' => 'blue',
            'custom_design' => 'purple',
            'repair_service' => 'orange',
        ];

        return $colors[$this->interaction_type] ?? 'gray';
    }

    public function getSatisfactionColorAttribute()
    {
        $colors = [
            'very_satisfied' => 'green',
            'satisfied' => 'blue',
            'neutral' => 'yellow',
            'dissatisfied' => 'orange',
            'very_dissatisfied' => 'red',
        ];

        return $colors[$this->customer_satisfaction] ?? 'gray';
    }

    public function getIsOverdueAttribute()
    {
        return $this->requires_follow_up &&
               $this->follow_up_date &&
               $this->follow_up_date < now() &&
               !$this->completed_at;
    }

    public function getDurationDisplayAttribute()
    {
        if (!$this->duration_minutes) {
            return 'Not recorded';
        }

        if ($this->duration_minutes < 60) {
            return $this->duration_minutes . ' minutes';
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        return $hours . 'h ' . ($minutes > 0 ? $minutes . 'm' : '');
    }

    public function getProductsDiscussedDisplayAttribute()
    {
        if (!$this->products_discussed) {
            return 'None specified';
        }

        $products = Product::whereIn('id', $this->products_discussed)
                          ->pluck('name')
                          ->toArray();

        return implode(', ', array_slice($products, 0, 3)) .
               (count($products) > 3 ? ' and ' . (count($products) - 3) . ' more' : '');
    }

    public function getCategoriesDiscussedDisplayAttribute()
    {
        if (!$this->categories_discussed) {
            return 'None specified';
        }

        $categories = Category::whereIn('id', $this->categories_discussed)
                             ->pluck('name')
                             ->toArray();

        return implode(', ', $categories);
    }

    /**
     * Mark interaction as completed
     */
    public function markAsCompleted($outcome = null, $notes = null)
    {
        $this->update([
            'completed_at' => now(),
            'outcome' => $outcome ?: $this->outcome,
            'internal_notes' => $notes ? ($this->internal_notes . "\n\n" . $notes) : $this->internal_notes,
        ]);

        return $this;
    }

    /**
     * Schedule follow-up
     */
    public function scheduleFollowUp($date, $notes = null)
    {
        $this->update([
            'requires_follow_up' => true,
            'follow_up_date' => $date,
            'follow_up_notes' => $notes,
        ]);

        return $this;
    }

    /**
     * Add attachment
     */
    public function addAttachment($filePath, $description = null)
    {
        $attachments = $this->attachments ?: [];
        $attachments[] = [
            'path' => $filePath,
            'description' => $description,
            'uploaded_at' => now()->toISOString(),
            'uploaded_by' => auth()->id(),
        ];

        $this->update(['attachments' => $attachments]);

        return $this;
    }

    /**
     * Add tag
     */
    public function addTag($tag)
    {
        $tags = $this->tags ?: [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->update(['tags' => $tags]);
        }

        return $this;
    }

    /**
     * Remove tag
     */
    public function removeTag($tag)
    {
        $tags = $this->tags ?: [];
        $tags = array_values(array_filter($tags, function ($t) use ($tag) {
            return $t !== $tag;
        }));

        $this->update(['tags' => $tags]);

        return $this;
    }

    /**
     * Get interaction summary
     */
    public function getSummary()
    {
        return [
            'type' => $this->interaction_type_display,
            'channel' => $this->interaction_channel_display,
            'direction' => ucfirst($this->interaction_direction),
            'outcome' => $this->outcome_display,
            'satisfaction' => $this->customer_satisfaction_display,
            'duration' => $this->duration_display,
            'staff_member' => $this->user->name,
            'date' => $this->created_at->format('M d, Y'),
            'requires_follow_up' => $this->requires_follow_up,
            'is_overdue' => $this->is_overdue,
        ];
    }

    /**
     * Create follow-up interaction
     */
    public function createFollowUp($data)
    {
        $followUpData = array_merge($data, [
            'customer_id' => $this->customer_id,
            'interaction_type' => 'follow_up',
            'related_reference_number' => "Follow-up for #{$this->id}",
        ]);

        $followUp = static::create($followUpData);

        // Mark this interaction as completed
        $this->update([
            'requires_follow_up' => false,
            'completed_at' => now(),
        ]);

        return $followUp;
    }
}
