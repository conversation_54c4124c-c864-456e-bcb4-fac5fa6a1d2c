<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\InventoryItem;
use App\Models\Product;
use App\Models\Location;
use App\Models\Customer;
use App\Services\InventoryService;
use App\Services\BarcodeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class InventoryItemController extends Controller
{
    protected $inventoryService;
    protected $barcodeService;

    public function __construct(InventoryService $inventoryService, BarcodeService $barcodeService)
    {
        $this->inventoryService = $inventoryService;
        $this->barcodeService = $barcodeService;
    }

    /**
     * Display a listing of inventory items
     */
    public function index(Request $request)
    {
        $query = InventoryItem::with(['product.category', 'product.metal', 'location', 'soldToCustomer'])
                             ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('unique_tag', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%")
                  ->orWhere('huid_number', 'like', "%{$search}%")
                  ->orWhere('certificate_number', 'like', "%{$search}%")
                  ->orWhereHas('product', function ($productQuery) use ($search) {
                      $productQuery->where('name', 'like', "%{$search}%")
                                  ->orWhere('sku', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by location
        if ($request->filled('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        // Filter by product
        if ($request->filled('product_id')) {
            $query->where('product_id', $request->product_id);
        }

        // Filter by condition
        if ($request->filled('condition')) {
            $query->where('condition', $request->condition);
        }

        // Filter by verification status
        if ($request->filled('verification_status')) {
            if ($request->verification_status === 'needs_verification') {
                $query->needsVerification();
            } elseif ($request->verification_status === 'recently_verified') {
                $query->where('last_verified_at', '>=', now()->subDays(30));
            }
        }

        // Filter by warranty status
        if ($request->filled('warranty_status')) {
            if ($request->warranty_status === 'expiring') {
                $query->warrantyExpiring();
            } elseif ($request->warranty_status === 'valid') {
                $query->withWarranty();
            }
        }

        $items = $query->paginate(20);

        // Get filter options
        $locations = Location::active()->orderBy('name')->get();
        $products = Product::active()->orderBy('name')->get();

        // Get statistics
        $stats = $this->inventoryService->getInventoryAnalytics($request->location_id);

        return view('admin.inventory-items.index', compact('items', 'locations', 'products', 'stats'));
    }

    /**
     * Show the form for creating a new inventory item
     */
    public function create(Request $request)
    {
        $products = Product::active()->with(['category', 'metal'])->orderBy('name')->get();
        $locations = Location::active()->orderBy('name')->get();

        // Pre-select product if provided
        $selectedProduct = null;
        if ($request->filled('product_id')) {
            $selectedProduct = Product::find($request->product_id);
        }

        return view('admin.inventory-items.create', compact('products', 'locations', 'selectedProduct'));
    }

    /**
     * Store a newly created inventory item
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'location_id' => 'required|exists:locations,id',
            'actual_weight' => 'nullable|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'net_metal_weight' => 'nullable|numeric|min:0',
            'purity' => 'nullable|string|max:20',
            'dimensions' => 'nullable|array',
            'dimensions.length' => 'nullable|numeric|min:0',
            'dimensions.width' => 'nullable|numeric|min:0',
            'dimensions.height' => 'nullable|numeric|min:0',
            'dimensions.diameter' => 'nullable|numeric|min:0',
            'cost_price' => 'required|numeric|min:0',
            'making_charges' => 'nullable|numeric|min:0',
            'stone_charges' => 'nullable|numeric|min:0',
            'other_charges' => 'nullable|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'mrp' => 'nullable|numeric|min:0',
            'condition' => 'required|in:new,excellent,good,fair,poor',
            'shelf_location' => 'nullable|string|max:100',
            'display_location' => 'nullable|string|max:100',
            'is_on_display' => 'boolean',
            'huid_number' => 'nullable|string|max:50|unique:inventory_items,huid_number',
            'certificate_number' => 'nullable|string|max:100|unique:inventory_items,certificate_number',
            'insurance_value' => 'nullable|numeric|min:0',
            'insurance_policy_number' => 'nullable|string|max:100',
            'warranty_start_date' => 'nullable|date',
            'warranty_end_date' => 'nullable|date|after_or_equal:warranty_start_date',
            'warranty_terms' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'photos.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        try {
            // Handle photo uploads
            $photoData = [];
            if ($request->hasFile('photos')) {
                foreach ($request->file('photos') as $index => $photo) {
                    $path = $photo->store('inventory-items', 'public');
                    $photoData[] = [
                        'path' => $path,
                        'description' => $request->input("photo_descriptions.{$index}"),
                        'uploaded_at' => now()->toISOString(),
                        'uploaded_by' => auth()->id(),
                    ];
                }
            }

            if (!empty($photoData)) {
                $validated['photos'] = $photoData;
            }

            // Create inventory item using service
            $item = $this->inventoryService->createInventoryItem($validated);

            return redirect()->route('admin.inventory-items.show', $item)
                ->with('success', 'Inventory item created successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Error creating inventory item: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified inventory item
     */
    public function show(InventoryItem $inventoryItem)
    {
        $inventoryItem->load([
            'product.category',
            'product.metal',
            'location',
            'soldToCustomer',
            'creator',
            'updater',
            'lastVerifiedBy',
            'stockMovements.createdBy'
        ]);

        // Get related items (same product, different items)
        $relatedItems = InventoryItem::where('product_id', $inventoryItem->product_id)
                                   ->where('id', '!=', $inventoryItem->id)
                                   ->with(['location'])
                                   ->limit(5)
                                   ->get();

        return view('admin.inventory-items.show', compact('inventoryItem', 'relatedItems'));
    }

    /**
     * Show the form for editing the specified inventory item
     */
    public function edit(InventoryItem $inventoryItem)
    {
        $inventoryItem->load(['product', 'location']);

        $products = Product::active()->with(['category', 'metal'])->orderBy('name')->get();
        $locations = Location::active()->orderBy('name')->get();
        $customers = Customer::active()->orderBy('name')->get();

        return view('admin.inventory-items.edit', compact('inventoryItem', 'products', 'locations', 'customers'));
    }

    /**
     * Update the specified inventory item
     */
    public function update(Request $request, InventoryItem $inventoryItem)
    {
        $validated = $request->validate([
            'location_id' => 'required|exists:locations,id',
            'actual_weight' => 'nullable|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'net_metal_weight' => 'nullable|numeric|min:0',
            'purity' => 'nullable|string|max:20',
            'dimensions' => 'nullable|array',
            'cost_price' => 'required|numeric|min:0',
            'making_charges' => 'nullable|numeric|min:0',
            'stone_charges' => 'nullable|numeric|min:0',
            'other_charges' => 'nullable|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'mrp' => 'nullable|numeric|min:0',
            'status' => 'required|in:available,reserved,sold,damaged,repair,display',
            'condition' => 'required|in:new,excellent,good,fair,poor',
            'condition_notes' => 'nullable|string|max:500',
            'shelf_location' => 'nullable|string|max:100',
            'display_location' => 'nullable|string|max:100',
            'is_on_display' => 'boolean',
            'insurance_value' => 'nullable|numeric|min:0',
            'insurance_policy_number' => 'nullable|string|max:100',
            'warranty_start_date' => 'nullable|date',
            'warranty_end_date' => 'nullable|date|after_or_equal:warranty_start_date',
            'warranty_terms' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $oldLocationId = $inventoryItem->location_id;

            $inventoryItem->update($validated);

            // Update aggregate inventory for old and new locations if location changed
            if ($oldLocationId !== $validated['location_id']) {
                $this->inventoryService->updateAggregateInventory($inventoryItem->product_id, $oldLocationId);
                $this->inventoryService->updateAggregateInventory($inventoryItem->product_id, $validated['location_id']);
            } else {
                $this->inventoryService->updateAggregateInventory($inventoryItem->product_id, $inventoryItem->location_id);
            }

            return redirect()->route('admin.inventory-items.show', $inventoryItem)
                ->with('success', 'Inventory item updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Error updating inventory item: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified inventory item
     */
    public function destroy(InventoryItem $inventoryItem)
    {
        try {
            // Delete associated photos
            if ($inventoryItem->photos) {
                foreach ($inventoryItem->photos as $photo) {
                    Storage::disk('public')->delete($photo['path']);
                }
            }

            $productId = $inventoryItem->product_id;
            $locationId = $inventoryItem->location_id;

            $inventoryItem->delete();

            // Update aggregate inventory
            $this->inventoryService->updateAggregateInventory($productId, $locationId);

            return redirect()->route('admin.inventory-items.index')
                ->with('success', 'Inventory item deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Error deleting inventory item: ' . $e->getMessage());
        }
    }

    /**
     * Upload photos for inventory item
     */
    public function uploadPhotos(Request $request, InventoryItem $inventoryItem)
    {
        $request->validate([
            'photos.*' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'descriptions.*' => 'nullable|string|max:255',
        ]);

        try {
            $uploadedPhotos = [];

            foreach ($request->file('photos') as $index => $photo) {
                $path = $photo->store('inventory-items', 'public');
                $description = $request->input("descriptions.{$index}");

                $inventoryItem->addPhoto($path, $description);

                $uploadedPhotos[] = [
                    'path' => $path,
                    'description' => $description,
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Photos uploaded successfully.',
                'photos' => $uploadedPhotos,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error uploading photos: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Delete photo from inventory item
     */
    public function deletePhoto(Request $request, InventoryItem $inventoryItem)
    {
        $photoIndex = $request->photo_index;

        try {
            $inventoryItem->removePhoto($photoIndex);

            return response()->json([
                'success' => true,
                'message' => 'Photo deleted successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting photo: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate barcode for item
     */
    public function generateBarcode(InventoryItem $inventoryItem)
    {
        try {
            if ($inventoryItem->barcode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Barcode already exists for this item.',
                    'barcode' => $inventoryItem->barcode,
                ]);
            }

            $barcode = $this->barcodeService->generateItemBarcode($inventoryItem);
            $inventoryItem->update(['barcode' => $barcode]);

            return response()->json([
                'success' => true,
                'message' => 'Barcode generated successfully.',
                'barcode' => $barcode,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating barcode: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate QR code for item
     */
    public function generateQRCode(InventoryItem $inventoryItem)
    {
        try {
            $qrCode = $this->barcodeService->generateItemQRCode($inventoryItem);
            $inventoryItem->update(['qr_code' => $qrCode]);

            return response()->json([
                'success' => true,
                'message' => 'QR code generated successfully.',
                'qr_code' => $qrCode,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating QR code: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Mark item as sold
     */
    public function markAsSold(Request $request, InventoryItem $inventoryItem)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'invoice_id' => 'nullable|exists:invoices,id',
            'sale_date' => 'nullable|date',
            'sale_notes' => 'nullable|string|max:500',
        ]);

        try {
            $inventoryItem->markAsSold(
                $validated['customer_id'],
                $validated['invoice_id'],
                $validated['sale_date']
            );

            if ($validated['sale_notes']) {
                $inventoryItem->update(['notes' => $inventoryItem->notes . "\n\nSale Notes: " . $validated['sale_notes']]);
            }

            // Update aggregate inventory
            $this->inventoryService->updateAggregateInventory($inventoryItem->product_id, $inventoryItem->location_id);

            return response()->json([
                'success' => true,
                'message' => 'Item marked as sold successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error marking item as sold: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Reserve item
     */
    public function reserve(Request $request, InventoryItem $inventoryItem)
    {
        $validated = $request->validate([
            'reservation_notes' => 'nullable|string|max:500',
        ]);

        try {
            $inventoryItem->reserve($validated['reservation_notes']);

            // Update aggregate inventory
            $this->inventoryService->updateAggregateInventory($inventoryItem->product_id, $inventoryItem->location_id);

            return response()->json([
                'success' => true,
                'message' => 'Item reserved successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error reserving item: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Make item available
     */
    public function makeAvailable(InventoryItem $inventoryItem)
    {
        try {
            $inventoryItem->makeAvailable();

            // Update aggregate inventory
            $this->inventoryService->updateAggregateInventory($inventoryItem->product_id, $inventoryItem->location_id);

            return response()->json([
                'success' => true,
                'message' => 'Item made available successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error making item available: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Verify item
     */
    public function verify(Request $request, InventoryItem $inventoryItem)
    {
        $validated = $request->validate([
            'verification_notes' => 'nullable|string|max:500',
            'actual_weight' => 'nullable|numeric|min:0',
            'condition' => 'nullable|in:new,excellent,good,fair,poor',
            'shelf_location' => 'nullable|string|max:100',
        ]);

        try {
            // Update physical properties if provided
            $updates = [];
            if (isset($validated['actual_weight'])) {
                $updates['actual_weight'] = $validated['actual_weight'];
            }
            if (isset($validated['condition'])) {
                $updates['condition'] = $validated['condition'];
            }
            if (isset($validated['shelf_location'])) {
                $updates['shelf_location'] = $validated['shelf_location'];
            }

            if (!empty($updates)) {
                $inventoryItem->update($updates);
            }

            $inventoryItem->verify($validated['verification_notes']);

            return response()->json([
                'success' => true,
                'message' => 'Item verified successfully.',
                'verified_at' => $inventoryItem->last_verified_at->format('Y-m-d H:i:s'),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error verifying item: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Transfer item to different location
     */
    public function transfer(Request $request, InventoryItem $inventoryItem)
    {
        $validated = $request->validate([
            'new_location_id' => 'required|exists:locations,id|different:location_id',
            'new_shelf_location' => 'nullable|string|max:100',
            'new_display_location' => 'nullable|string|max:100',
            'transfer_notes' => 'nullable|string|max:500',
        ]);

        try {
            $oldLocationId = $inventoryItem->location_id;

            $inventoryItem->updateLocation(
                $validated['new_location_id'],
                $validated['new_shelf_location'],
                $validated['new_display_location']
            );

            if ($validated['transfer_notes']) {
                $inventoryItem->update(['notes' => $inventoryItem->notes . "\n\nTransfer Notes: " . $validated['transfer_notes']]);
            }

            // Update aggregate inventory for both locations
            $this->inventoryService->updateAggregateInventory($inventoryItem->product_id, $oldLocationId);
            $this->inventoryService->updateAggregateInventory($inventoryItem->product_id, $validated['new_location_id']);

            return response()->json([
                'success' => true,
                'message' => 'Item transferred successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error transferring item: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Search items by barcode or tag
     */
    public function searchByCode(Request $request)
    {
        $code = $request->code;

        if (!$code) {
            return response()->json([
                'success' => false,
                'message' => 'Search code is required.',
            ]);
        }

        try {
            $item = $this->barcodeService->searchByCode($code);

            if ($item) {
                $item->load(['product.category', 'product.metal', 'location', 'soldToCustomer']);

                return response()->json([
                    'success' => true,
                    'item' => $item,
                    'url' => route('admin.inventory-items.show', $item),
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'No item found with the provided code.',
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching for item: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Bulk operations
     */
    public function bulkOperations(Request $request)
    {
        $validated = $request->validate([
            'operation' => 'required|in:update_status,transfer,generate_barcodes,generate_qr_codes,verify',
            'item_ids' => 'required|array|min:1',
            'item_ids.*' => 'exists:inventory_items,id',
            'status' => 'required_if:operation,update_status|in:available,reserved,sold,damaged,repair,display',
            'location_id' => 'required_if:operation,transfer|exists:locations,id',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $results = [];

            switch ($validated['operation']) {
                case 'update_status':
                    $results = $this->inventoryService->bulkUpdateStatus(
                        $validated['item_ids'],
                        $validated['status'],
                        $validated['notes']
                    );
                    break;

                case 'transfer':
                    $results = $this->inventoryService->bulkTransferItems(
                        $validated['item_ids'],
                        $validated['location_id'],
                        $validated['notes']
                    );
                    break;

                case 'generate_barcodes':
                    $results = $this->barcodeService->bulkGenerateBarcodes($validated['item_ids']);
                    break;

                case 'generate_qr_codes':
                    $results = $this->barcodeService->bulkGenerateQRCodes($validated['item_ids']);
                    break;

                case 'verify':
                    // For bulk verification, we'll just update the verification timestamp
                    $items = InventoryItem::whereIn('id', $validated['item_ids'])->get();
                    foreach ($items as $item) {
                        $item->verify($validated['notes']);
                        $results[] = [
                            'item_id' => $item->id,
                            'tag' => $item->unique_tag,
                            'success' => true,
                        ];
                    }
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => 'Bulk operation completed successfully.',
                'results' => $results,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error performing bulk operation: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get inventory analytics
     */
    public function getAnalytics(Request $request)
    {
        $locationId = $request->location_id;
        $days = $request->days ?? 30;

        try {
            $analytics = $this->inventoryService->getInventoryAnalytics($locationId, $days);

            return response()->json([
                'success' => true,
                'analytics' => $analytics,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting analytics: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Export inventory items
     */
    public function export(Request $request)
    {
        $format = $request->format ?? 'csv';
        $locationId = $request->location_id;
        $status = $request->status;

        try {
            $query = InventoryItem::with(['product.category', 'product.metal', 'location', 'soldToCustomer']);

            if ($locationId) {
                $query->where('location_id', $locationId);
            }

            if ($status) {
                $query->where('status', $status);
            }

            $items = $query->get();

            return $this->exportToCSV($items);

        } catch (\Exception $e) {
            return back()->with('error', 'Error exporting inventory: ' . $e->getMessage());
        }
    }

    /**
     * Export to CSV
     */
    private function exportToCSV($items)
    {
        $filename = 'inventory-items-' . now()->format('Y-m-d-H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($items) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Unique Tag', 'Barcode', 'HUID', 'Product Name', 'Category', 'Metal',
                'Weight (g)', 'Purity', 'Cost Price', 'Making Charges', 'Stone Charges',
                'Total Cost', 'Selling Price', 'MRP', 'Status', 'Condition',
                'Location', 'Shelf Location', 'Created Date', 'Sale Date'
            ]);

            foreach ($items as $item) {
                fputcsv($file, [
                    $item->unique_tag,
                    $item->barcode,
                    $item->huid_number,
                    $item->product->name,
                    $item->product->category->name,
                    $item->product->metal->name,
                    $item->actual_weight,
                    $item->purity,
                    $item->cost_price,
                    $item->making_charges,
                    $item->stone_charges,
                    $item->total_cost,
                    $item->selling_price,
                    $item->mrp,
                    $item->status_display,
                    $item->condition_display,
                    $item->location->name,
                    $item->shelf_location,
                    $item->created_at->format('Y-m-d'),
                    $item->sale_date ? $item->sale_date->format('Y-m-d') : '',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
