<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PurchaseOrder;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PurchaseOrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_purchase_orders')->only(['index', 'show']);
        $this->middleware('permission:create_purchase_orders')->only(['create', 'store']);
        $this->middleware('permission:edit_purchase_orders')->only(['edit', 'update']);
        $this->middleware('permission:delete_purchase_orders')->only(['destroy']);
    }

    /**
     * Display a listing of purchase orders
     */
    public function index(Request $request)
    {
        $query = PurchaseOrder::with(['supplier', 'location', 'createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('po_number', 'like', "%{$search}%")
                  ->orWhereHas('supplier', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by supplier
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Filter by location
        if ($request->filled('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('po_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('po_date', '<=', $request->date_to);
        }

        // Filter overdue orders
        if ($request->filled('overdue') && $request->overdue) {
            $query->overdue();
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $purchaseOrders = $query->paginate(20);

        // Get filter options
        $suppliers = Supplier::active()->orderBy('name')->get(['id', 'name']);
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $statuses = [
            'draft' => 'Draft',
            'sent' => 'Sent',
            'confirmed' => 'Confirmed',
            'partially_received' => 'Partially Received',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
        ];

        return view('admin.purchase-orders.index', compact(
            'purchaseOrders',
            'suppliers',
            'locations',
            'statuses'
        ));
    }

    /**
     * Show the form for creating a new purchase order
     */
    public function create()
    {
        $suppliers = Supplier::active()->orderBy('name')->get(['id', 'name', 'company_name']);
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $products = Product::active()->with(['category', 'metal'])->orderBy('name')->get();

        return view('admin.purchase-orders.create', compact('suppliers', 'locations', 'products'));
    }

    /**
     * Store a newly created purchase order
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'location_id' => 'required|exists:locations,id',
            'po_date' => 'required|date',
            'expected_delivery_date' => 'nullable|date|after:po_date',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity_ordered' => 'required|numeric|min:0.001',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Check supplier credit limit
            $supplier = Supplier::findOrFail($validated['supplier_id']);
            $totalAmount = collect($validated['items'])->sum(function ($item) {
                return $item['quantity_ordered'] * $item['unit_price'];
            });

            if (!$supplier->canPlaceOrder($totalAmount)) {
                throw new \Exception('Order amount exceeds supplier credit limit');
            }

            // Create purchase order
            $purchaseOrder = PurchaseOrder::create([
                'supplier_id' => $validated['supplier_id'],
                'location_id' => $validated['location_id'],
                'po_date' => $validated['po_date'],
                'expected_delivery_date' => $validated['expected_delivery_date'],
                'notes' => $validated['notes'],
                'status' => 'draft',
            ]);

            // Add items
            foreach ($validated['items'] as $itemData) {
                $purchaseOrder->addItem(
                    $itemData['product_id'],
                    $itemData['quantity_ordered'],
                    $itemData['unit_price'],
                    $itemData['notes'] ?? null
                );
            }

            DB::commit();

            return redirect()
                ->route('admin.purchase-orders.show', $purchaseOrder)
                ->with('success', 'Purchase order created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Failed to create purchase order: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified purchase order
     */
    public function show(PurchaseOrder $purchaseOrder)
    {
        $purchaseOrder->load([
            'supplier',
            'location',
            'createdBy',
            'items.product.category',
            'items.product.metal',
            'goodsReceiptNotes.items',
            'supplierPayments'
        ]);

        return view('admin.purchase-orders.show', compact('purchaseOrder'));
    }

    /**
     * Show the form for editing the specified purchase order
     */
    public function edit(PurchaseOrder $purchaseOrder)
    {
        if (!in_array($purchaseOrder->status, ['draft', 'sent'])) {
            return redirect()
                ->route('admin.purchase-orders.show', $purchaseOrder)
                ->with('error', 'Only draft or sent purchase orders can be edited.');
        }

        $purchaseOrder->load(['items.product']);
        $suppliers = Supplier::active()->orderBy('name')->get(['id', 'name', 'company_name']);
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $products = Product::active()->with(['category', 'metal'])->orderBy('name')->get();

        return view('admin.purchase-orders.edit', compact(
            'purchaseOrder',
            'suppliers',
            'locations',
            'products'
        ));
    }

    /**
     * Update the specified purchase order
     */
    public function update(Request $request, PurchaseOrder $purchaseOrder)
    {
        if (!in_array($purchaseOrder->status, ['draft', 'sent'])) {
            return back()->with('error', 'Only draft or sent purchase orders can be updated.');
        }

        $validated = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'location_id' => 'required|exists:locations,id',
            'po_date' => 'required|date',
            'expected_delivery_date' => 'nullable|date|after:po_date',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.id' => 'nullable|exists:purchase_order_items,id',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity_ordered' => 'required|numeric|min:0.001',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Update purchase order
            $purchaseOrder->update([
                'supplier_id' => $validated['supplier_id'],
                'location_id' => $validated['location_id'],
                'po_date' => $validated['po_date'],
                'expected_delivery_date' => $validated['expected_delivery_date'],
                'notes' => $validated['notes'],
            ]);

            // Update items
            $existingItemIds = [];
            foreach ($validated['items'] as $itemData) {
                if (isset($itemData['id']) && $itemData['id']) {
                    // Update existing item
                    $purchaseOrder->updateItem(
                        $itemData['id'],
                        $itemData['quantity_ordered'],
                        $itemData['unit_price'],
                        $itemData['notes'] ?? null
                    );
                    $existingItemIds[] = $itemData['id'];
                } else {
                    // Add new item
                    $newItem = $purchaseOrder->addItem(
                        $itemData['product_id'],
                        $itemData['quantity_ordered'],
                        $itemData['unit_price'],
                        $itemData['notes'] ?? null
                    );
                    $existingItemIds[] = $newItem->id;
                }
            }

            // Remove items not in the update
            $purchaseOrder->items()
                         ->whereNotIn('id', $existingItemIds)
                         ->delete();

            $purchaseOrder->calculateTotals();

            DB::commit();

            return redirect()
                ->route('admin.purchase-orders.show', $purchaseOrder)
                ->with('success', 'Purchase order updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Failed to update purchase order: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified purchase order
     */
    public function destroy(PurchaseOrder $purchaseOrder)
    {
        if (!$purchaseOrder->can_cancel) {
            return back()->with('error', 'This purchase order cannot be deleted.');
        }

        try {
            $purchaseOrder->delete();

            return redirect()
                ->route('admin.purchase-orders.index')
                ->with('success', 'Purchase order deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete purchase order: ' . $e->getMessage());
        }
    }

    /**
     * Send purchase order to supplier
     */
    public function sendToSupplier(PurchaseOrder $purchaseOrder)
    {
        try {
            $purchaseOrder->sendToSupplier();

            return back()->with('success', 'Purchase order sent to supplier successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to send purchase order: ' . $e->getMessage());
        }
    }

    /**
     * Confirm purchase order
     */
    public function confirm(PurchaseOrder $purchaseOrder)
    {
        try {
            $purchaseOrder->confirm();

            return back()->with('success', 'Purchase order confirmed successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to confirm purchase order: ' . $e->getMessage());
        }
    }

    /**
     * Cancel purchase order
     */
    public function cancel(Request $request, PurchaseOrder $purchaseOrder)
    {
        $validated = $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $purchaseOrder->cancel($validated['reason'] ?? null);

            return back()->with('success', 'Purchase order cancelled successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to cancel purchase order: ' . $e->getMessage());
        }
    }

    /**
     * Print purchase order
     */
    public function print(PurchaseOrder $purchaseOrder)
    {
        $purchaseOrder->load([
            'supplier',
            'location',
            'createdBy',
            'items.product.category',
            'items.product.metal'
        ]);

        return view('admin.purchase-orders.print', compact('purchaseOrder'));
    }

    /**
     * Export purchase orders to CSV
     */
    public function export(Request $request)
    {
        $query = PurchaseOrder::with(['supplier', 'location']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('po_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('po_date', '<=', $request->date_to);
        }

        $purchaseOrders = $query->get();

        $filename = 'purchase_orders_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($purchaseOrders) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'PO Number', 'Supplier', 'Location', 'PO Date', 'Expected Delivery',
                'Status', 'Total Amount', 'Completion %', 'Created At'
            ]);

            // CSV data
            foreach ($purchaseOrders as $po) {
                fputcsv($file, [
                    $po->po_number,
                    $po->supplier->name,
                    $po->location->name,
                    $po->po_date->format('Y-m-d'),
                    $po->expected_delivery_date?->format('Y-m-d'),
                    $po->status_display,
                    $po->total_amount,
                    $po->completion_percentage . '%',
                    $po->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get purchase order statistics
     */
    public function getStatistics(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = now()->subDays($days);

        $stats = [
            'total_orders' => PurchaseOrder::where('created_at', '>=', $startDate)->count(),
            'total_value' => PurchaseOrder::where('created_at', '>=', $startDate)->sum('total_amount'),
            'pending_orders' => PurchaseOrder::whereIn('status', ['draft', 'sent', 'confirmed'])->count(),
            'overdue_orders' => PurchaseOrder::overdue()->count(),
            'completed_orders' => PurchaseOrder::where('status', 'completed')
                                              ->where('created_at', '>=', $startDate)
                                              ->count(),
            'orders_by_status' => PurchaseOrder::where('created_at', '>=', $startDate)
                                              ->groupBy('status')
                                              ->selectRaw('status, count(*) as count')
                                              ->pluck('count', 'status'),
            'top_suppliers' => PurchaseOrder::with('supplier')
                                           ->where('created_at', '>=', $startDate)
                                           ->groupBy('supplier_id')
                                           ->selectRaw('supplier_id, count(*) as order_count, sum(total_amount) as total_value')
                                           ->orderBy('total_value', 'desc')
                                           ->limit(5)
                                           ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}
