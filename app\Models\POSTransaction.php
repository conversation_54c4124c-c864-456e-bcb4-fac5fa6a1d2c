<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class POSTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pos_transactions';

    protected $fillable = [
        'transaction_number',
        'pos_session_id',
        'customer_id',
        'invoice_id',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'payment_method',
        'payment_reference',
        'cash_received',
        'change_given',
        'status',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'cash_received' => 'decimal:2',
        'change_given' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (!$transaction->transaction_number) {
                $transaction->transaction_number = static::generateTransactionNumber();
            }
            $transaction->created_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function posSession(): BelongsTo
    {
        return $this->belongsTo(POSSession::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function items(): HasMany
    {
        return $this->hasMany(POSTransactionItem::class);
    }

    /**
     * Scopes
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'pending' => 'Pending',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'refunded' => 'Refunded',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'yellow',
            'completed' => 'green',
            'cancelled' => 'red',
            'refunded' => 'orange',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getPaymentMethodDisplayAttribute()
    {
        $methods = [
            'cash' => 'Cash',
            'card' => 'Card',
            'upi' => 'UPI',
            'bank_transfer' => 'Bank Transfer',
            'cheque' => 'Cheque',
            'credit' => 'Credit',
            'mixed' => 'Mixed Payment',
        ];

        return $methods[$this->payment_method] ?? ucfirst(str_replace('_', ' ', $this->payment_method));
    }

    public function getIsCompletedAttribute()
    {
        return $this->status === 'completed';
    }

    public function getCanCancelAttribute()
    {
        return in_array($this->status, ['pending']);
    }

    public function getCanRefundAttribute()
    {
        return $this->status === 'completed';
    }

    /**
     * Business Logic Methods
     */
    public static function generateTransactionNumber()
    {
        $prefix = 'TXN';
        $date = now()->format('ymd');

        $lastTransaction = static::whereDate('created_at', today())
                                ->orderBy('id', 'desc')
                                ->first();

        $sequence = $lastTransaction ? (int) substr($lastTransaction->transaction_number, -4) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function addItem($productId, $quantity, $unitPrice, $discount = 0)
    {
        $product = Product::findOrFail($productId);

        $totalPrice = ($quantity * $unitPrice) - $discount;

        return $this->items()->create([
            'product_id' => $productId,
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'discount_amount' => $discount,
            'total_price' => $totalPrice,
        ]);
    }

    public function calculateTotals()
    {
        $this->subtotal = $this->items->sum('total_price');
        $this->tax_amount = $this->subtotal * 0.03; // 3% GST for jewelry
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->save();

        return $this;
    }

    public function processPayment($paymentMethod, $cashReceived = null, $paymentReference = null)
    {
        $updateData = [
            'payment_method' => $paymentMethod,
            'payment_reference' => $paymentReference,
            'status' => 'completed',
        ];

        if ($paymentMethod === 'cash' && $cashReceived) {
            $updateData['cash_received'] = $cashReceived;
            $updateData['change_given'] = max(0, $cashReceived - $this->total_amount);
        }

        $this->update($updateData);

        // Update inventory
        $this->updateInventory();

        // Update POS session totals
        $this->posSession->updateSessionTotals();

        return $this;
    }

    public function updateInventory()
    {
        foreach ($this->items as $item) {
            // Find available inventory items
            $inventoryItems = InventoryItem::where('product_id', $item->product_id)
                                         ->where('location_id', $this->posSession->location_id)
                                         ->where('status', 'available')
                                         ->limit($item->quantity)
                                         ->get();

            if ($inventoryItems->count() < $item->quantity) {
                throw new \Exception("Insufficient inventory for product: {$item->product->name}");
            }

            // Mark inventory items as sold
            foreach ($inventoryItems as $inventoryItem) {
                $inventoryItem->update([
                    'status' => 'sold',
                    'sold_date' => now(),
                    'selling_price' => $item->unit_price,
                ]);
            }
        }
    }

    public function cancel($reason = null)
    {
        if (!$this->can_cancel) {
            throw new \Exception('This transaction cannot be cancelled');
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? ($this->notes . "\n\nCancelled: " . $reason) : $this->notes,
        ]);

        // Update POS session totals
        $this->posSession->updateSessionTotals();

        return $this;
    }

    public function refund($amount = null, $reason = null)
    {
        if (!$this->can_refund) {
            throw new \Exception('This transaction cannot be refunded');
        }

        $refundAmount = $amount ?: $this->total_amount;

        $this->update([
            'status' => 'refunded',
            'notes' => $reason ? ($this->notes . "\n\nRefunded: ₹{$refundAmount} - " . $reason) : $this->notes,
        ]);

        // Reverse inventory changes
        $this->reverseInventory();

        // Update POS session totals
        $this->posSession->updateSessionTotals();

        return $this;
    }

    protected function reverseInventory()
    {
        foreach ($this->items as $item) {
            // Find sold inventory items for this transaction
            $inventoryItems = InventoryItem::where('product_id', $item->product_id)
                                         ->where('status', 'sold')
                                         ->where('sold_date', $this->created_at->toDateString())
                                         ->limit($item->quantity)
                                         ->get();

            // Mark inventory items as available again
            foreach ($inventoryItems as $inventoryItem) {
                $inventoryItem->update([
                    'status' => 'available',
                    'sold_date' => null,
                    'selling_price' => null,
                ]);
            }
        }
    }

    public function generateReceipt()
    {
        return [
            'transaction_number' => $this->transaction_number,
            'date' => $this->created_at->format('Y-m-d H:i:s'),
            'customer' => $this->customer?->name ?: 'Walk-in Customer',
            'cashier' => $this->createdBy->name,
            'items' => $this->items->map(function ($item) {
                return [
                    'name' => $item->product->name,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_price' => $item->total_price,
                ];
            }),
            'subtotal' => $this->subtotal,
            'tax_amount' => $this->tax_amount,
            'discount_amount' => $this->discount_amount,
            'total_amount' => $this->total_amount,
            'payment_method' => $this->payment_method_display,
            'cash_received' => $this->cash_received,
            'change_given' => $this->change_given,
        ];
    }

    public function getSummary()
    {
        return [
            'transaction_number' => $this->transaction_number,
            'customer_name' => $this->customer?->name ?: 'Walk-in Customer',
            'subtotal' => $this->subtotal,
            'tax_amount' => $this->tax_amount,
            'discount_amount' => $this->discount_amount,
            'total_amount' => $this->total_amount,
            'payment_method' => $this->payment_method_display,
            'status' => $this->status_display,
            'items_count' => $this->items->count(),
            'cashier' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
