<?php

namespace App\Http\View\Composers;

use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class AdminLayoutComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        $user = Auth::user();
        
        if (!$user) {
            return;
        }

        // Check permissions for navigation
        $permissions = [
            'hasProductPermission' => $user->can('view_products') || $user->can('create_products') || $user->can('edit_products'),
            'hasCustomerPermission' => $user->can('view_customers') || $user->can('create_customers') || $user->can('edit_customers'),
            'hasInventoryPermission' => $user->can('view_inventory') || $user->can('manage_inventory'),
            'hasSalesPermission' => $user->can('create_invoices') || $user->can('edit_invoices') || $user->can('view_invoices'),
            'hasEstimatePermission' => $user->can('create_estimates') || $user->can('edit_estimates') || $user->can('view_estimates'),
            'hasReportsPermission' => $user->can('view_reports') || $user->can('export_reports'),
            'hasCategoryPermission' => $user->can('manage_categories'),
            'hasMetalPermission' => $user->can('view_metals') || $user->can('edit_metals'),
            'hasStonePermission' => $user->can('view_stones') || $user->can('edit_stones'),
            'hasHSNPermission' => $user->can('view_hsn') || $user->can('manage_hsn'),
            'hasMakingChargePermission' => $user->can('manage_making_charges'),
            'hasMetalRatePermission' => $user->can('view_metal_rates') || $user->can('edit_metal_rates'),
            'hasUserPermission' => $user->can('manage_users'),
            'hasSettingsPermission' => $user->can('system_settings'),
            'hasCRMPermission' => $user->can('view_crm'),
            'hasFinancialPermission' => $user->can('view_financial_reports'),
            'hasPOSPermission' => $user->can('access_pos'),
        ];

        $view->with($permissions);
    }
}
