<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ChartOfAccount;
use App\Models\JournalEntry;
use App\Models\JournalEntryLine;
use App\Models\Invoice;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class FinancialReportsController extends Controller
{
    /**
     * Financial reports dashboard
     */
    public function index()
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();
        
        $stats = [
            'total_revenue' => $this->getTotalRevenue($currentMonth, now()),
            'total_expenses' => $this->getTotalExpenses($currentMonth, now()),
            'net_profit' => 0, // Will be calculated
            'cash_balance' => $this->getCashBalance(),
            'accounts_receivable' => $this->getAccountsReceivable(),
            'accounts_payable' => $this->getAccountsPayable(),
        ];
        
        $stats['net_profit'] = $stats['total_revenue'] - $stats['total_expenses'];
        
        // Monthly comparison
        $lastMonthStats = [
            'revenue' => $this->getTotalRevenue($lastMonth, $lastMonth->copy()->endOfMonth()),
            'expenses' => $this->getTotalExpenses($lastMonth, $lastMonth->copy()->endOfMonth()),
        ];
        
        $recentTransactions = JournalEntry::with(['entries.account', 'createdBy'])
                                        ->where('status', 'posted')
                                        ->orderByDesc('transaction_date')
                                        ->limit(10)
                                        ->get();

        return view('admin.financial.reports.index', compact(
            'stats', 'lastMonthStats', 'recentTransactions'
        ));
    }

    /**
     * Profit & Loss Statement
     */
    public function profitLoss(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfYear()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));
        $format = $request->get('format', 'view'); // view, pdf, excel

        // Revenue Accounts
        $revenueAccounts = ChartOfAccount::where('account_type', 'revenue')
                                       ->where('is_active', true)
                                       ->get();
        
        $revenues = [];
        $totalRevenue = 0;
        
        foreach ($revenueAccounts as $account) {
            $balance = $this->getAccountBalance($account->id, $startDate, $endDate);
            if ($balance != 0) {
                $revenues[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalRevenue += $balance;
            }
        }

        // Expense Accounts
        $expenseAccounts = ChartOfAccount::where('account_type', 'expense')
                                       ->where('is_active', true)
                                       ->get();
        
        $expenses = [];
        $totalExpenses = 0;
        
        foreach ($expenseAccounts as $account) {
            $balance = $this->getAccountBalance($account->id, $startDate, $endDate);
            if ($balance != 0) {
                $expenses[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalExpenses += $balance;
            }
        }

        $netIncome = $totalRevenue - $totalExpenses;

        $data = compact('revenues', 'expenses', 'totalRevenue', 'totalExpenses', 'netIncome', 'startDate', 'endDate');

        if ($format === 'pdf') {
            $pdf = PDF::loadView('admin.financial.reports.profit-loss-pdf', $data);
            return $pdf->download('profit-loss-' . $startDate . '-to-' . $endDate . '.pdf');
        }

        return view('admin.financial.reports.profit-loss', $data);
    }

    /**
     * Balance Sheet
     */
    public function balanceSheet(Request $request)
    {
        $asOfDate = $request->get('as_of_date', now()->format('Y-m-d'));
        $format = $request->get('format', 'view');

        // Assets
        $assetAccounts = ChartOfAccount::where('account_type', 'asset')
                                     ->where('is_active', true)
                                     ->orderBy('account_code')
                                     ->get();
        
        $assets = [];
        $totalAssets = 0;
        
        foreach ($assetAccounts as $account) {
            $balance = $this->getAccountBalanceAsOf($account->id, $asOfDate);
            if ($balance != 0) {
                $assets[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalAssets += $balance;
            }
        }

        // Liabilities
        $liabilityAccounts = ChartOfAccount::where('account_type', 'liability')
                                         ->where('is_active', true)
                                         ->orderBy('account_code')
                                         ->get();
        
        $liabilities = [];
        $totalLiabilities = 0;
        
        foreach ($liabilityAccounts as $account) {
            $balance = $this->getAccountBalanceAsOf($account->id, $asOfDate);
            if ($balance != 0) {
                $liabilities[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalLiabilities += $balance;
            }
        }

        // Equity
        $equityAccounts = ChartOfAccount::where('account_type', 'equity')
                                      ->where('is_active', true)
                                      ->orderBy('account_code')
                                      ->get();
        
        $equity = [];
        $totalEquity = 0;
        
        foreach ($equityAccounts as $account) {
            $balance = $this->getAccountBalanceAsOf($account->id, $asOfDate);
            if ($balance != 0) {
                $equity[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalEquity += $balance;
            }
        }

        $totalLiabilitiesAndEquity = $totalLiabilities + $totalEquity;

        $data = compact(
            'assets', 'liabilities', 'equity', 
            'totalAssets', 'totalLiabilities', 'totalEquity', 'totalLiabilitiesAndEquity',
            'asOfDate'
        );

        if ($format === 'pdf') {
            $pdf = PDF::loadView('admin.financial.reports.balance-sheet-pdf', $data);
            return $pdf->download('balance-sheet-' . $asOfDate . '.pdf');
        }

        return view('admin.financial.reports.balance-sheet', $data);
    }

    /**
     * Cash Flow Statement
     */
    public function cashFlow(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        // Operating Activities
        $operatingCashFlow = $this->getOperatingCashFlow($startDate, $endDate);
        
        // Investing Activities
        $investingCashFlow = $this->getInvestingCashFlow($startDate, $endDate);
        
        // Financing Activities
        $financingCashFlow = $this->getFinancingCashFlow($startDate, $endDate);

        $netCashFlow = $operatingCashFlow['total'] + $investingCashFlow['total'] + $financingCashFlow['total'];
        
        $beginningCash = $this->getCashBalanceAsOf(Carbon::parse($startDate)->subDay()->format('Y-m-d'));
        $endingCash = $beginningCash + $netCashFlow;

        return view('admin.financial.reports.cash-flow', compact(
            'operatingCashFlow', 'investingCashFlow', 'financingCashFlow',
            'netCashFlow', 'beginningCash', 'endingCash', 'startDate', 'endDate'
        ));
    }

    /**
     * Trial Balance
     */
    public function trialBalance(Request $request)
    {
        $asOfDate = $request->get('as_of_date', now()->format('Y-m-d'));
        
        $accounts = ChartOfAccount::where('is_active', true)
                                ->orderBy('account_code')
                                ->get();
        
        $trialBalance = [];
        $totalDebits = 0;
        $totalCredits = 0;
        
        foreach ($accounts as $account) {
            $balance = $this->getAccountBalanceAsOf($account->id, $asOfDate);
            
            if ($balance != 0) {
                $debitBalance = 0;
                $creditBalance = 0;
                
                // Determine if balance is debit or credit based on account type
                if (in_array($account->account_type, ['asset', 'expense'])) {
                    $debitBalance = $balance > 0 ? $balance : 0;
                    $creditBalance = $balance < 0 ? abs($balance) : 0;
                } else {
                    $creditBalance = $balance > 0 ? $balance : 0;
                    $debitBalance = $balance < 0 ? abs($balance) : 0;
                }
                
                $trialBalance[] = [
                    'account' => $account,
                    'debit_balance' => $debitBalance,
                    'credit_balance' => $creditBalance,
                ];
                
                $totalDebits += $debitBalance;
                $totalCredits += $creditBalance;
            }
        }

        return view('admin.financial.reports.trial-balance', compact(
            'trialBalance', 'totalDebits', 'totalCredits', 'asOfDate'
        ));
    }

    /**
     * General Ledger
     */
    public function generalLedger(Request $request)
    {
        $accountId = $request->get('account_id');
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        $accounts = ChartOfAccount::where('is_active', true)
                                ->orderBy('account_code')
                                ->get();

        $ledgerData = null;
        
        if ($accountId) {
            $account = ChartOfAccount::findOrFail($accountId);
            
            $entries = JournalEntryLine::with(['journalEntry', 'account'])
                                     ->where('account_id', $accountId)
                                     ->whereHas('journalEntry', function ($q) use ($startDate, $endDate) {
                                         $q->where('status', 'posted')
                                           ->whereBetween('transaction_date', [$startDate, $endDate]);
                                     })
                                     ->orderBy('created_at')
                                     ->get();

            $runningBalance = $account->opening_balance;
            $ledgerEntries = [];
            
            foreach ($entries as $entry) {
                if (in_array($account->account_type, ['asset', 'expense'])) {
                    $runningBalance += $entry->debit_amount - $entry->credit_amount;
                } else {
                    $runningBalance += $entry->credit_amount - $entry->debit_amount;
                }
                
                $ledgerEntries[] = [
                    'entry' => $entry,
                    'running_balance' => $runningBalance,
                ];
            }
            
            $ledgerData = [
                'account' => $account,
                'entries' => $ledgerEntries,
                'opening_balance' => $account->opening_balance,
                'closing_balance' => $runningBalance,
            ];
        }

        return view('admin.financial.reports.general-ledger', compact(
            'accounts', 'ledgerData', 'accountId', 'startDate', 'endDate'
        ));
    }

    // Helper methods
    private function getTotalRevenue($startDate, $endDate)
    {
        return JournalEntryLine::whereHas('account', function ($q) {
                    $q->where('account_type', 'revenue');
                })
                ->whereHas('journalEntry', function ($q) use ($startDate, $endDate) {
                    $q->where('status', 'posted')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
                })
                ->sum('credit_amount');
    }

    private function getTotalExpenses($startDate, $endDate)
    {
        return JournalEntryLine::whereHas('account', function ($q) {
                    $q->where('account_type', 'expense');
                })
                ->whereHas('journalEntry', function ($q) use ($startDate, $endDate) {
                    $q->where('status', 'posted')
                      ->whereBetween('transaction_date', [$startDate, $endDate]);
                })
                ->sum('debit_amount');
    }

    private function getCashBalance()
    {
        return ChartOfAccount::whereIn('account_code', ['1001', '1002'])
                           ->sum('current_balance');
    }

    private function getAccountsReceivable()
    {
        return ChartOfAccount::where('account_code', '1003')
                           ->value('current_balance') ?? 0;
    }

    private function getAccountsPayable()
    {
        return ChartOfAccount::where('account_code', '2001')
                           ->value('current_balance') ?? 0;
    }

    private function getAccountBalance($accountId, $startDate, $endDate)
    {
        $account = ChartOfAccount::find($accountId);
        if (!$account) return 0;

        return $account->getBalanceForPeriod($startDate, $endDate);
    }

    private function getAccountBalanceAsOf($accountId, $asOfDate)
    {
        $account = ChartOfAccount::find($accountId);
        if (!$account) return 0;

        $debitTotal = JournalEntryLine::where('account_id', $accountId)
                                    ->whereHas('journalEntry', function ($q) use ($asOfDate) {
                                        $q->where('status', 'posted')
                                          ->where('transaction_date', '<=', $asOfDate);
                                    })
                                    ->sum('debit_amount');

        $creditTotal = JournalEntryLine::where('account_id', $accountId)
                                     ->whereHas('journalEntry', function ($q) use ($asOfDate) {
                                         $q->where('status', 'posted')
                                           ->where('transaction_date', '<=', $asOfDate);
                                     })
                                     ->sum('credit_amount');

        if (in_array($account->account_type, ['asset', 'expense'])) {
            return $account->opening_balance + $debitTotal - $creditTotal;
        }
        
        return $account->opening_balance + $creditTotal - $debitTotal;
    }

    private function getCashBalanceAsOf($asOfDate)
    {
        $cashAccounts = ChartOfAccount::whereIn('account_code', ['1001', '1002'])->get();
        $totalCash = 0;
        
        foreach ($cashAccounts as $account) {
            $totalCash += $this->getAccountBalanceAsOf($account->id, $asOfDate);
        }
        
        return $totalCash;
    }

    private function getOperatingCashFlow($startDate, $endDate)
    {
        // This is a simplified version - in practice, you'd need more detailed cash flow analysis
        return [
            'items' => [
                ['description' => 'Cash from Sales', 'amount' => $this->getTotalRevenue($startDate, $endDate)],
                ['description' => 'Cash for Expenses', 'amount' => -$this->getTotalExpenses($startDate, $endDate)],
            ],
            'total' => $this->getTotalRevenue($startDate, $endDate) - $this->getTotalExpenses($startDate, $endDate)
        ];
    }

    private function getInvestingCashFlow($startDate, $endDate)
    {
        return ['items' => [], 'total' => 0];
    }

    private function getFinancingCashFlow($startDate, $endDate)
    {
        return ['items' => [], 'total' => 0];
    }
}
