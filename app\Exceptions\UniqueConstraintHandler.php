<?php

namespace App\Exceptions;

use Illuminate\Database\QueryException;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UniqueConstraintHandler
{
    public static function handle($exception, $request = null)
    {
        $message = $exception->getMessage();
        $sql = method_exists($exception, 'getSql') ? $exception->getSql() : 'N/A';
        $bindings = method_exists($exception, 'getBindings') ? $exception->getBindings() : [];

        // Check if it's a unique constraint violation
        if (Str::contains($message, ['Duplicate entry', 'UNIQUE constraint failed', 'Integrity constraint violation'])) {
            
            // Extract table and column information
            $tableInfo = self::extractTableInfo($message, $sql);
            
            // Log detailed information
            Log::error('Unique Constraint Violation Detected', [
                'message' => $message,
                'sql' => $sql,
                'bindings' => $bindings,
                'table' => $tableInfo['table'] ?? 'unknown',
                'column' => $tableInfo['column'] ?? 'unknown',
                'duplicate_value' => $tableInfo['value'] ?? 'unknown',
                'stack_trace' => $exception->getTraceAsString(),
                'timestamp' => now()->toISOString(),
            ]);

            // Try to provide a user-friendly error message
            $userMessage = self::generateUserFriendlyMessage($tableInfo);
            
            // Check if this is an AJAX request or API request
            if ($request && ($request->expectsJson() || $request->ajax())) {
                return response()->json([
                    'error' => 'Duplicate data detected',
                    'message' => $userMessage,
                    'details' => [
                        'table' => $tableInfo['table'] ?? 'unknown',
                        'field' => $tableInfo['column'] ?? 'unknown',
                        'value' => $tableInfo['value'] ?? 'unknown',
                    ]
                ], 422);
            }
            
            // For regular web requests, redirect back with error
            if ($request) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['duplicate' => $userMessage])
                    ->with('error', $userMessage);
            }
            
            // Fallback JSON response
            return response()->json([
                'error' => 'Duplicate data detected',
                'message' => $userMessage,
                'details' => [
                    'table' => $tableInfo['table'] ?? 'unknown',
                    'field' => $tableInfo['column'] ?? 'unknown',
                    'value' => $tableInfo['value'] ?? 'unknown',
                ]
            ], 422);
        }

        // If not a unique constraint violation, re-throw
        throw $exception;
    }

    private static function extractTableInfo($message, $sql)
    {
        $info = [];

        // Extract table name from SQL
        if (preg_match('/insert into `?(\w+)`?/i', $sql, $matches)) {
            $info['table'] = $matches[1];
        } elseif (preg_match('/update `?(\w+)`?/i', $sql, $matches)) {
            $info['table'] = $matches[1];
        }

        // Extract duplicate value and key from error message (MySQL)
        if (preg_match("/Duplicate entry '([^']+)' for key '([^']+)'/", $message, $matches)) {
            $info['value'] = $matches[1];
            $info['key'] = $matches[2];
            
            // Extract column name from key - handle various patterns
            if (preg_match('/(\w+)\.(\w+)_(\w+)_unique/', $matches[2], $keyMatches)) {
                // Pattern: table.table_column_unique
                $info['column'] = $keyMatches[3];
            } elseif (preg_match('/(\w+)_unique/', $matches[2], $keyMatches)) {
                // Pattern: column_unique
                $info['column'] = $keyMatches[1];
            } elseif (preg_match('/(\w+)\.(\w+)/', $matches[2], $keyMatches)) {
                // Pattern: table.column
                $info['column'] = $keyMatches[2];
            } else {
                // Fallback: use the key name as column
                $info['column'] = $matches[2];
            }
        }
        
        // Handle SQLite UNIQUE constraint failed
        elseif (preg_match('/UNIQUE constraint failed: (\w+)\.(\w+)/', $message, $matches)) {
            $info['table'] = $matches[1];
            $info['column'] = $matches[2];
            $info['value'] = 'unknown';
        }
        
        // Handle PostgreSQL unique violation
        elseif (preg_match('/duplicate key value violates unique constraint "([^"]+)"/', $message, $matches)) {
            $info['key'] = $matches[1];
            if (preg_match('/(\w+)_(\w+)_unique/', $matches[1], $keyMatches)) {
                $info['table'] = $keyMatches[1];
                $info['column'] = $keyMatches[2];
            }
            $info['value'] = 'unknown';
        }

        return $info;
    }

    private static function generateUserFriendlyMessage($tableInfo)
    {
        $table = $tableInfo['table'] ?? 'record';
        $column = $tableInfo['column'] ?? 'field';
        $value = $tableInfo['value'] ?? 'value';

        $friendlyNames = [
            'users' => 'user',
            'customers' => 'customer',
            'products' => 'product',
            'saving_scheme_plans' => 'saving scheme plan',
            'saving_schemes' => 'saving scheme',
            'scheme_payments' => 'payment',
            'scheme_transactions' => 'transaction',
        ];

        $friendlyFields = [
            'email' => 'email address',
            'phone' => 'phone number',
            'employee_id' => 'employee ID',
            'customer_code' => 'customer code',
            'sku' => 'SKU',
            'barcode' => 'barcode',
            'plan_code' => 'plan code',
            'scheme_number' => 'scheme number',
            'payment_number' => 'payment number',
            'receipt_number' => 'receipt number',
            'transaction_number' => 'transaction number',
        ];

        $tableName = $friendlyNames[$table] ?? $table;
        $fieldName = $friendlyFields[$column] ?? $column;

        return "A {$tableName} with this {$fieldName} ('{$value}') already exists. Please use a different {$fieldName}.";
    }
}
