<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_interactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Staff member
            
            // Interaction details
            $table->enum('interaction_type', [
                'visit', 'phone_call', 'email', 'sms', 'whatsapp', 
                'inquiry', 'complaint', 'compliment', 'follow_up',
                'appointment', 'purchase', 'return', 'exchange',
                'consultation', 'custom_design', 'repair_service'
            ]);
            $table->enum('interaction_channel', ['in_store', 'phone', 'email', 'sms', 'whatsapp', 'website', 'social_media']);
            $table->enum('interaction_direction', ['inbound', 'outbound']);
            
            // Content and context
            $table->string('subject', 255)->nullable();
            $table->text('description');
            $table->json('products_discussed')->nullable(); // Array of product IDs
            $table->json('categories_discussed')->nullable(); // Array of category IDs
            $table->decimal('budget_discussed', 12, 2)->nullable();
            
            // Outcome and follow-up
            $table->enum('outcome', [
                'information_provided', 'appointment_scheduled', 'quote_given',
                'purchase_made', 'follow_up_required', 'no_interest',
                'complaint_resolved', 'issue_escalated', 'custom_order_placed'
            ])->nullable();
            $table->enum('customer_satisfaction', ['very_satisfied', 'satisfied', 'neutral', 'dissatisfied', 'very_dissatisfied'])->nullable();
            $table->boolean('requires_follow_up')->default(false);
            $table->date('follow_up_date')->nullable();
            $table->text('follow_up_notes')->nullable();
            
            // Duration and effort
            $table->integer('duration_minutes')->nullable();
            $table->enum('effort_level', ['low', 'medium', 'high'])->default('medium');
            
            // Related records
            $table->foreignId('related_invoice_id')->nullable()->constrained('invoices')->onDelete('set null');
            $table->foreignId('related_estimate_id')->nullable()->constrained('estimates')->onDelete('set null');
            $table->string('related_reference_number')->nullable(); // For external references
            
            // Attachments and media
            $table->json('attachments')->nullable(); // File paths for documents, images
            $table->json('recordings')->nullable(); // Call recordings if applicable
            
            // Tracking and analytics
            $table->boolean('is_first_interaction')->default(false);
            $table->integer('interaction_sequence_number')->default(1); // Sequence in customer journey
            $table->timestamp('scheduled_at')->nullable(); // For scheduled interactions
            $table->timestamp('completed_at')->nullable();
            
            // Tags and categorization
            $table->json('tags')->nullable(); // Custom tags for categorization
            $table->text('internal_notes')->nullable(); // Staff-only notes
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['customer_id', 'created_at']);
            $table->index(['interaction_type']);
            $table->index(['interaction_channel']);
            $table->index(['outcome']);
            $table->index(['requires_follow_up', 'follow_up_date']);
            $table->index(['user_id']);
            $table->index(['scheduled_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_interactions');
    }
};
