<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add role column for easier querying (optional, as <PERSON><PERSON> handles roles in separate tables)
            $table->string('role')->nullable()->after('location_id')->index();
        });

        // Populate the role column with current user roles from Spatie tables
        $this->populateRoleColumn();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role']);
            $table->dropColumn('role');
        });
    }

    /**
     * Populate the role column with existing user roles
     */
    private function populateRoleColumn(): void
    {
        // Get all users and their primary role
        $users = User::with('roles')->get();

        foreach ($users as $user) {
            $primaryRole = $user->getRoleNames()->first();
            if ($primaryRole) {
                $user->update(['role' => $primaryRole]);
            }
        }
    }
};
