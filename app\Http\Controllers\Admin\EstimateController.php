<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Estimate;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Location;
use App\Models\MetalRate;
use App\Services\GstCalculationService;
use App\Services\InvoiceNumberService;
use App\Services\MetalRateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EstimateController extends Controller
{
    protected $gstService;
    protected $invoiceNumberService;
    protected $metalRateService;

    public function __construct(
        GstCalculationService $gstService,
        InvoiceNumberService $invoiceNumberService,
        MetalRateService $metalRateService
    ) {
        $this->gstService = $gstService;
        $this->invoiceNumberService = $invoiceNumberService;
        $this->metalRateService = $metalRateService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Estimate::with(['customer', 'location', 'items.product'])
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('estimate_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('first_name', 'like', "%{$search}%")
                                   ->orWhere('last_name', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('estimate_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('estimate_date', '<=', $request->date_to);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Validity filter
        if ($request->filled('validity_status')) {
            switch ($request->validity_status) {
                case 'valid':
                    $query->where('valid_until', '>=', now());
                    break;
                case 'expired':
                    $query->where('valid_until', '<', now());
                    break;
                case 'expiring_soon':
                    $query->whereBetween('valid_until', [now(), now()->addDays(7)]);
                    break;
            }
        }

        $estimates = $query->paginate(20);
        $locations = Location::active()->get();

        // Get summary statistics
        $stats = [
            'total_estimates' => Estimate::count(),
            'pending_estimates' => Estimate::where('status', 'pending')->count(),
            'approved_estimates' => Estimate::where('status', 'approved')->count(),
            'converted_estimates' => Estimate::where('status', 'converted')->count(),
            'total_value' => Estimate::where('status', '!=', 'cancelled')->sum('total_amount'),
            'expiring_soon' => Estimate::where('valid_until', '>=', now())
                                     ->where('valid_until', '<=', now()->addDays(7))
                                     ->count(),
        ];

        return view('admin.estimates.index', compact('estimates', 'locations', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $customers = Customer::active()->orderBy('first_name')->get();
        $locations = Location::active()->get();
        $products = Product::with(['category', 'metal', 'inventory'])->active()->get();

        // Get current metal rates
        $metalRates = $this->metalRateService->getCurrentRates();

        // If customer is pre-selected
        $selectedCustomer = null;
        if ($request->filled('customer_id')) {
            $selectedCustomer = Customer::find($request->customer_id);
        }

        return view('admin.estimates.create', compact('customers', 'locations', 'products', 'metalRates', 'selectedCustomer'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'location_id' => 'required|exists:locations,id',
            'estimate_date' => 'required|date',
            'valid_until' => 'required|date|after:estimate_date',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'nullable|exists:products,id',
            'items.*.custom_item_name' => 'nullable|string|max:255',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.making_charges' => 'nullable|numeric|min:0',
            'items.*.stone_charges' => 'nullable|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.metal_rate_locked' => 'nullable|boolean',
            'items.*.locked_metal_rate' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'terms_conditions' => 'nullable|string|max:2000',
        ]);

        try {
            DB::beginTransaction();

            // Generate estimate number
            $estimateNumber = $this->invoiceNumberService->generateEstimateNumber($validated['location_id']);

            // Calculate totals and GST
            $calculation = $this->gstService->calculateInvoiceTotal($validated);

            // Create estimate
            $estimate = Estimate::create([
                'estimate_number' => $estimateNumber,
                'customer_id' => $validated['customer_id'],
                'location_id' => $validated['location_id'],
                'estimate_date' => $validated['estimate_date'],
                'valid_until' => $validated['valid_until'],
                'subtotal' => $calculation['subtotal'],
                'discount_amount' => $validated['discount_amount'] ?? 0,
                'cgst_amount' => $calculation['cgst_amount'],
                'sgst_amount' => $calculation['sgst_amount'],
                'igst_amount' => $calculation['igst_amount'],
                'total_gst' => $calculation['total_gst'],
                'total_amount' => $calculation['total_amount'],
                'status' => 'pending',
                'notes' => $validated['notes'],
                'terms_conditions' => $validated['terms_conditions'],
                'created_by' => auth()->id(),
            ]);

            // Create estimate items
            foreach ($validated['items'] as $item) {
                $product = null;
                if ($item['product_id']) {
                    $product = Product::find($item['product_id']);
                }

                $itemCalculation = $this->gstService->calculateItemGst($item, $product);

                $estimate->items()->create([
                    'product_id' => $item['product_id'],
                    'custom_item_name' => $item['custom_item_name'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'discount_percentage' => $item['discount_percentage'] ?? 0,
                    'discount_amount' => $itemCalculation['discount_amount'],
                    'making_charges' => $item['making_charges'] ?? 0,
                    'stone_charges' => $item['stone_charges'] ?? 0,
                    'taxable_amount' => $itemCalculation['taxable_amount'],
                    'gst_rate' => $itemCalculation['gst_rate'],
                    'cgst_amount' => $itemCalculation['cgst_amount'],
                    'sgst_amount' => $itemCalculation['sgst_amount'],
                    'igst_amount' => $itemCalculation['igst_amount'],
                    'total_amount' => $itemCalculation['total_amount'],
                    'metal_rate_locked' => $item['metal_rate_locked'] ?? false,
                    'locked_metal_rate' => $item['locked_metal_rate'] ?? null,
                ]);
            }

            DB::commit();

            return redirect()->route('admin.estimates.show', $estimate)
                ->with('success', 'Estimate created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error creating estimate: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Estimate $estimate)
    {
        $estimate->load(['customer', 'location', 'items.product.category', 'createdBy']);

        return view('admin.estimates.show', compact('estimate'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Estimate $estimate)
    {
        if ($estimate->status === 'converted' || $estimate->status === 'cancelled') {
            return redirect()->route('admin.estimates.show', $estimate)
                ->with('error', 'Cannot edit converted or cancelled estimates.');
        }

        $customers = Customer::active()->orderBy('first_name')->get();
        $locations = Location::active()->get();
        $products = Product::with(['category', 'metal', 'inventory'])->active()->get();
        $metalRates = $this->metalRateService->getCurrentRates();
        $estimate->load(['items.product']);

        return view('admin.estimates.edit', compact('estimate', 'customers', 'locations', 'products', 'metalRates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Estimate $estimate)
    {
        if ($estimate->status === 'converted' || $estimate->status === 'cancelled') {
            return redirect()->route('admin.estimates.show', $estimate)
                ->with('error', 'Cannot update converted or cancelled estimates.');
        }

        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'location_id' => 'required|exists:locations,id',
            'estimate_date' => 'required|date',
            'valid_until' => 'required|date|after:estimate_date',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'nullable|exists:products,id',
            'items.*.custom_item_name' => 'nullable|string|max:255',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.making_charges' => 'nullable|numeric|min:0',
            'items.*.stone_charges' => 'nullable|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.metal_rate_locked' => 'nullable|boolean',
            'items.*.locked_metal_rate' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'terms_conditions' => 'nullable|string|max:2000',
        ]);

        try {
            DB::beginTransaction();

            // Delete existing items
            $estimate->items()->delete();

            // Calculate new totals and GST
            $calculation = $this->gstService->calculateInvoiceTotal($validated);

            // Update estimate
            $estimate->update([
                'customer_id' => $validated['customer_id'],
                'location_id' => $validated['location_id'],
                'estimate_date' => $validated['estimate_date'],
                'valid_until' => $validated['valid_until'],
                'subtotal' => $calculation['subtotal'],
                'discount_amount' => $validated['discount_amount'] ?? 0,
                'cgst_amount' => $calculation['cgst_amount'],
                'sgst_amount' => $calculation['sgst_amount'],
                'igst_amount' => $calculation['igst_amount'],
                'total_gst' => $calculation['total_gst'],
                'total_amount' => $calculation['total_amount'],
                'notes' => $validated['notes'],
                'terms_conditions' => $validated['terms_conditions'],
                'updated_by' => auth()->id(),
            ]);

            // Create new estimate items
            foreach ($validated['items'] as $item) {
                $product = null;
                if ($item['product_id']) {
                    $product = Product::find($item['product_id']);
                }

                $itemCalculation = $this->gstService->calculateItemGst($item, $product);

                $estimate->items()->create([
                    'product_id' => $item['product_id'],
                    'custom_item_name' => $item['custom_item_name'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'discount_percentage' => $item['discount_percentage'] ?? 0,
                    'discount_amount' => $itemCalculation['discount_amount'],
                    'making_charges' => $item['making_charges'] ?? 0,
                    'stone_charges' => $item['stone_charges'] ?? 0,
                    'taxable_amount' => $itemCalculation['taxable_amount'],
                    'gst_rate' => $itemCalculation['gst_rate'],
                    'cgst_amount' => $itemCalculation['cgst_amount'],
                    'sgst_amount' => $itemCalculation['sgst_amount'],
                    'igst_amount' => $itemCalculation['igst_amount'],
                    'total_amount' => $itemCalculation['total_amount'],
                    'metal_rate_locked' => $item['metal_rate_locked'] ?? false,
                    'locked_metal_rate' => $item['locked_metal_rate'] ?? null,
                ]);
            }

            DB::commit();

            return redirect()->route('admin.estimates.show', $estimate)
                ->with('success', 'Estimate updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error updating estimate: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Estimate $estimate)
    {
        if ($estimate->status === 'converted') {
            return back()->with('error', 'Cannot delete converted estimates.');
        }

        try {
            DB::beginTransaction();

            // Delete estimate items
            $estimate->items()->delete();

            // Delete estimate
            $estimate->delete();

            DB::commit();

            return redirect()->route('admin.estimates.index')
                ->with('success', 'Estimate deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting estimate: ' . $e->getMessage());
        }
    }

    /**
     * Update estimate status
     */
    public function updateStatus(Request $request, Estimate $estimate)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,approved,rejected,converted,cancelled',
            'status_notes' => 'nullable|string|max:500',
        ]);

        try {
            $estimate->update([
                'status' => $validated['status'],
                'status_notes' => $validated['status_notes'],
                'status_updated_at' => now(),
                'status_updated_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Estimate status updated successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating status: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Convert estimate to invoice
     */
    public function convertToInvoice(Estimate $estimate)
    {
        if ($estimate->status !== 'approved') {
            return back()->with('error', 'Only approved estimates can be converted to invoices.');
        }

        if ($estimate->valid_until < now()) {
            return back()->with('error', 'Cannot convert expired estimates to invoices.');
        }

        try {
            DB::beginTransaction();

            // Create invoice from estimate
            $invoice = $estimate->convertToInvoice();

            // Update estimate status
            $estimate->update([
                'status' => 'converted',
                'converted_to_invoice_id' => $invoice->id,
                'converted_at' => now(),
                'converted_by' => auth()->id(),
            ]);

            DB::commit();

            return redirect()->route('admin.invoices.show', $invoice)
                ->with('success', 'Estimate converted to invoice successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error converting estimate: ' . $e->getMessage());
        }
    }

    /**
     * Print estimate
     */
    public function print(Estimate $estimate)
    {
        $estimate->load(['customer', 'location', 'items.product.category', 'createdBy']);

        return view('admin.estimates.print', compact('estimate'));
    }

    /**
     * Download estimate PDF
     */
    public function downloadPdf(Estimate $estimate)
    {
        // This would implement PDF generation
        return response()->json(['message' => 'PDF download functionality to be implemented']);
    }

    /**
     * Get current metal rates
     */
    public function getMetalRates()
    {
        $rates = $this->metalRateService->getCurrentRates();

        return response()->json($rates);
    }

    /**
     * Calculate estimate totals
     */
    public function calculateTotals(Request $request)
    {
        try {
            $calculation = $this->gstService->calculateInvoiceTotal($request->all());

            return response()->json([
                'success' => true,
                'calculation' => $calculation
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating totals: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Extend estimate validity
     */
    public function extendValidity(Request $request, Estimate $estimate)
    {
        $validated = $request->validate([
            'valid_until' => 'required|date|after:today',
            'extension_notes' => 'nullable|string|max:500',
        ]);

        try {
            $estimate->update([
                'valid_until' => $validated['valid_until'],
                'extension_notes' => $validated['extension_notes'],
                'extended_at' => now(),
                'extended_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Estimate validity extended successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error extending validity: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export estimates
     */
    public function export(Request $request)
    {
        // This would implement CSV/Excel export functionality
        return response()->json(['message' => 'Export functionality to be implemented']);
    }

    /**
     * Get estimate statistics
     */
    public function getStatistics(Request $request)
    {
        $startDate = $request->start_date ?? now()->startOfMonth();
        $endDate = $request->end_date ?? now()->endOfMonth();

        $stats = [
            'total_estimates' => Estimate::whereBetween('estimate_date', [$startDate, $endDate])->count(),
            'pending_estimates' => Estimate::whereBetween('estimate_date', [$startDate, $endDate])->where('status', 'pending')->count(),
            'approved_estimates' => Estimate::whereBetween('estimate_date', [$startDate, $endDate])->where('status', 'approved')->count(),
            'converted_estimates' => Estimate::whereBetween('estimate_date', [$startDate, $endDate])->where('status', 'converted')->count(),
            'total_value' => Estimate::whereBetween('estimate_date', [$startDate, $endDate])->sum('total_amount'),
            'conversion_rate' => 0,
        ];

        if ($stats['approved_estimates'] > 0) {
            $stats['conversion_rate'] = ($stats['converted_estimates'] / $stats['approved_estimates']) * 100;
        }

        return response()->json($stats);
    }
}
