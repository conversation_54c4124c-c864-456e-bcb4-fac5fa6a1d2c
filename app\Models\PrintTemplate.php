<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PrintTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'template_type',
        'description',
        'template_content',
        'template_variables',
        'required_fields',
        'paper_width_mm',
        'paper_height_mm',
        'margin_left',
        'margin_right',
        'margin_top',
        'margin_bottom',
        'default_font_size',
        'supports_bold',
        'supports_underline',
        'supports_alignment',
        'header_settings',
        'body_settings',
        'footer_settings',
        'include_logo',
        'include_barcode',
        'include_qr_code',
        'auto_cut_after',
        'feed_lines_after',
        'open_drawer_after',
        'copies_default',
        'compatible_printers',
        'supported_paper_sizes',
        'is_system_template',
        'is_active',
        'is_default',
        'version',
        'changelog',
        'parent_template_id',
        'usage_count',
        'last_used_at',
        'average_print_time_seconds',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'template_variables' => 'array',
        'required_fields' => 'array',
        'supports_bold' => 'boolean',
        'supports_underline' => 'boolean',
        'supports_alignment' => 'boolean',
        'header_settings' => 'array',
        'body_settings' => 'array',
        'footer_settings' => 'array',
        'include_logo' => 'boolean',
        'include_barcode' => 'boolean',
        'include_qr_code' => 'boolean',
        'auto_cut_after' => 'boolean',
        'open_drawer_after' => 'boolean',
        'compatible_printers' => 'array',
        'supported_paper_sizes' => 'array',
        'is_system_template' => 'boolean',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'last_used_at' => 'datetime',
        'average_print_time_seconds' => 'decimal:2',
    ];

    /**
     * Relationships
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function parentTemplate(): BelongsTo
    {
        return $this->belongsTo(PrintTemplate::class, 'parent_template_id');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $templateType)
    {
        return $query->where('template_type', $templateType);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeSystem($query)
    {
        return $query->where('is_system_template', true);
    }

    public function scopeCustom($query)
    {
        return $query->where('is_system_template', false);
    }

    /**
     * Accessors
     */
    public function getTemplateTypeDisplayAttribute()
    {
        $types = [
            'invoice' => 'Invoice',
            'receipt' => 'Receipt',
            'estimate' => 'Estimate',
            'tag' => 'Inventory Tag',
            'label' => 'Label',
            'barcode' => 'Barcode',
            'qr_code' => 'QR Code',
            'report' => 'Report',
            'custom' => 'Custom',
        ];

        return $types[$this->template_type] ?? ucfirst(str_replace('_', ' ', $this->template_type));
    }

    public function getFontSizeDisplayAttribute()
    {
        $sizes = [
            'small' => 'Small',
            'medium' => 'Medium',
            'large' => 'Large',
        ];

        return $sizes[$this->default_font_size] ?? 'Medium';
    }

    public function getPaperSizeDisplayAttribute()
    {
        $width = $this->paper_width_mm;
        $height = $this->paper_height_mm;

        if ($height) {
            return "{$width}mm x {$height}mm";
        }

        return "{$width}mm (continuous)";
    }

    public function getFeaturesAttribute()
    {
        $features = [];

        if ($this->supports_bold) $features[] = 'Bold text';
        if ($this->supports_underline) $features[] = 'Underline';
        if ($this->supports_alignment) $features[] = 'Text alignment';
        if ($this->include_logo) $features[] = 'Logo';
        if ($this->include_barcode) $features[] = 'Barcode';
        if ($this->include_qr_code) $features[] = 'QR Code';
        if ($this->auto_cut_after) $features[] = 'Auto cut';
        if ($this->open_drawer_after) $features[] = 'Cash drawer';

        return $features;
    }

    public function getUsageStatsAttribute()
    {
        return [
            'usage_count' => $this->usage_count,
            'last_used' => $this->last_used_at?->diffForHumans(),
            'average_print_time' => $this->average_print_time_seconds ?
                round($this->average_print_time_seconds, 2) . 's' : 'N/A',
        ];
    }

    /**
     * Process template content with data
     */
    public function processContent($data)
    {
        $content = $this->template_content;

        // Handle simple placeholders
        foreach ($data as $key => $value) {
            if (is_scalar($value)) {
                $content = str_replace("{{" . $key . "}}", $value, $content);
            }
        }

        // Handle array data (like items)
        $content = $this->processArrayPlaceholders($content, $data);

        return $content;
    }

    /**
     * Process array placeholders (like {{#items}}...{{/items}})
     */
    private function processArrayPlaceholders($content, $data)
    {
        // Simple implementation for array processing
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $pattern = '/\{\{#' . $key . '\}\}(.*?)\{\{\/' . $key . '\}\}/s';

                if (preg_match($pattern, $content, $matches)) {
                    $template = $matches[1];
                    $output = '';

                    foreach ($value as $item) {
                        $itemOutput = $template;
                        foreach ($item as $itemKey => $itemValue) {
                            $itemOutput = str_replace("{{" . $itemKey . "}}", $itemValue, $itemOutput);
                        }
                        $output .= $itemOutput;
                    }

                    $content = str_replace($matches[0], $output, $content);
                }
            }
        }

        return $content;
    }

    /**
     * Validate template content
     */
    public function validateContent()
    {
        $errors = [];

        // Check for required fields
        if ($this->required_fields) {
            foreach ($this->required_fields as $field) {
                if (strpos($this->template_content, "{{" . $field . "}}") === false) {
                    $errors[] = "Required field '{$field}' not found in template";
                }
            }
        }

        // Check for unclosed placeholders
        if (preg_match_all('/\{\{[^}]*$/', $this->template_content, $matches)) {
            $errors[] = "Unclosed placeholders found: " . implode(', ', $matches[0]);
        }

        // Check for invalid commands
        if (preg_match_all('/\[([A-Z_]+):[^\]]*\]/', $this->template_content, $matches)) {
            $validCommands = ['BARCODE', 'QRCODE', 'IMAGE', 'SEPARATOR', 'FEED'];
            foreach ($matches[1] as $command) {
                if (!in_array($command, $validCommands)) {
                    $errors[] = "Invalid command: [{$command}]";
                }
            }
        }

        return $errors;
    }

    /**
     * Clone template
     */
    public function cloneTemplate($newName, $newCode = null)
    {
        $clone = $this->replicate();
        $clone->name = $newName;
        $clone->code = $newCode ?: $this->code . '_copy_' . time();
        $clone->is_default = false;
        $clone->is_system_template = false;
        $clone->parent_template_id = $this->id;
        $clone->usage_count = 0;
        $clone->last_used_at = null;
        $clone->created_by = auth()->id();
        $clone->updated_by = null;
        $clone->save();

        return $clone;
    }

    /**
     * Record usage
     */
    public function recordUsage($printTimeSeconds = null)
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);

        if ($printTimeSeconds) {
            // Update average print time
            $currentAvg = $this->average_print_time_seconds ?: 0;
            $newAvg = (($currentAvg * ($this->usage_count - 1)) + $printTimeSeconds) / $this->usage_count;
            $this->update(['average_print_time_seconds' => $newAvg]);
        }
    }

    /**
     * Get template variables from content
     */
    public function extractVariables()
    {
        $content = $this->template_content;
        $variables = [];

        // Extract simple placeholders
        if (preg_match_all('/\{\{([^}#\/]+)\}\}/', $content, $matches)) {
            $variables = array_unique($matches[1]);
        }

        // Extract array placeholders
        if (preg_match_all('/\{\{#([^}]+)\}\}/', $content, $matches)) {
            foreach ($matches[1] as $arrayVar) {
                $variables[] = $arrayVar . ' (array)';
            }
        }

        return $variables;
    }

    /**
     * Check compatibility with printer
     */
    public function isCompatibleWith($printerId)
    {
        if (!$this->compatible_printers) {
            return true; // Compatible with all if not specified
        }

        $printer = PrinterConfiguration::find($printerId);
        if (!$printer) {
            return false;
        }

        // Check if printer type/model is in compatible list
        return in_array($printer->type, $this->compatible_printers) ||
               in_array($printer->name, $this->compatible_printers);
    }

    /**
     * Get template summary
     */
    public function getSummary()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'type' => $this->template_type_display,
            'description' => $this->description,
            'paper_size' => $this->paper_size_display,
            'features' => $this->features,
            'is_default' => $this->is_default,
            'is_system' => $this->is_system_template,
            'usage_stats' => $this->usage_stats,
            'variables' => $this->extractVariables(),
            'version' => $this->version,
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
