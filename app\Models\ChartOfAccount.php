<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChartOfAccount extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'account_code',
        'account_name',
        'account_type',
        'account_category',
        'parent_account_id',
        'description',
        'is_active',
        'is_system_account',
        'opening_balance',
        'current_balance',
        'balance_type',
        'tax_applicable',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_system_account' => 'boolean',
        'tax_applicable' => 'boolean',
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($account) {
            if (!$account->account_code) {
                $account->account_code = static::generateAccountCode($account->account_type);
            }
            $account->created_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function parentAccount(): BelongsTo
    {
        return $this->belongsTo(ChartOfAccount::class, 'parent_account_id');
    }

    public function childAccounts(): HasMany
    {
        return $this->hasMany(ChartOfAccount::class, 'parent_account_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function journalEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class, 'account_id');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(AccountTransaction::class, 'account_id');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('account_type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('account_category', $category);
    }

    public function scopeParentAccounts($query)
    {
        return $query->whereNull('parent_account_id');
    }

    public function scopeChildAccounts($query)
    {
        return $query->whereNotNull('parent_account_id');
    }

    public function scopeSystemAccounts($query)
    {
        return $query->where('is_system_account', true);
    }

    /**
     * Accessors
     */
    public function getAccountTypeDisplayAttribute(): string
    {
        $types = [
            'asset' => 'Asset',
            'liability' => 'Liability',
            'equity' => 'Equity',
            'revenue' => 'Revenue',
            'expense' => 'Expense',
        ];

        return $types[$this->account_type] ?? ucfirst($this->account_type);
    }

    public function getAccountCategoryDisplayAttribute(): string
    {
        $categories = [
            // Assets
            'current_asset' => 'Current Asset',
            'fixed_asset' => 'Fixed Asset',
            'intangible_asset' => 'Intangible Asset',
            'investment' => 'Investment',

            // Liabilities
            'current_liability' => 'Current Liability',
            'long_term_liability' => 'Long Term Liability',

            // Equity
            'owner_equity' => 'Owner\'s Equity',
            'retained_earnings' => 'Retained Earnings',

            // Revenue
            'sales_revenue' => 'Sales Revenue',
            'service_revenue' => 'Service Revenue',
            'other_income' => 'Other Income',

            // Expenses
            'cost_of_goods_sold' => 'Cost of Goods Sold',
            'operating_expense' => 'Operating Expense',
            'administrative_expense' => 'Administrative Expense',
            'financial_expense' => 'Financial Expense',
        ];

        return $categories[$this->account_category] ?? ucfirst(str_replace('_', ' ', $this->account_category));
    }

    public function getBalanceTypeDisplayAttribute(): string
    {
        return $this->balance_type === 'debit' ? 'Debit' : 'Credit';
    }

    public function getFullAccountNameAttribute(): string
    {
        if ($this->parentAccount) {
            return $this->parentAccount->account_name . ' - ' . $this->account_name;
        }
        return $this->account_name;
    }

    public function getFormattedBalanceAttribute(): string
    {
        return '₹' . number_format(abs($this->current_balance), 2) .
               ' ' . ($this->current_balance >= 0 ? 'Dr' : 'Cr');
    }

    public function getIsDebitBalanceAttribute(): bool
    {
        return in_array($this->account_type, ['asset', 'expense']) ?
               $this->current_balance >= 0 : $this->current_balance < 0;
    }

    public function getIsCreditBalanceAttribute(): bool
    {
        return !$this->is_debit_balance;
    }

    /**
     * Business Logic Methods
     */
    public static function generateAccountCode($accountType): string
    {
        $prefixes = [
            'asset' => '1',
            'liability' => '2',
            'equity' => '3',
            'revenue' => '4',
            'expense' => '5',
        ];

        $prefix = $prefixes[$accountType] ?? '9';

        $lastAccount = static::where('account_type', $accountType)
                            ->where('account_code', 'like', $prefix . '%')
                            ->orderBy('account_code', 'desc')
                            ->first();

        if ($lastAccount) {
            $lastNumber = (int) substr($lastAccount->account_code, 1);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1001; // Start from 1001
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    public function updateBalance($amount, $type = 'debit'): self
    {
        $multiplier = 1;

        // Determine if this increases or decreases the balance
        if (in_array($this->account_type, ['asset', 'expense'])) {
            // Assets and Expenses increase with debits, decrease with credits
            $multiplier = $type === 'debit' ? 1 : -1;
        } else {
            // Liabilities, Equity, and Revenue increase with credits, decrease with debits
            $multiplier = $type === 'credit' ? 1 : -1;
        }

        $this->increment('current_balance', $amount * $multiplier);

        return $this;
    }

    public function getAccountHierarchy(): array
    {
        $hierarchy = [];
        $current = $this;

        while ($current) {
            array_unshift($hierarchy, [
                'id' => $current->id,
                'name' => $current->account_name,
                'code' => $current->account_code,
            ]);
            $current = $current->parentAccount;
        }

        return $hierarchy;
    }

    public function getTotalBalance(): float
    {
        $balance = $this->current_balance;

        // Add balances of all child accounts
        foreach ($this->childAccounts as $child) {
            $balance += $child->getTotalBalance();
        }

        return $balance;
    }

    public function canBeDeleted(): bool
    {
        // Cannot delete if it has transactions
        if ($this->transactions()->count() > 0) {
            return false;
        }

        // Cannot delete if it has child accounts
        if ($this->childAccounts()->count() > 0) {
            return false;
        }

        // Cannot delete system accounts
        if ($this->is_system_account) {
            return false;
        }

        return true;
    }

    public function activate(): self
    {
        $this->update(['is_active' => true]);
        return $this;
    }

    public function deactivate(): self
    {
        if (!$this->canBeDeleted()) {
            throw new \Exception('Cannot deactivate account with transactions or child accounts');
        }

        $this->update(['is_active' => false]);
        return $this;
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'account_code' => $this->account_code,
            'account_name' => $this->account_name,
            'full_name' => $this->full_account_name,
            'account_type' => $this->account_type_display,
            'account_category' => $this->account_category_display,
            'current_balance' => $this->current_balance,
            'formatted_balance' => $this->formatted_balance,
            'balance_type' => $this->balance_type_display,
            'is_active' => $this->is_active,
            'is_system_account' => $this->is_system_account,
            'parent_account' => $this->parentAccount?->account_name,
            'child_accounts_count' => $this->childAccounts()->count(),
            'transactions_count' => $this->transactions()->count(),
        ];
    }

    /**
     * Static Methods for Account Setup
     */
    public static function createDefaultAccounts(): void
    {
        $defaultAccounts = [
            // Assets
            ['account_name' => 'Cash', 'account_type' => 'asset', 'account_category' => 'current_asset', 'balance_type' => 'debit', 'is_system_account' => true],
            ['account_name' => 'Bank Account', 'account_type' => 'asset', 'account_category' => 'current_asset', 'balance_type' => 'debit', 'is_system_account' => true],
            ['account_name' => 'Accounts Receivable', 'account_type' => 'asset', 'account_category' => 'current_asset', 'balance_type' => 'debit', 'is_system_account' => true],
            ['account_name' => 'Inventory', 'account_type' => 'asset', 'account_category' => 'current_asset', 'balance_type' => 'debit', 'is_system_account' => true],
            ['account_name' => 'Equipment', 'account_type' => 'asset', 'account_category' => 'fixed_asset', 'balance_type' => 'debit', 'is_system_account' => true],

            // Liabilities
            ['account_name' => 'Accounts Payable', 'account_type' => 'liability', 'account_category' => 'current_liability', 'balance_type' => 'credit', 'is_system_account' => true],
            ['account_name' => 'GST Payable', 'account_type' => 'liability', 'account_category' => 'current_liability', 'balance_type' => 'credit', 'is_system_account' => true],
            ['account_name' => 'TDS Payable', 'account_type' => 'liability', 'account_category' => 'current_liability', 'balance_type' => 'credit', 'is_system_account' => true],

            // Equity
            ['account_name' => 'Owner\'s Capital', 'account_type' => 'equity', 'account_category' => 'owner_equity', 'balance_type' => 'credit', 'is_system_account' => true],
            ['account_name' => 'Retained Earnings', 'account_type' => 'equity', 'account_category' => 'retained_earnings', 'balance_type' => 'credit', 'is_system_account' => true],

            // Revenue
            ['account_name' => 'Sales Revenue', 'account_type' => 'revenue', 'account_category' => 'sales_revenue', 'balance_type' => 'credit', 'is_system_account' => true],
            ['account_name' => 'Service Revenue', 'account_type' => 'revenue', 'account_category' => 'service_revenue', 'balance_type' => 'credit', 'is_system_account' => true],
            ['account_name' => 'Other Income', 'account_type' => 'revenue', 'account_category' => 'other_income', 'balance_type' => 'credit', 'is_system_account' => true],

            // Expenses
            ['account_name' => 'Cost of Goods Sold', 'account_type' => 'expense', 'account_category' => 'cost_of_goods_sold', 'balance_type' => 'debit', 'is_system_account' => true],
            ['account_name' => 'Rent Expense', 'account_type' => 'expense', 'account_category' => 'operating_expense', 'balance_type' => 'debit', 'is_system_account' => true],
            ['account_name' => 'Salary Expense', 'account_type' => 'expense', 'account_category' => 'operating_expense', 'balance_type' => 'debit', 'is_system_account' => true],
            ['account_name' => 'Utilities Expense', 'account_type' => 'expense', 'account_category' => 'operating_expense', 'balance_type' => 'debit', 'is_system_account' => true],
        ];

        foreach ($defaultAccounts as $account) {
            static::firstOrCreate(
                ['account_name' => $account['account_name']],
                array_merge($account, ['is_active' => true, 'opening_balance' => 0, 'current_balance' => 0])
            );
        }
    }
}
