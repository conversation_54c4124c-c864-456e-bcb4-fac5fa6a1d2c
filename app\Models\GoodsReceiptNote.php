<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class GoodsReceiptNote extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'grn_number',
        'purchase_order_id',
        'supplier_id',
        'location_id',
        'received_date',
        'quality_rating',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'received_date' => 'date',
        'quality_rating' => 'decimal:1',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($grn) {
            if (!$grn->grn_number) {
                $grn->grn_number = static::generateGRNNumber();
            }
            $grn->created_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function items(): HasMany
    {
        return $this->hasMany(GoodsReceiptNoteItem::class);
    }

    /**
     * Scopes
     */
    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('received_date', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('received_date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('received_date', now()->month)
                    ->whereYear('received_date', now()->year);
    }

    /**
     * Accessors
     */
    public function getTotalQuantityReceivedAttribute()
    {
        return $this->items->sum('quantity_received');
    }

    public function getTotalValueReceivedAttribute()
    {
        return $this->items->sum('total_price');
    }

    public function getQualityRatingDisplayAttribute()
    {
        if (!$this->quality_rating) {
            return 'Not Rated';
        }

        $ratings = [
            1 => 'Poor',
            2 => 'Fair',
            3 => 'Good',
            4 => 'Very Good',
            5 => 'Excellent',
        ];

        return $ratings[$this->quality_rating] ?? 'Unknown';
    }

    public function getQualityRatingColorAttribute()
    {
        if (!$this->quality_rating) {
            return 'gray';
        }

        $colors = [
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'blue',
            5 => 'green',
        ];

        return $colors[$this->quality_rating] ?? 'gray';
    }

    public function getIsOnTimeAttribute()
    {
        if (!$this->purchaseOrder->expected_delivery_date) {
            return null;
        }

        return $this->received_date <= $this->purchaseOrder->expected_delivery_date;
    }

    public function getDeliveryStatusAttribute()
    {
        if ($this->is_on_time === null) {
            return 'No Expected Date';
        }

        return $this->is_on_time ? 'On Time' : 'Late';
    }

    public function getDeliveryStatusColorAttribute()
    {
        if ($this->is_on_time === null) {
            return 'gray';
        }

        return $this->is_on_time ? 'green' : 'red';
    }

    public function getDaysFromExpectedAttribute()
    {
        if (!$this->purchaseOrder->expected_delivery_date) {
            return null;
        }

        return $this->received_date->diffInDays($this->purchaseOrder->expected_delivery_date, false);
    }

    /**
     * Business Logic Methods
     */
    public static function generateGRNNumber()
    {
        $prefix = 'GRN';
        $year = now()->format('y');
        $month = now()->format('m');

        $lastGRN = static::whereYear('created_at', now()->year)
                        ->whereMonth('created_at', now()->month)
                        ->orderBy('id', 'desc')
                        ->first();

        $sequence = $lastGRN ? (int) substr($lastGRN->grn_number, -4) + 1 : 1;

        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function addItem($poItemId, $quantityReceived, $qualityRating = null, $notes = null)
    {
        $poItem = PurchaseOrderItem::findOrFail($poItemId);

        if ($poItem->purchase_order_id !== $this->purchase_order_id) {
            throw new \Exception('Purchase order item does not belong to this purchase order');
        }

        $item = $this->items()->create([
            'purchase_order_item_id' => $poItemId,
            'product_id' => $poItem->product_id,
            'quantity_received' => $quantityReceived,
            'unit_price' => $poItem->unit_price,
            'total_price' => $quantityReceived * $poItem->unit_price,
            'quality_rating' => $qualityRating,
            'notes' => $notes,
        ]);

        // Update PO item received quantity
        $poItem->increment('quantity_received', $quantityReceived);

        // Update PO status
        $this->purchaseOrder->updateReceiptStatus();

        return $item;
    }

    public function updateQualityRating($rating, $notes = null)
    {
        $this->update([
            'quality_rating' => $rating,
            'notes' => $notes ? ($this->notes . "\n\nQuality Rating Update: " . $notes) : $this->notes,
        ]);

        // Log the rating update
        activity()
            ->performedOn($this)
            ->withProperties([
                'quality_rating' => $rating,
                'notes' => $notes,
            ])
            ->log('Quality rating updated for GRN');

        return $this;
    }

    public function updateInventory()
    {
        foreach ($this->items as $item) {
            // Create inventory items for received products
            for ($i = 0; $i < $item->quantity_received; $i++) {
                InventoryItem::create([
                    'product_id' => $item->product_id,
                    'location_id' => $this->location_id,
                    'unique_tag' => InventoryItem::generateUniqueTag(),
                    'barcode' => InventoryItem::generateBarcode(),
                    'purchase_price' => $item->unit_price,
                    'selling_price' => $item->unit_price * 1.3, // 30% markup as default
                    'status' => 'available',
                    'source_type' => 'purchase',
                    'source_reference' => $this->grn_number,
                ]);
            }
        }

        // Log inventory update
        activity()
            ->performedOn($this)
            ->withProperties([
                'total_items_added' => $this->total_quantity_received,
                'location_id' => $this->location_id,
            ])
            ->log('Inventory updated from GRN');

        return $this;
    }

    public function getItemsSummary()
    {
        return $this->items->map(function ($item) {
            return [
                'product_name' => $item->product->name,
                'product_code' => $item->product->product_code,
                'quantity_received' => $item->quantity_received,
                'unit_price' => $item->unit_price,
                'total_price' => $item->total_price,
                'quality_rating' => $item->quality_rating,
                'notes' => $item->notes,
            ];
        });
    }

    public function getSummary()
    {
        return [
            'grn_number' => $this->grn_number,
            'purchase_order_number' => $this->purchaseOrder->po_number,
            'supplier_name' => $this->supplier->name,
            'location_name' => $this->location->name,
            'received_date' => $this->received_date->format('Y-m-d'),
            'total_quantity_received' => $this->total_quantity_received,
            'total_value_received' => $this->total_value_received,
            'quality_rating' => $this->quality_rating_display,
            'delivery_status' => $this->delivery_status,
            'days_from_expected' => $this->days_from_expected,
            'total_items' => $this->items->count(),
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'notes' => $this->notes,
        ];
    }
}
