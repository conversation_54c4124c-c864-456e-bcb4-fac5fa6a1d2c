<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Category::with(['parent', 'children'])
            ->withCount(['products', 'children']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('hsn_code', 'like', "%{$search}%");
            });
        }

        // Filter by parent category
        if ($request->filled('parent_id')) {
            if ($request->parent_id === 'root') {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $request->parent_id);
            }
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $categories = $query->orderBy('sort_order')->orderBy('name')->paginate(20);

        // Get parent categories for filter
        $parentCategories = Category::whereNull('parent_id')->active()->orderBy('name')->get();

        // Get statistics
        $stats = [
            'total_categories' => Category::count(),
            'active_categories' => Category::active()->count(),
            'root_categories' => Category::whereNull('parent_id')->count(),
            'subcategories' => Category::whereNotNull('parent_id')->count(),
            'categories_with_products' => Category::has('products')->count(),
        ];

        return view('admin.categories.index', compact('categories', 'parentCategories', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $parentCategories = Category::whereNull('parent_id')->active()->orderBy('name')->get();
        $selectedParent = null;

        if ($request->filled('parent_id')) {
            $selectedParent = Category::find($request->parent_id);
        }

        return view('admin.categories.create', compact('parentCategories', 'selectedParent'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories,name',
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|exists:categories,id',
            'hsn_code' => 'nullable|string|max:20',
            'gst_rate' => 'nullable|numeric|min:0|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        try {
            DB::beginTransaction();

            // Handle image upload
            $imagePath = null;
            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('categories', 'public');
            }

            // Generate slug
            $slug = Str::slug($validated['name']);
            $originalSlug = $slug;
            $counter = 1;

            while (Category::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Set sort order if not provided
            if (!isset($validated['sort_order'])) {
                $maxOrder = Category::where('parent_id', $validated['parent_id'])->max('sort_order') ?? 0;
                $validated['sort_order'] = $maxOrder + 1;
            }

            $category = Category::create([
                'name' => $validated['name'],
                'slug' => $slug,
                'description' => $validated['description'],
                'parent_id' => $validated['parent_id'],
                'hsn_code' => $validated['hsn_code'],
                'gst_rate' => $validated['gst_rate'],
                'sort_order' => $validated['sort_order'],
                'is_active' => $validated['is_active'] ?? true,
                'image_path' => $imagePath,
                'created_by' => auth()->id(),
            ]);

            DB::commit();

            return redirect()->route('admin.categories.index')
                ->with('success', 'Category created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error creating category: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        $category->load(['parent', 'children.products', 'products.inventory']);

        // Get category statistics
        $stats = [
            'total_products' => $category->products()->count(),
            'active_products' => $category->products()->active()->count(),
            'total_inventory_value' => $category->products()
                ->join('inventories', 'products.id', '=', 'inventories.product_id')
                ->sum(DB::raw('inventories.quantity_available * inventories.cost_price')),
            'subcategories' => $category->children()->count(),
            'total_sales' => $category->products()
                ->join('invoice_items', 'products.id', '=', 'invoice_items.product_id')
                ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
                ->where('invoices.status', '!=', 'cancelled')
                ->sum('invoice_items.total_amount'),
        ];

        return view('admin.categories.show', compact('category', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category)
    {
        $parentCategories = Category::whereNull('parent_id')
            ->where('id', '!=', $category->id)
            ->active()
            ->orderBy('name')
            ->get();

        return view('admin.categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|exists:categories,id',
            'hsn_code' => 'nullable|string|max:20',
            'gst_rate' => 'nullable|numeric|min:0|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Prevent setting parent to self or descendant
        if ($validated['parent_id'] && $this->isDescendant($category->id, $validated['parent_id'])) {
            return back()->withInput()
                ->with('error', 'Cannot set parent to self or descendant category.');
        }

        try {
            DB::beginTransaction();

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image
                if ($category->image_path) {
                    \Storage::disk('public')->delete($category->image_path);
                }
                $validated['image_path'] = $request->file('image')->store('categories', 'public');
            }

            // Update slug if name changed
            if ($category->name !== $validated['name']) {
                $slug = Str::slug($validated['name']);
                $originalSlug = $slug;
                $counter = 1;

                while (Category::where('slug', $slug)->where('id', '!=', $category->id)->exists()) {
                    $slug = $originalSlug . '-' . $counter;
                    $counter++;
                }
                $validated['slug'] = $slug;
            }

            $validated['updated_by'] = auth()->id();

            $category->update($validated);

            DB::commit();

            return redirect()->route('admin.categories.index')
                ->with('success', 'Category updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error updating category: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category)
    {
        // Check if category has products
        if ($category->products()->count() > 0) {
            return back()->with('error', 'Cannot delete category with existing products. Please move or delete products first.');
        }

        // Check if category has subcategories
        if ($category->children()->count() > 0) {
            return back()->with('error', 'Cannot delete category with subcategories. Please delete subcategories first.');
        }

        try {
            DB::beginTransaction();

            // Delete image if exists
            if ($category->image_path) {
                \Storage::disk('public')->delete($category->image_path);
            }

            $category->delete();

            DB::commit();

            return redirect()->route('admin.categories.index')
                ->with('success', 'Category deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting category: ' . $e->getMessage());
        }
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(Category $category)
    {
        try {
            $category->update([
                'is_active' => !$category->is_active,
                'updated_by' => auth()->id(),
            ]);

            $status = $category->is_active ? 'activated' : 'deactivated';

            return response()->json([
                'success' => true,
                'message' => "Category {$status} successfully.",
                'is_active' => $category->is_active,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating category status: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Update category sort order
     */
    public function updateSortOrder(Request $request)
    {
        $validated = $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        try {
            DB::beginTransaction();

            foreach ($validated['categories'] as $categoryData) {
                Category::where('id', $categoryData['id'])
                    ->update([
                        'sort_order' => $categoryData['sort_order'],
                        'updated_by' => auth()->id(),
                    ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Category order updated successfully.',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error updating category order: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get category tree for API
     */
    public function getTree()
    {
        $categories = Category::with(['children' => function ($query) {
            $query->active()->orderBy('sort_order')->orderBy('name');
        }])
        ->whereNull('parent_id')
        ->active()
        ->orderBy('sort_order')
        ->orderBy('name')
        ->get();

        return response()->json($this->buildCategoryTree($categories));
    }

    /**
     * Get categories for select dropdown
     */
    public function getForSelect(Request $request)
    {
        $query = Category::active();

        if ($request->filled('parent_only')) {
            $query->whereNull('parent_id');
        }

        if ($request->filled('with_children')) {
            $query->with('children');
        }

        $categories = $query->orderBy('sort_order')->orderBy('name')->get();

        return response()->json($categories);
    }

    /**
     * Bulk operations
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'category_ids' => 'required|array|min:1',
            'category_ids.*' => 'exists:categories,id',
        ]);

        try {
            DB::beginTransaction();

            $categories = Category::whereIn('id', $validated['category_ids'])->get();

            switch ($validated['action']) {
                case 'activate':
                    Category::whereIn('id', $validated['category_ids'])
                        ->update(['is_active' => true, 'updated_by' => auth()->id()]);
                    $message = 'Categories activated successfully.';
                    break;

                case 'deactivate':
                    Category::whereIn('id', $validated['category_ids'])
                        ->update(['is_active' => false, 'updated_by' => auth()->id()]);
                    $message = 'Categories deactivated successfully.';
                    break;

                case 'delete':
                    // Check for products and subcategories
                    foreach ($categories as $category) {
                        if ($category->products()->count() > 0 || $category->children()->count() > 0) {
                            throw new \Exception("Cannot delete category '{$category->name}' - it has products or subcategories.");
                        }
                    }

                    // Delete images
                    foreach ($categories as $category) {
                        if ($category->image_path) {
                            \Storage::disk('public')->delete($category->image_path);
                        }
                    }

                    Category::whereIn('id', $validated['category_ids'])->delete();
                    $message = 'Categories deleted successfully.';
                    break;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $message,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error performing bulk action: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if category is descendant of another
     */
    private function isDescendant($categoryId, $potentialAncestorId)
    {
        $category = Category::find($categoryId);

        while ($category && $category->parent_id) {
            if ($category->parent_id == $potentialAncestorId) {
                return true;
            }
            $category = $category->parent;
        }

        return false;
    }

    /**
     * Build category tree structure
     */
    private function buildCategoryTree($categories)
    {
        return $categories->map(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description,
                'hsn_code' => $category->hsn_code,
                'gst_rate' => $category->gst_rate,
                'products_count' => $category->products_count,
                'children' => $this->buildCategoryTree($category->children),
            ];
        });
    }
}
