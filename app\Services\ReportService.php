<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Estimate;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Inventory;
use App\Models\InventoryMovement;
use App\Models\Location;
use App\Models\Category;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class ReportService
{
    /**
     * Get dashboard summary data
     */
    public function getDashboardSummary($dateRange)
    {
        $startDate = Carbon::parse($dateRange['start']);
        $endDate = Carbon::parse($dateRange['end']);

        return [
            'sales' => $this->getSalesSummary($startDate, $endDate),
            'estimates' => $this->getEstimatesSummary($startDate, $endDate),
            'customers' => $this->getCustomersSummary($startDate, $endDate),
            'inventory' => $this->getInventorySummary(),
            'top_products' => $this->getTopProducts($startDate, $endDate),
            'recent_activities' => $this->getRecentActivities(),
        ];
    }

    /**
     * Get sales summary
     */
    private function getSalesSummary($startDate, $endDate)
    {
        $currentPeriod = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled');

        $previousStart = $startDate->copy()->subDays($startDate->diffInDays($endDate) + 1);
        $previousEnd = $startDate->copy()->subDay();
        
        $previousPeriod = Invoice::whereBetween('invoice_date', [$previousStart, $previousEnd])
            ->where('status', '!=', 'cancelled');

        $currentRevenue = $currentPeriod->sum('total_amount');
        $previousRevenue = $previousPeriod->sum('total_amount');
        
        $currentCount = $currentPeriod->count();
        $previousCount = $previousPeriod->count();

        return [
            'total_revenue' => $currentRevenue,
            'total_invoices' => $currentCount,
            'average_order_value' => $currentCount > 0 ? $currentRevenue / $currentCount : 0,
            'revenue_growth' => $this->calculateGrowthPercentage($currentRevenue, $previousRevenue),
            'invoice_growth' => $this->calculateGrowthPercentage($currentCount, $previousCount),
            'paid_amount' => $currentPeriod->sum('paid_amount'),
            'pending_amount' => $currentPeriod->sum('balance_amount'),
        ];
    }

    /**
     * Get estimates summary
     */
    private function getEstimatesSummary($startDate, $endDate)
    {
        $estimates = Estimate::whereBetween('estimate_date', [$startDate, $endDate]);

        return [
            'total_estimates' => $estimates->count(),
            'pending_estimates' => $estimates->where('status', 'pending')->count(),
            'approved_estimates' => $estimates->where('status', 'approved')->count(),
            'converted_estimates' => $estimates->where('status', 'converted')->count(),
            'total_value' => $estimates->sum('total_amount'),
            'conversion_rate' => $this->calculateConversionRate($startDate, $endDate),
            'average_estimate_value' => $estimates->avg('total_amount') ?? 0,
        ];
    }

    /**
     * Get customers summary
     */
    private function getCustomersSummary($startDate, $endDate)
    {
        $newCustomers = Customer::whereBetween('created_at', [$startDate, $endDate])->count();
        $activeCustomers = Customer::whereHas('invoices', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('invoice_date', [$startDate, $endDate]);
        })->count();

        return [
            'total_customers' => Customer::count(),
            'new_customers' => $newCustomers,
            'active_customers' => $activeCustomers,
            'repeat_customers' => $this->getRepeatCustomers($startDate, $endDate),
            'customer_lifetime_value' => $this->getAverageCustomerLifetimeValue(),
        ];
    }

    /**
     * Get inventory summary
     */
    private function getInventorySummary()
    {
        $totalProducts = Product::active()->count();
        $totalValue = Inventory::join('products', 'inventory.product_id', '=', 'products.id')
            ->where('products.is_active', true)
            ->sum(DB::raw('inventory.quantity_available * inventory.cost_price'));

        $lowStockCount = Inventory::join('products', 'inventory.product_id', '=', 'products.id')
            ->where('products.is_active', true)
            ->whereRaw('inventory.quantity_available <= inventory.minimum_stock_level')
            ->count();

        return [
            'total_products' => $totalProducts,
            'total_inventory_value' => $totalValue,
            'low_stock_items' => $lowStockCount,
            'out_of_stock_items' => Inventory::where('quantity_available', 0)->count(),
            'total_quantity' => Inventory::sum('quantity_available'),
        ];
    }

    /**
     * Get sales report data
     */
    public function getSalesReport($filters)
    {
        $query = Invoice::with(['customer', 'location', 'items.product'])
            ->where('status', '!=', 'cancelled');

        if (isset($filters['date_from'])) {
            $query->whereDate('invoice_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('invoice_date', '<=', $filters['date_to']);
        }

        if (isset($filters['location_id']) && $filters['location_id']) {
            $query->where('location_id', $filters['location_id']);
        }

        $invoices = $query->orderBy('invoice_date', 'desc')->get();

        // Group by specified period
        $groupBy = $filters['group_by'] ?? 'day';
        $groupedData = $this->groupSalesData($invoices, $groupBy);

        return [
            'invoices' => $invoices,
            'grouped_data' => $groupedData,
            'summary' => [
                'total_revenue' => $invoices->sum('total_amount'),
                'total_invoices' => $invoices->count(),
                'average_order_value' => $invoices->avg('total_amount'),
                'total_gst' => $invoices->sum('total_gst'),
                'total_items_sold' => $invoices->sum(function ($invoice) {
                    return $invoice->items->sum('quantity');
                }),
            ],
        ];
    }

    /**
     * Get inventory report data
     */
    public function getInventoryReport($filters)
    {
        $query = Inventory::with(['product.category', 'location'])
            ->join('products', 'inventory.product_id', '=', 'products.id')
            ->where('products.is_active', true);

        if (isset($filters['location_id']) && $filters['location_id']) {
            $query->where('inventory.location_id', $filters['location_id']);
        }

        if (isset($filters['category_id']) && $filters['category_id']) {
            $query->where('products.category_id', $filters['category_id']);
        }

        if (isset($filters['low_stock']) && $filters['low_stock']) {
            $query->whereRaw('inventory.quantity_available <= inventory.minimum_stock_level');
        }

        $inventory = $query->select('inventory.*')->get();

        return [
            'inventory_items' => $inventory,
            'summary' => [
                'total_items' => $inventory->count(),
                'total_quantity' => $inventory->sum('quantity_available'),
                'total_value' => $inventory->sum(function ($item) {
                    return $item->quantity_available * $item->cost_price;
                }),
                'low_stock_count' => $inventory->filter(function ($item) {
                    return $item->quantity_available <= $item->reorder_level;
                })->count(),
                'out_of_stock_count' => $inventory->where('quantity_available', 0)->count(),
            ],
            'category_breakdown' => $this->getInventoryCategoryBreakdown($inventory),
            'location_breakdown' => $this->getInventoryLocationBreakdown($inventory),
        ];
    }

    /**
     * Get customer analytics
     */
    public function getCustomerAnalytics($filters)
    {
        $query = Customer::with(['invoices' => function ($q) use ($filters) {
            if (isset($filters['date_from'])) {
                $q->whereDate('invoice_date', '>=', $filters['date_from']);
            }
            if (isset($filters['date_to'])) {
                $q->whereDate('invoice_date', '<=', $filters['date_to']);
            }
        }]);

        $customers = $query->get();

        // Calculate customer metrics
        $customerMetrics = $customers->map(function ($customer) {
            $totalSpent = $customer->invoices->sum('total_amount');
            $totalOrders = $customer->invoices->count();
            $avgOrderValue = $totalOrders > 0 ? $totalSpent / $totalOrders : 0;
            $lastOrderDate = $customer->invoices->max('invoice_date');

            return [
                'customer' => $customer,
                'total_spent' => $totalSpent,
                'total_orders' => $totalOrders,
                'avg_order_value' => $avgOrderValue,
                'last_order_date' => $lastOrderDate,
                'segment' => $this->getCustomerSegment($totalSpent, $totalOrders),
            ];
        });

        return [
            'customers' => $customerMetrics,
            'segments' => $this->getCustomerSegments($customerMetrics),
            'summary' => [
                'total_customers' => $customers->count(),
                'active_customers' => $customerMetrics->where('total_orders', '>', 0)->count(),
                'total_revenue' => $customerMetrics->sum('total_spent'),
                'avg_customer_value' => $customerMetrics->avg('total_spent'),
                'avg_orders_per_customer' => $customerMetrics->avg('total_orders'),
            ],
        ];
    }

    /**
     * Get GST report data
     */
    public function getGSTReport($filters)
    {
        $query = Invoice::where('status', '!=', 'cancelled');

        if (isset($filters['date_from'])) {
            $query->whereDate('invoice_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('invoice_date', '<=', $filters['date_to']);
        }

        if (isset($filters['location_id']) && $filters['location_id']) {
            $query->where('location_id', $filters['location_id']);
        }

        $invoices = $query->get();

        return [
            'invoices' => $invoices,
            'summary' => [
                'total_taxable_amount' => $invoices->sum(function ($invoice) {
                    return $invoice->subtotal - $invoice->discount_amount;
                }),
                'total_cgst' => $invoices->sum('cgst_amount'),
                'total_sgst' => $invoices->sum('sgst_amount'),
                'total_igst' => $invoices->sum('igst_amount'),
                'total_gst' => $invoices->sum('total_gst'),
                'total_invoice_value' => $invoices->sum('total_amount'),
            ],
            'gst_breakdown' => $this->getGSTBreakdown($invoices),
            'monthly_gst' => $this->getMonthlyGSTData($invoices),
        ];
    }

    /**
     * Get profit analysis
     */
    public function getProfitAnalysis($filters)
    {
        $query = Invoice::with(['items.product'])
            ->where('status', '!=', 'cancelled');

        if (isset($filters['date_from'])) {
            $query->whereDate('invoice_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('invoice_date', '<=', $filters['date_to']);
        }

        if (isset($filters['location_id']) && $filters['location_id']) {
            $query->where('location_id', $filters['location_id']);
        }

        $invoices = $query->get();

        $profitData = $invoices->map(function ($invoice) {
            $totalCost = 0;
            $totalRevenue = $invoice->total_amount;

            foreach ($invoice->items as $item) {
                if ($item->product) {
                    $inventory = $item->product->inventory()
                        ->where('location_id', $invoice->location_id)
                        ->first();
                    
                    if ($inventory) {
                        $totalCost += $inventory->cost_price * $item->quantity;
                    }
                }
            }

            $profit = $totalRevenue - $totalCost;
            $profitMargin = $totalRevenue > 0 ? ($profit / $totalRevenue) * 100 : 0;

            return [
                'invoice' => $invoice,
                'revenue' => $totalRevenue,
                'cost' => $totalCost,
                'profit' => $profit,
                'profit_margin' => $profitMargin,
            ];
        });

        return [
            'profit_data' => $profitData,
            'summary' => [
                'total_revenue' => $profitData->sum('revenue'),
                'total_cost' => $profitData->sum('cost'),
                'total_profit' => $profitData->sum('profit'),
                'avg_profit_margin' => $profitData->avg('profit_margin'),
                'profitable_invoices' => $profitData->where('profit', '>', 0)->count(),
                'loss_making_invoices' => $profitData->where('profit', '<', 0)->count(),
            ],
        ];
    }

    /**
     * Calculate growth percentage
     */
    private function calculateGrowthPercentage($current, $previous)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return (($current - $previous) / $previous) * 100;
    }

    /**
     * Calculate conversion rate
     */
    private function calculateConversionRate($startDate, $endDate)
    {
        $totalEstimates = Estimate::whereBetween('estimate_date', [$startDate, $endDate])->count();
        $convertedEstimates = Estimate::whereBetween('estimate_date', [$startDate, $endDate])
            ->where('status', 'converted')->count();

        return $totalEstimates > 0 ? ($convertedEstimates / $totalEstimates) * 100 : 0;
    }

    /**
     * Get repeat customers count
     */
    private function getRepeatCustomers($startDate, $endDate)
    {
        return Customer::whereHas('invoices', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('invoice_date', [$startDate, $endDate]);
        }, '>=', 2)->count();
    }

    /**
     * Get average customer lifetime value
     */
    private function getAverageCustomerLifetimeValue()
    {
        $customerCount = Customer::count();

        if ($customerCount === 0) {
            return 0;
        }

        return Customer::withSum('invoices', 'total_amount')->avg('invoices_sum_total_amount') ?? 0;
    }

    /**
     * Group sales data by period
     */
    private function groupSalesData($invoices, $groupBy)
    {
        return $invoices->groupBy(function ($invoice) use ($groupBy) {
            switch ($groupBy) {
                case 'hour':
                    return $invoice->invoice_date->format('Y-m-d H:00');
                case 'day':
                    return $invoice->invoice_date->format('Y-m-d');
                case 'week':
                    return $invoice->invoice_date->startOfWeek()->format('Y-m-d');
                case 'month':
                    return $invoice->invoice_date->format('Y-m');
                case 'year':
                    return $invoice->invoice_date->format('Y');
                default:
                    return $invoice->invoice_date->format('Y-m-d');
            }
        })->map(function ($group) {
            return [
                'count' => $group->count(),
                'total_amount' => $group->sum('total_amount'),
                'avg_amount' => $group->avg('total_amount'),
            ];
        });
    }

    /**
     * Get top performing products
     */
    private function getTopProducts($startDate, $endDate, $limit = 10)
    {
        return DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->join('products', 'invoice_items.product_id', '=', 'products.id')
            ->whereBetween('invoices.invoice_date', [$startDate, $endDate])
            ->where('invoices.status', '!=', 'cancelled')
            ->select(
                'products.name',
                'products.sku',
                DB::raw('SUM(invoice_items.quantity) as total_quantity'),
                DB::raw('SUM(invoice_items.total_amount) as total_revenue'),
                DB::raw('COUNT(DISTINCT invoices.id) as invoice_count')
            )
            ->groupBy('products.id', 'products.name', 'products.sku')
            ->orderBy('total_revenue', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities($limit = 10)
    {
        $activities = collect();

        // Recent invoices
        $recentInvoices = Invoice::with('customer')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($invoice) {
                return [
                    'type' => 'invoice',
                    'description' => "Invoice {$invoice->invoice_number} created for {$invoice->customer->first_name} {$invoice->customer->last_name}",
                    'amount' => $invoice->total_amount,
                    'created_at' => $invoice->created_at,
                ];
            });

        // Recent estimates
        $recentEstimates = Estimate::with('customer')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($estimate) {
                return [
                    'type' => 'estimate',
                    'description' => "Estimate {$estimate->estimate_number} created for {$estimate->customer->first_name} {$estimate->customer->last_name}",
                    'amount' => $estimate->total_amount,
                    'created_at' => $estimate->created_at,
                ];
            });

        return $activities->merge($recentInvoices)
            ->merge($recentEstimates)
            ->sortByDesc('created_at')
            ->take($limit)
            ->values();
    }

    /**
     * Get customer segment
     */
    private function getCustomerSegment($totalSpent, $totalOrders)
    {
        if ($totalSpent >= 100000) {
            return 'VIP';
        } elseif ($totalSpent >= 50000) {
            return 'Premium';
        } elseif ($totalOrders >= 5) {
            return 'Regular';
        } elseif ($totalOrders >= 1) {
            return 'New';
        } else {
            return 'Inactive';
        }
    }

    /**
     * Get customer segments breakdown
     */
    private function getCustomerSegments($customerMetrics)
    {
        return $customerMetrics->groupBy('segment')->map(function ($group, $segment) {
            return [
                'segment' => $segment,
                'count' => $group->count(),
                'total_revenue' => $group->sum('total_spent'),
                'avg_order_value' => $group->avg('avg_order_value'),
            ];
        })->values();
    }

    /**
     * Get inventory category breakdown
     */
    private function getInventoryCategoryBreakdown($inventory)
    {
        return $inventory->groupBy('product.category.name')->map(function ($group, $category) {
            return [
                'category' => $category ?: 'Uncategorized',
                'count' => $group->count(),
                'total_quantity' => $group->sum('quantity_available'),
                'total_value' => $group->sum(function ($item) {
                    return $item->quantity_available * $item->cost_price;
                }),
            ];
        })->values();
    }

    /**
     * Get inventory location breakdown
     */
    private function getInventoryLocationBreakdown($inventory)
    {
        return $inventory->groupBy('location.name')->map(function ($group, $location) {
            return [
                'location' => $location ?: 'Unknown',
                'count' => $group->count(),
                'total_quantity' => $group->sum('quantity_available'),
                'total_value' => $group->sum(function ($item) {
                    return $item->quantity_available * $item->cost_price;
                }),
            ];
        })->values();
    }

    /**
     * Get GST breakdown
     */
    private function getGSTBreakdown($invoices)
    {
        return [
            'intra_state' => [
                'count' => $invoices->where('cgst_amount', '>', 0)->count(),
                'cgst' => $invoices->sum('cgst_amount'),
                'sgst' => $invoices->sum('sgst_amount'),
                'total' => $invoices->sum('cgst_amount') + $invoices->sum('sgst_amount'),
            ],
            'inter_state' => [
                'count' => $invoices->where('igst_amount', '>', 0)->count(),
                'igst' => $invoices->sum('igst_amount'),
                'total' => $invoices->sum('igst_amount'),
            ],
        ];
    }

    /**
     * Get monthly GST data
     */
    private function getMonthlyGSTData($invoices)
    {
        return $invoices->groupBy(function ($invoice) {
            return $invoice->invoice_date->format('Y-m');
        })->map(function ($group, $month) {
            return [
                'month' => $month,
                'cgst' => $group->sum('cgst_amount'),
                'sgst' => $group->sum('sgst_amount'),
                'igst' => $group->sum('igst_amount'),
                'total_gst' => $group->sum('total_gst'),
                'taxable_amount' => $group->sum(function ($invoice) {
                    return $invoice->subtotal - $invoice->discount_amount;
                }),
            ];
        })->values();
    }

    /**
     * Get estimate analytics
     */
    public function getEstimateAnalytics($filters)
    {
        $query = Estimate::with(['customer', 'location']);

        if (isset($filters['date_from'])) {
            $query->whereDate('estimate_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('estimate_date', '<=', $filters['date_to']);
        }

        if (isset($filters['location_id']) && $filters['location_id']) {
            $query->where('location_id', $filters['location_id']);
        }

        $estimates = $query->get();

        return [
            'estimates' => $estimates,
            'summary' => [
                'total_estimates' => $estimates->count(),
                'pending_estimates' => $estimates->where('status', 'pending')->count(),
                'approved_estimates' => $estimates->where('status', 'approved')->count(),
                'converted_estimates' => $estimates->where('status', 'converted')->count(),
                'rejected_estimates' => $estimates->where('status', 'rejected')->count(),
                'total_value' => $estimates->sum('total_amount'),
                'conversion_rate' => $this->calculateEstimateConversionRate($estimates),
                'average_estimate_value' => $estimates->avg('total_amount') ?? 0,
            ],
            'status_breakdown' => $this->getEstimateStatusBreakdown($estimates),
            'monthly_trend' => $this->getEstimateMonthlyTrend($estimates),
        ];
    }

    /**
     * Get product performance data
     */
    public function getProductPerformance($filters)
    {
        $query = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->join('products', 'invoice_items.product_id', '=', 'products.id')
            ->leftJoin('categories', 'products.category_id', '=', 'categories.id')
            ->where('invoices.status', '!=', 'cancelled');

        if (isset($filters['date_from'])) {
            $query->whereDate('invoices.invoice_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('invoices.invoice_date', '<=', $filters['date_to']);
        }

        if (isset($filters['location_id']) && $filters['location_id']) {
            $query->where('invoices.location_id', $filters['location_id']);
        }

        if (isset($filters['category_id']) && $filters['category_id']) {
            $query->where('products.category_id', $filters['category_id']);
        }

        $sortBy = $filters['sort_by'] ?? 'revenue';
        $sortColumn = match($sortBy) {
            'quantity' => 'total_quantity',
            'orders' => 'order_count',
            'profit' => 'total_profit',
            default => 'total_revenue'
        };

        $products = $query->select(
            'products.id',
            'products.name',
            'products.sku',
            'categories.name as category_name',
            DB::raw('SUM(invoice_items.quantity) as total_quantity'),
            DB::raw('SUM(invoice_items.total_amount) as total_revenue'),
            DB::raw('COUNT(DISTINCT invoices.id) as order_count'),
            DB::raw('AVG(invoice_items.unit_price) as avg_price')
        )
        ->groupBy('products.id', 'products.name', 'products.sku', 'categories.name')
        ->orderBy($sortColumn, 'desc')
        ->get();

        return [
            'products' => $products,
            'summary' => [
                'total_products_sold' => $products->count(),
                'total_quantity_sold' => $products->sum('total_quantity'),
                'total_revenue' => $products->sum('total_revenue'),
                'avg_price' => $products->avg('avg_price'),
            ],
        ];
    }

    /**
     * Calculate estimate conversion rate
     */
    private function calculateEstimateConversionRate($estimates)
    {
        $totalEstimates = $estimates->count();
        $convertedEstimates = $estimates->where('status', 'converted')->count();

        return $totalEstimates > 0 ? ($convertedEstimates / $totalEstimates) * 100 : 0;
    }

    /**
     * Get estimate status breakdown
     */
    private function getEstimateStatusBreakdown($estimates)
    {
        return $estimates->groupBy('status')->map(function ($group, $status) {
            return [
                'status' => $status,
                'count' => $group->count(),
                'total_value' => $group->sum('total_amount'),
                'percentage' => ($group->count() / $estimates->count()) * 100,
            ];
        })->values();
    }

    /**
     * Get estimate monthly trend
     */
    private function getEstimateMonthlyTrend($estimates)
    {
        return $estimates->groupBy(function ($estimate) {
            return $estimate->estimate_date->format('Y-m');
        })->map(function ($group, $month) {
            return [
                'month' => $month,
                'count' => $group->count(),
                'total_value' => $group->sum('total_amount'),
                'converted_count' => $group->where('status', 'converted')->count(),
                'conversion_rate' => $group->count() > 0 ? ($group->where('status', 'converted')->count() / $group->count()) * 100 : 0,
            ];
        })->values();
    }

    /**
     * Get sales trend data for charts
     */
    public function getSalesTrendData($filters)
    {
        $salesData = $this->getSalesReport($filters);
        $groupedData = $salesData['grouped_data'];

        return [
            'labels' => $groupedData->keys()->toArray(),
            'values' => $groupedData->pluck('total_amount')->toArray(),
        ];
    }

    /**
     * Get category sales data for charts
     */
    public function getCategorySalesData($filters)
    {
        $query = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->join('products', 'invoice_items.product_id', '=', 'products.id')
            ->leftJoin('categories', 'products.category_id', '=', 'categories.id')
            ->where('invoices.status', '!=', 'cancelled');

        if (isset($filters['date_from'])) {
            $query->whereDate('invoices.invoice_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('invoices.invoice_date', '<=', $filters['date_to']);
        }

        $categoryData = $query->select(
            DB::raw('COALESCE(categories.name, "Uncategorized") as category'),
            DB::raw('SUM(invoice_items.total_amount) as total_amount')
        )
        ->groupBy('categories.name')
        ->orderBy('total_amount', 'desc')
        ->get();

        return [
            'labels' => $categoryData->pluck('category')->toArray(),
            'values' => $categoryData->pluck('total_amount')->toArray(),
        ];
    }

    /**
     * Get customer segment data for charts
     */
    public function getCustomerSegmentData($filters)
    {
        $customerData = $this->getCustomerAnalytics($filters);
        $segments = $customerData['segments'];

        return [
            'labels' => $segments->pluck('segment')->toArray(),
            'values' => $segments->pluck('total_revenue')->toArray(),
        ];
    }

    /**
     * Get inventory value data for charts
     */
    public function getInventoryValueData($filters)
    {
        $inventoryData = $this->getInventoryReport($filters);
        $categoryBreakdown = $inventoryData['category_breakdown'];

        return [
            'labels' => $categoryBreakdown->pluck('category')->toArray(),
            'values' => $categoryBreakdown->pluck('total_value')->toArray(),
        ];
    }

    /**
     * Get profit trend data for charts
     */
    public function getProfitTrendData($filters)
    {
        $profitData = $this->getProfitAnalysis($filters);

        $monthlyProfit = $profitData['profit_data']->groupBy(function ($item) {
            return $item['invoice']->invoice_date->format('Y-m');
        })->map(function ($group, $month) {
            return [
                'month' => $month,
                'profit' => $group->sum('profit'),
                'revenue' => $group->sum('revenue'),
                'margin' => $group->avg('profit_margin'),
            ];
        });

        return [
            'labels' => $monthlyProfit->keys()->toArray(),
            'values' => $monthlyProfit->pluck('profit')->toArray(),
        ];
    }

    /**
     * Get real-time dashboard data
     */
    public function getRealTimeDashboardData($period)
    {
        $dateRange = $this->getPeriodDateRange($period);

        return [
            'sales' => $this->getSalesSummary($dateRange['start'], $dateRange['end']),
            'estimates' => $this->getEstimatesSummary($dateRange['start'], $dateRange['end']),
            'customers' => $this->getCustomersSummary($dateRange['start'], $dateRange['end']),
            'inventory' => $this->getInventorySummary(),
        ];
    }

    /**
     * Get period date range
     */
    private function getPeriodDateRange($period)
    {
        $now = Carbon::now();

        return match($period) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay()
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek()
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth()
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter()
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear()
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth()
            ]
        };
    }

    /**
     * Export sales report to CSV
     */
    public function exportSalesReport($filters)
    {
        $salesData = $this->getSalesReport($filters);

        $filename = 'sales_report_' . date('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($salesData) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Invoice Number', 'Customer Name', 'Customer Phone', 'Date',
                'Location', 'Items Count', 'Subtotal', 'Discount', 'CGST',
                'SGST', 'IGST', 'Total GST', 'Total Amount', 'Status'
            ]);

            // CSV data
            foreach ($salesData['invoices'] as $invoice) {
                fputcsv($file, [
                    $invoice->invoice_number,
                    $invoice->customer->first_name . ' ' . $invoice->customer->last_name,
                    $invoice->customer->phone,
                    $invoice->invoice_date->format('Y-m-d'),
                    $invoice->location->name ?? '',
                    $invoice->items->count(),
                    $invoice->subtotal,
                    $invoice->discount_amount,
                    $invoice->cgst_amount,
                    $invoice->sgst_amount,
                    $invoice->igst_amount,
                    $invoice->total_gst ?? ($invoice->cgst_amount + $invoice->sgst_amount + $invoice->igst_amount),
                    $invoice->total_amount,
                    $invoice->status,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export inventory report to CSV
     */
    public function exportInventoryReport($filters)
    {
        $inventoryData = $this->getInventoryReport($filters);

        $filename = 'inventory_report_' . date('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($inventoryData) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Product Name', 'SKU', 'Category', 'Location', 'Available Quantity',
                'Reorder Level', 'Cost Price', 'Selling Price', 'Total Value', 'Status'
            ]);

            // CSV data
            foreach ($inventoryData['inventory_items'] as $item) {
                $status = 'In Stock';
                if ($item->quantity_available == 0) {
                    $status = 'Out of Stock';
                } elseif ($item->quantity_available <= $item->reorder_level) {
                    $status = 'Low Stock';
                }

                fputcsv($file, [
                    $item->product->name,
                    $item->product->sku,
                    $item->product->category->name ?? 'Uncategorized',
                    $item->location->name,
                    $item->quantity_available,
                    $item->reorder_level,
                    $item->cost_price,
                    $item->selling_price,
                    $item->quantity_available * $item->cost_price,
                    $status,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export GST report to CSV
     */
    public function exportGSTReport($filters)
    {
        $gstData = $this->getGSTReport($filters);

        $filename = 'gst_report_' . date('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($gstData) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Invoice Number', 'Date', 'Customer Name', 'Customer GSTIN',
                'Taxable Amount', 'CGST', 'SGST', 'IGST', 'Total GST', 'Invoice Total'
            ]);

            // CSV data
            foreach ($gstData['invoices'] as $invoice) {
                fputcsv($file, [
                    $invoice->invoice_number,
                    $invoice->invoice_date->format('Y-m-d'),
                    $invoice->customer->first_name . ' ' . $invoice->customer->last_name,
                    $invoice->customer->gstin ?? '',
                    $invoice->subtotal - $invoice->discount_amount,
                    $invoice->cgst_amount,
                    $invoice->sgst_amount,
                    $invoice->igst_amount,
                    $invoice->total_gst ?? ($invoice->cgst_amount + $invoice->sgst_amount + $invoice->igst_amount),
                    $invoice->total_amount,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
