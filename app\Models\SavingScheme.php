<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;
use App\Traits\GeneratesUniqueFields;

class SavingScheme extends Model
{
    use HasFactory, SoftDeletes, GeneratesUniqueFields;

    protected $fillable = [
        'scheme_number',
        'customer_id',
        'plan_id',
        'scheme_name',
        'scheme_type',
        'monthly_amount',
        'duration_months',
        'start_date',
        'maturity_date',
        'installment_amount',
        'total_target_amount',
        'total_paid_amount',
        'interest_earned',
        'bonus_amount',
        'penalty_amount',
        'current_value',
        'status',
        'last_payment_date',
        'next_due_date',
        'payments_completed',
        'payments_missed',
        'closure_reason',
        'closure_date',
        'closed_by',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'maturity_date' => 'date',
        'last_payment_date' => 'date',
        'next_due_date' => 'date',
        'closure_date' => 'date',
        'installment_amount' => 'decimal:2',
        'total_target_amount' => 'decimal:2',
        'total_paid_amount' => 'decimal:2',
        'interest_earned' => 'decimal:2',
        'bonus_amount' => 'decimal:2',
        'penalty_amount' => 'decimal:2',
        'current_value' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($scheme) {
            if (!$scheme->scheme_number) {
                $scheme->scheme_number = static::generateSchemeNumber();
            }
            if (!$scheme->created_by) {
                $scheme->created_by = auth()->id() ?: 1; // Default to user ID 1 if no auth
            }
        });
    }

    /**
     * Relationships
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(SavingSchemePlan::class, 'plan_id');
    }

    public function payments(): HasMany
    {
        return $this->hasMany(SchemePayment::class, 'scheme_id');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(SchemeTransaction::class, 'scheme_id');
    }

    public function maturity(): HasOne
    {
        return $this->hasOne(SchemeMaturity::class, 'scheme_id');
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(SchemeNotification::class, 'scheme_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function closedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'closed_by');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeMatured($query)
    {
        return $query->where('status', 'matured');
    }

    public function scopeDueForPayment($query)
    {
        return $query->where('status', 'active')
                    ->where('next_due_date', '<=', now()->toDateString());
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'active')
                    ->where('next_due_date', '<', now()->toDateString());
    }

    public function scopeMaturityApproaching($query, $days = 30)
    {
        return $query->where('status', 'active')
                    ->whereBetween('maturity_date', [
                        now()->toDateString(),
                        now()->addDays($days)->toDateString()
                    ]);
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Accessors
     */
    public function getFormattedInstallmentAmountAttribute(): string
    {
        return '₹' . number_format($this->installment_amount, 2);
    }

    public function getFormattedTotalTargetAmountAttribute(): string
    {
        return '₹' . number_format($this->total_target_amount, 2);
    }

    public function getFormattedCurrentValueAttribute(): string
    {
        return '₹' . number_format($this->current_value, 2);
    }

    public function getStatusDisplayAttribute(): string
    {
        $statuses = [
            'active' => 'Active',
            'matured' => 'Matured',
            'closed' => 'Closed',
            'suspended' => 'Suspended',
            'defaulted' => 'Defaulted',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute(): string
    {
        $colors = [
            'active' => 'success',
            'matured' => 'primary',
            'closed' => 'secondary',
            'suspended' => 'warning',
            'defaulted' => 'danger',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    public function getCompletionPercentageAttribute(): float
    {
        if ($this->total_target_amount <= 0) {
            return 0;
        }

        return min(100, ($this->total_paid_amount / $this->total_target_amount) * 100);
    }

    public function getDaysToMaturityAttribute(): int
    {
        return now()->diffInDays($this->maturity_date, false);
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'active' && $this->next_due_date < now()->toDateString();
    }

    public function getCanMakePaymentAttribute(): bool
    {
        return in_array($this->status, ['active']);
    }

    public function getCanCloseAttribute(): bool
    {
        return in_array($this->status, ['active', 'suspended']);
    }

    public function getCanSuspendAttribute(): bool
    {
        return $this->status === 'active';
    }

    public function getCanReactivateAttribute(): bool
    {
        return $this->status === 'suspended';
    }

    /**
     * Business Logic Methods
     */
    public static function generateSchemeNumber(): string
    {
        $maxAttempts = 10;
        $attempt = 0;

        while ($attempt < $maxAttempts) {
            $prefix = 'SS';
            $year = now()->format('Y');
            $month = now()->format('m');

            // Use microseconds for better uniqueness
            $microtime = microtime(true);
            $timestamp = date('His', $microtime);
            $microseconds = sprintf('%03d', ($microtime - floor($microtime)) * 1000);

            // Add random component
            $random = rand(100, 999);

            $number = $prefix . $year . $month . $timestamp . $microseconds . $random;

            // Check if this number already exists
            if (!static::where('scheme_number', $number)->exists()) {
                return $number;
            }

            $attempt++;
            // Small delay to avoid rapid collisions
            usleep(1000); // 1ms
        }

        // Fallback: use UUID-based approach if all attempts failed
        $uuid = str_replace('-', '', \Illuminate\Support\Str::uuid());
        $prefix = 'SS';
        $year = now()->format('Y');
        $month = now()->format('m');

        return $prefix . $year . $month . substr($uuid, 0, 10);
    }

    public function calculateNextDueDate(): Carbon
    {
        $frequency = $this->plan->getInstallmentFrequency();

        if ($this->last_payment_date) {
            return Carbon::parse($this->last_payment_date)->addMonths($frequency);
        }

        return Carbon::parse($this->start_date)->addMonths($frequency);
    }

    public function makePayment($amount, $paymentMethod = 'cash', $paymentReference = null, $notes = null): SchemePayment
    {
        if (!$this->can_make_payment) {
            throw new \Exception('Cannot make payment for this scheme');
        }

        $dueDate = $this->next_due_date ?: $this->calculateNextDueDate();
        $lateFee = 0;

        // Calculate late fee if payment is overdue
        if (now()->toDateString() > $dueDate->toDateString()) {
            $daysLate = now()->diffInDays($dueDate);
            if ($daysLate > $this->plan->grace_period_days) {
                $lateFee = $this->plan->late_fee_amount;
            }
        }

        $totalAmount = $amount + $lateFee;
        $installmentNumber = $this->payments_completed + 1;

        // Create payment record
        $payment = SchemePayment::create([
            'scheme_id' => $this->id,
            'payment_date' => now()->toDateString(),
            'due_date' => $dueDate,
            'amount' => $amount,
            'late_fee' => $lateFee,
            'total_amount' => $totalAmount,
            'payment_method' => $paymentMethod,
            'payment_reference' => $paymentReference,
            'status' => 'paid',
            'installment_number' => $installmentNumber,
            'notes' => $notes,
            'received_by' => auth()->id(),
            'paid_at' => now(),
        ]);

        // Update scheme
        $this->update([
            'total_paid_amount' => $this->total_paid_amount + $totalAmount,
            'current_value' => $this->current_value + $amount,
            'penalty_amount' => $this->penalty_amount + $lateFee,
            'last_payment_date' => now()->toDateString(),
            'next_due_date' => $this->calculateNextDueDate(),
            'payments_completed' => $this->payments_completed + 1,
        ]);

        // Check if scheme is completed
        if ($this->payments_completed >= $this->plan->getTotalInstallments()) {
            $this->markAsMatured();
        }

        return $payment;
    }

    public function markAsMatured(): self
    {
        $maturityCalculation = $this->plan->calculateMaturityAmount($this->total_paid_amount);

        $this->update([
            'status' => 'matured',
            'interest_earned' => $maturityCalculation['interest_amount'],
            'bonus_amount' => $maturityCalculation['bonus_amount'],
            'current_value' => $maturityCalculation['total_amount'],
        ]);

        // Create maturity record
        SchemeMaturity::create([
            'scheme_id' => $this->id,
            'maturity_date' => $this->maturity_date,
            'maturity_amount' => $maturityCalculation['total_amount'],
            'principal_amount' => $maturityCalculation['principal_amount'],
            'interest_amount' => $maturityCalculation['interest_amount'],
            'bonus_amount' => $maturityCalculation['bonus_amount'],
            'action_taken' => 'pending',
        ]);

        return $this;
    }

    public function suspend($reason = null): self
    {
        if (!$this->can_suspend) {
            throw new \Exception('Cannot suspend this scheme');
        }

        $this->update([
            'status' => 'suspended',
            'notes' => $reason ? ($this->notes . "\n\nSuspended: " . $reason) : $this->notes,
        ]);

        return $this;
    }

    public function reactivate(): self
    {
        if (!$this->can_reactivate) {
            throw new \Exception('Cannot reactivate this scheme');
        }

        $this->update([
            'status' => 'active',
            'next_due_date' => $this->calculateNextDueDate(),
        ]);

        return $this;
    }

    public function close($reason = null): self
    {
        if (!$this->can_close) {
            throw new \Exception('Cannot close this scheme');
        }

        $this->update([
            'status' => 'closed',
            'closure_reason' => $reason,
            'closure_date' => now()->toDateString(),
            'closed_by' => auth()->id(),
        ]);

        return $this;
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'scheme_number' => $this->scheme_number,
            'scheme_name' => $this->scheme_name ?: $this->plan->plan_name,
            'customer_name' => $this->customer->full_name,
            'plan_name' => $this->plan->plan_name,
            'start_date' => $this->start_date->format('Y-m-d'),
            'maturity_date' => $this->maturity_date->format('Y-m-d'),
            'installment_amount' => $this->formatted_installment_amount,
            'total_target_amount' => $this->formatted_total_target_amount,
            'current_value' => $this->formatted_current_value,
            'completion_percentage' => $this->completion_percentage,
            'status' => $this->status_display,
            'status_color' => $this->status_color,
            'payments_completed' => $this->payments_completed,
            'payments_missed' => $this->payments_missed,
            'days_to_maturity' => $this->days_to_maturity,
            'is_overdue' => $this->is_overdue,
            'next_due_date' => $this->next_due_date?->format('Y-m-d'),
            'last_payment_date' => $this->last_payment_date?->format('Y-m-d'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
