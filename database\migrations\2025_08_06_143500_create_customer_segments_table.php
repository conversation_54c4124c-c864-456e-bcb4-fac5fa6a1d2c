<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_segments', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('code', 20)->unique();
            $table->text('description')->nullable();
            $table->string('color', 7)->default('#3B82F6'); // Hex color for UI
            
            // Segmentation criteria
            $table->json('criteria')->nullable(); // JSON criteria for automatic segmentation
            $table->boolean('is_automatic')->default(true); // Auto-assign based on criteria
            $table->boolean('is_active')->default(true);
            
            // Segment characteristics
            $table->decimal('min_purchase_value', 12, 2)->nullable();
            $table->decimal('max_purchase_value', 12, 2)->nullable();
            $table->integer('min_orders')->nullable();
            $table->integer('max_orders')->nullable();
            $table->integer('min_days_since_last_purchase')->nullable();
            $table->integer('max_days_since_last_purchase')->nullable();
            
            // Marketing preferences for segment
            $table->json('marketing_preferences')->nullable();
            $table->json('communication_templates')->nullable();
            $table->json('discount_eligibility')->nullable();
            
            // Analytics
            $table->integer('customer_count')->default(0);
            $table->decimal('total_segment_value', 15, 2)->default(0);
            $table->decimal('average_customer_value', 12, 2)->default(0);
            
            // Audit fields
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('last_calculated_at')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['is_active']);
            $table->index(['is_automatic']);
            $table->index(['customer_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_segments');
    }
};
