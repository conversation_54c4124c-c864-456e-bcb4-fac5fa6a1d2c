<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CustomerAnalytics extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'total_orders',
        'total_purchase_value',
        'average_order_value',
        'highest_order_value',
        'lowest_order_value',
        'days_since_last_purchase',
        'purchase_frequency_days',
        'top_categories',
        'top_metals',
        'top_product_types',
        'preferred_price_range',
        'total_visits',
        'total_inquiries',
        'conversion_rate',
        'average_decision_time_days',
        'customer_lifecycle_stage',
        'email_opens',
        'email_clicks',
        'sms_responses',
        'whatsapp_interactions',
        'engagement_score',
        'loyalty_points',
        'referrals_made',
        'referrals_converted',
        'is_repeat_customer',
        'months_as_customer',
        'credit_utilization_percentage',
        'payment_delays',
        'payment_delay_average_days',
        'payment_behavior',
        'risk_level',
        'seasonal_purchase_pattern',
        'festival_purchase_pattern',
        'peak_shopping_month',
        'peak_shopping_season',
        'customer_lifetime_value',
        'predicted_next_purchase_value',
        'predicted_next_purchase_date',
        'value_segment',
        'average_rating',
        'complaints_count',
        'compliments_count',
        'satisfaction_score',
        'last_calculated_at',
        'next_calculation_due',
    ];

    protected $casts = [
        'total_purchase_value' => 'decimal:2',
        'average_order_value' => 'decimal:2',
        'highest_order_value' => 'decimal:2',
        'lowest_order_value' => 'decimal:2',
        'conversion_rate' => 'decimal:2',
        'engagement_score' => 'decimal:2',
        'credit_utilization_percentage' => 'decimal:2',
        'payment_delay_average_days' => 'decimal:2',
        'customer_lifetime_value' => 'decimal:2',
        'predicted_next_purchase_value' => 'decimal:2',
        'average_rating' => 'decimal:2',
        'satisfaction_score' => 'decimal:2',
        'top_categories' => 'array',
        'top_metals' => 'array',
        'top_product_types' => 'array',
        'seasonal_purchase_pattern' => 'array',
        'festival_purchase_pattern' => 'array',
        'is_repeat_customer' => 'boolean',
        'predicted_next_purchase_date' => 'date',
        'last_calculated_at' => 'datetime',
        'next_calculation_due' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Scopes
     */
    public function scopeHighValue($query)
    {
        return $query->where('value_segment', 'high');
    }

    public function scopeVip($query)
    {
        return $query->where('value_segment', 'vip');
    }

    public function scopeAtRisk($query)
    {
        return $query->where('customer_lifecycle_stage', 'at_risk');
    }

    public function scopeDormant($query)
    {
        return $query->where('customer_lifecycle_stage', 'dormant');
    }

    public function scopeHighRisk($query)
    {
        return $query->where('risk_level', 'high');
    }

    public function scopeNeedsCalculation($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('last_calculated_at')
              ->orWhere('next_calculation_due', '<=', now());
        });
    }

    /**
     * Accessors
     */
    public function getLifecycleStageDisplayAttribute()
    {
        $stages = [
            'new' => 'New Customer',
            'active' => 'Active Customer',
            'at_risk' => 'At Risk',
            'dormant' => 'Dormant',
            'lost' => 'Lost Customer',
        ];

        return $stages[$this->customer_lifecycle_stage] ?? 'Unknown';
    }

    public function getValueSegmentDisplayAttribute()
    {
        $segments = [
            'low' => 'Low Value',
            'medium' => 'Medium Value',
            'high' => 'High Value',
            'vip' => 'VIP Customer',
        ];

        return $segments[$this->value_segment] ?? 'Unknown';
    }

    public function getRiskLevelDisplayAttribute()
    {
        $levels = [
            'low' => 'Low Risk',
            'medium' => 'Medium Risk',
            'high' => 'High Risk',
        ];

        return $levels[$this->risk_level] ?? 'Unknown';
    }

    public function getPaymentBehaviorDisplayAttribute()
    {
        $behaviors = [
            'excellent' => 'Excellent',
            'good' => 'Good',
            'fair' => 'Fair',
            'poor' => 'Poor',
        ];

        return $behaviors[$this->payment_behavior] ?? 'Unknown';
    }

    public function getLifecycleStageColorAttribute()
    {
        $colors = [
            'new' => 'green',
            'active' => 'blue',
            'at_risk' => 'yellow',
            'dormant' => 'orange',
            'lost' => 'red',
        ];

        return $colors[$this->customer_lifecycle_stage] ?? 'gray';
    }

    public function getValueSegmentColorAttribute()
    {
        $colors = [
            'low' => 'gray',
            'medium' => 'blue',
            'high' => 'green',
            'vip' => 'purple',
        ];

        return $colors[$this->value_segment] ?? 'gray';
    }

    public function getRiskLevelColorAttribute()
    {
        $colors = [
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'red',
        ];

        return $colors[$this->risk_level] ?? 'gray';
    }

    public function getEngagementLevelAttribute()
    {
        if ($this->engagement_score >= 80) {
            return 'high';
        } elseif ($this->engagement_score >= 60) {
            return 'medium';
        } elseif ($this->engagement_score >= 40) {
            return 'low';
        } else {
            return 'very_low';
        }
    }

    public function getIsHighValueAttribute()
    {
        return in_array($this->value_segment, ['high', 'vip']);
    }

    public function getIsAtRiskAttribute()
    {
        return in_array($this->customer_lifecycle_stage, ['at_risk', 'dormant']);
    }

    public function getNextPurchaseProbabilityAttribute()
    {
        if (!$this->predicted_next_purchase_date) {
            return 0;
        }

        $daysUntilPredicted = now()->diffInDays($this->predicted_next_purchase_date, false);

        if ($daysUntilPredicted <= 0) {
            return 90; // High probability if date has passed
        } elseif ($daysUntilPredicted <= 30) {
            return 70; // High probability within 30 days
        } elseif ($daysUntilPredicted <= 60) {
            return 50; // Medium probability within 60 days
        } else {
            return 30; // Lower probability beyond 60 days
        }
    }

    public function getTopCategoriesDisplayAttribute()
    {
        if (!$this->top_categories) {
            return 'No purchases yet';
        }

        $categories = Category::whereIn('id', array_keys($this->top_categories))
                             ->pluck('name', 'id')
                             ->toArray();

        $display = [];
        foreach ($this->top_categories as $categoryId => $count) {
            if (isset($categories[$categoryId])) {
                $display[] = $categories[$categoryId] . " ({$count})";
            }
        }

        return implode(', ', array_slice($display, 0, 3));
    }

    public function getTopMetalsDisplayAttribute()
    {
        if (!$this->top_metals) {
            return 'No purchases yet';
        }

        $metals = Metal::whereIn('id', array_keys($this->top_metals))
                      ->pluck('name', 'id')
                      ->toArray();

        $display = [];
        foreach ($this->top_metals as $metalId => $count) {
            if (isset($metals[$metalId])) {
                $display[] = $metals[$metalId] . " ({$count})";
            }
        }

        return implode(', ', array_slice($display, 0, 3));
    }

    /**
     * Calculate customer analytics
     */
    public function calculateAnalytics()
    {
        $customer = $this->customer;

        // Get all invoices for this customer
        $invoices = $customer->invoices()->paid()->get();

        // Basic purchase analytics
        $this->total_orders = $invoices->count();
        $this->total_purchase_value = $invoices->sum('total_amount');
        $this->average_order_value = $this->total_orders > 0 ? $this->total_purchase_value / $this->total_orders : 0;
        $this->highest_order_value = $invoices->max('total_amount') ?? 0;
        $this->lowest_order_value = $invoices->min('total_amount') ?? 0;

        // Last purchase analysis
        $lastInvoice = $invoices->sortByDesc('invoice_date')->first();
        $this->days_since_last_purchase = $lastInvoice ?
            Carbon::parse($lastInvoice->invoice_date)->diffInDays(now()) : null;

        // Purchase frequency
        if ($this->total_orders > 1 && $lastInvoice) {
            $firstInvoice = $invoices->sortBy('invoice_date')->first();
            $totalDays = Carbon::parse($firstInvoice->invoice_date)->diffInDays($lastInvoice->invoice_date);
            $this->purchase_frequency_days = $totalDays / ($this->total_orders - 1);
        }

        // Customer age
        $this->months_as_customer = $customer->created_at->diffInMonths(now());

        // Repeat customer status
        $this->is_repeat_customer = $this->total_orders > 1;

        // Calculate lifecycle stage
        $this->customer_lifecycle_stage = $this->calculateLifecycleStage();

        // Calculate value segment
        $this->value_segment = $this->calculateValueSegment();

        // Calculate CLV
        $this->customer_lifetime_value = $this->calculateCustomerLifetimeValue();

        // Calculate engagement score
        $this->engagement_score = $this->calculateEngagementScore();

        // Calculate risk level
        $this->risk_level = $this->calculateRiskLevel();

        // Set calculation timestamps
        $this->last_calculated_at = now();
        $this->next_calculation_due = now()->addDays(7); // Recalculate weekly

        $this->save();

        return $this;
    }

    /**
     * Calculate lifecycle stage
     */
    private function calculateLifecycleStage()
    {
        if ($this->total_orders == 0) {
            return 'new';
        }

        if ($this->days_since_last_purchase === null) {
            return 'new';
        }

        if ($this->days_since_last_purchase <= 90) {
            return 'active';
        } elseif ($this->days_since_last_purchase <= 180) {
            return 'at_risk';
        } elseif ($this->days_since_last_purchase <= 365) {
            return 'dormant';
        } else {
            return 'lost';
        }
    }

    /**
     * Calculate value segment
     */
    private function calculateValueSegment()
    {
        if ($this->total_purchase_value >= 500000) { // 5 Lakhs
            return 'vip';
        } elseif ($this->total_purchase_value >= 200000) { // 2 Lakhs
            return 'high';
        } elseif ($this->total_purchase_value >= 50000) { // 50k
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Calculate Customer Lifetime Value
     */
    private function calculateCustomerLifetimeValue()
    {
        if ($this->months_as_customer <= 0 || $this->total_orders <= 0) {
            return $this->total_purchase_value;
        }

        $monthlyValue = $this->total_purchase_value / $this->months_as_customer;
        $predictedLifetimeMonths = 24; // Assume 2 years average customer lifetime

        return $monthlyValue * $predictedLifetimeMonths;
    }

    /**
     * Calculate engagement score
     */
    private function calculateEngagementScore()
    {
        $score = 0;

        // Purchase activity (40 points)
        if ($this->total_orders > 0) {
            $score += min(40, $this->total_orders * 5);
        }

        // Recency (30 points)
        if ($this->days_since_last_purchase !== null) {
            if ($this->days_since_last_purchase <= 30) {
                $score += 30;
            } elseif ($this->days_since_last_purchase <= 90) {
                $score += 20;
            } elseif ($this->days_since_last_purchase <= 180) {
                $score += 10;
            }
        }

        // Communication engagement (20 points)
        $communicationScore = ($this->email_opens * 0.5) + ($this->email_clicks * 2) +
                             ($this->sms_responses * 3) + ($this->whatsapp_interactions * 1);
        $score += min(20, $communicationScore);

        // Loyalty indicators (10 points)
        if ($this->referrals_made > 0) {
            $score += min(10, $this->referrals_made * 2);
        }

        return min(100, $score);
    }

    /**
     * Calculate risk level
     */
    private function calculateRiskLevel()
    {
        $riskFactors = 0;

        // Payment behavior
        if ($this->payment_behavior === 'poor') {
            $riskFactors += 3;
        } elseif ($this->payment_behavior === 'fair') {
            $riskFactors += 1;
        }

        // Payment delays
        if ($this->payment_delays > 3) {
            $riskFactors += 2;
        } elseif ($this->payment_delays > 1) {
            $riskFactors += 1;
        }

        // Lifecycle stage
        if ($this->customer_lifecycle_stage === 'dormant') {
            $riskFactors += 2;
        } elseif ($this->customer_lifecycle_stage === 'at_risk') {
            $riskFactors += 1;
        }

        // Complaints
        if ($this->complaints_count > 2) {
            $riskFactors += 1;
        }

        if ($riskFactors >= 4) {
            return 'high';
        } elseif ($riskFactors >= 2) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Check if analytics need recalculation
     */
    public function needsRecalculation()
    {
        return !$this->last_calculated_at ||
               ($this->next_calculation_due && $this->next_calculation_due <= now());
    }

    /**
     * Get analytics summary
     */
    public function getSummary()
    {
        return [
            'total_orders' => $this->total_orders,
            'total_value' => $this->total_purchase_value,
            'average_order' => $this->average_order_value,
            'lifecycle_stage' => $this->lifecycle_stage_display,
            'value_segment' => $this->value_segment_display,
            'engagement_level' => $this->engagement_level,
            'risk_level' => $this->risk_level_display,
            'clv' => $this->customer_lifetime_value,
            'days_since_last_purchase' => $this->days_since_last_purchase,
            'is_repeat_customer' => $this->is_repeat_customer,
        ];
    }
}
