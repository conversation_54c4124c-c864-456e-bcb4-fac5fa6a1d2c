<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\POSSession;
use App\Models\Location;
use App\Models\User;
use Illuminate\Http\Request;

class POSSessionController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_pos_sessions')->only(['index', 'show']);
        $this->middleware('permission:manage_pos_sessions')->only(['create', 'store', 'edit', 'update', 'destroy']);
    }

    /**
     * Display a listing of POS sessions
     */
    public function index(Request $request)
    {
        $query = POSSession::with(['user', 'location', 'closedBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('session_number', 'like', "%{$search}%")
                  ->orWhere('terminal_id', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($uq) use ($search) {
                      $uq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by location
        if ($request->filled('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('opened_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('opened_at', '<=', $request->date_to);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'opened_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $sessions = $query->paginate(20);

        // Get filter options
        $users = User::role(['cashier', 'admin'])->orderBy('name')->get(['id', 'name']);
        $locations = Location::orderBy('name')->get(['id', 'name']);
        $statuses = [
            'active' => 'Active',
            'closed' => 'Closed',
            'suspended' => 'Suspended',
        ];

        return view('admin.pos-sessions.index', compact('sessions', 'users', 'locations', 'statuses'));
    }

    /**
     * Display the specified POS session
     */
    public function show(POSSession $posSession)
    {
        $posSession->load([
            'user',
            'location',
            'closedBy',
            'transactions.customer',
            'transactions.items.product',
            'cashMovements.createdBy'
        ]);

        $sessionSummary = $posSession->getSessionSummary();
        $transactionsByHour = $posSession->getTransactionsByHour();
        $topProducts = $posSession->getTopSellingProducts();

        return view('admin.pos-sessions.show', compact(
            'posSession',
            'sessionSummary',
            'transactionsByHour',
            'topProducts'
        ));
    }

    /**
     * Force close a POS session
     */
    public function forceClose(Request $request, POSSession $posSession)
    {
        $validated = $request->validate([
            'closing_cash' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            if ($posSession->status !== 'active') {
                return back()->with('error', 'Only active sessions can be closed.');
            }

            $posSession->closeSession(
                $validated['closing_cash'],
                $validated['notes'] ?? 'Force closed by admin'
            );

            return back()->with('success', 'POS session force closed successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to close session: ' . $e->getMessage());
        }
    }

    /**
     * Suspend POS session
     */
    public function suspend(Request $request, POSSession $posSession)
    {
        $validated = $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $posSession->suspendSession($validated['reason']);

            return back()->with('success', 'POS session suspended successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to suspend session: ' . $e->getMessage());
        }
    }

    /**
     * Resume POS session
     */
    public function resume(POSSession $posSession)
    {
        try {
            $posSession->resumeSession();

            return back()->with('success', 'POS session resumed successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to resume session: ' . $e->getMessage());
        }
    }

    /**
     * Export sessions to CSV
     */
    public function export(Request $request)
    {
        $query = POSSession::with(['user', 'location']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('opened_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('opened_at', '<=', $request->date_to);
        }

        $sessions = $query->get();

        $filename = 'pos_sessions_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($sessions) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Session Number', 'User', 'Location', 'Terminal ID',
                'Opened At', 'Closed At', 'Duration', 'Status',
                'Opening Cash', 'Closing Cash', 'Expected Cash', 'Cash Difference',
                'Total Sales', 'Total Transactions'
            ]);

            // CSV data
            foreach ($sessions as $session) {
                fputcsv($file, [
                    $session->session_number,
                    $session->user->name,
                    $session->location->name,
                    $session->terminal_id,
                    $session->opened_at->format('Y-m-d H:i:s'),
                    $session->closed_at?->format('Y-m-d H:i:s'),
                    $session->duration,
                    $session->status_display,
                    $session->opening_cash,
                    $session->closing_cash,
                    $session->expected_cash,
                    $session->cash_difference,
                    $session->total_sales,
                    $session->total_transactions,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get session statistics
     */
    public function getStatistics(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = now()->subDays($days);

        $stats = [
            'total_sessions' => POSSession::where('opened_at', '>=', $startDate)->count(),
            'active_sessions' => POSSession::where('status', 'active')->count(),
            'total_sales' => POSSession::where('opened_at', '>=', $startDate)
                                    ->where('status', 'closed')
                                    ->sum('total_sales'),
            'average_session_value' => POSSession::where('opened_at', '>=', $startDate)
                                               ->where('status', 'closed')
                                               ->avg('total_sales'),
            'cash_variances' => POSSession::where('opened_at', '>=', $startDate)
                                        ->where('status', 'closed')
                                        ->whereNotNull('cash_difference')
                                        ->sum('cash_difference'),
            'sessions_by_location' => POSSession::with('location')
                                              ->where('opened_at', '>=', $startDate)
                                              ->groupBy('location_id')
                                              ->selectRaw('location_id, count(*) as count, sum(total_sales) as total_sales')
                                              ->get()
                                              ->map(function ($item) {
                                                  return [
                                                      'location' => $item->location->name,
                                                      'sessions' => $item->count,
                                                      'sales' => $item->total_sales,
                                                  ];
                                              }),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}
