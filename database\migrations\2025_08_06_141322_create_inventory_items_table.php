<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('location_id')->constrained()->onDelete('cascade');
            $table->string('unique_tag')->unique(); // Unique tag for each item
            $table->string('barcode')->unique()->nullable(); // Individual barcode
            $table->string('qr_code')->unique()->nullable(); // QR code for mobile scanning
            $table->string('huid_number')->unique()->nullable(); // HUID for gold items
            $table->string('certificate_number')->unique()->nullable(); // Certificate number

            // Physical properties
            $table->decimal('actual_weight', 10, 3)->nullable(); // Actual measured weight
            $table->decimal('stone_weight', 10, 3)->default(0); // Total stone weight
            $table->decimal('net_metal_weight', 10, 3)->nullable(); // Net metal weight
            $table->string('purity')->nullable(); // Metal purity (22K, 18K, etc.)
            $table->json('dimensions')->nullable(); // Length, width, height, diameter

            // Pricing
            $table->decimal('cost_price', 10, 2); // Individual cost price
            $table->decimal('making_charges', 10, 2)->default(0); // Making charges
            $table->decimal('stone_charges', 10, 2)->default(0); // Stone charges
            $table->decimal('other_charges', 10, 2)->default(0); // Other charges
            $table->decimal('total_cost', 10, 2); // Total cost
            $table->decimal('selling_price', 10, 2); // Selling price
            $table->decimal('mrp', 10, 2)->nullable(); // MRP

            // Status and condition
            $table->enum('status', ['available', 'reserved', 'sold', 'damaged', 'repair', 'display'])->default('available');
            $table->enum('condition', ['new', 'excellent', 'good', 'fair', 'poor'])->default('new');
            $table->text('condition_notes')->nullable();

            // Location tracking
            $table->string('shelf_location')->nullable(); // Specific shelf/cabinet location
            $table->string('display_location')->nullable(); // Display case location
            $table->boolean('is_on_display')->default(false);

            // Photos and documentation
            $table->json('photos')->nullable(); // Array of photo paths
            $table->json('certificates')->nullable(); // Array of certificate paths
            $table->json('appraisal_documents')->nullable(); // Appraisal documents

            // Purchase and sale tracking
            $table->foreignId('purchase_order_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');
            $table->date('purchase_date')->nullable();
            $table->date('sale_date')->nullable();
            $table->foreignId('sold_to_customer_id')->nullable()->constrained('customers')->onDelete('set null');

            // Insurance and warranty
            $table->decimal('insurance_value', 10, 2)->nullable();
            $table->string('insurance_policy_number')->nullable();
            $table->date('warranty_start_date')->nullable();
            $table->date('warranty_end_date')->nullable();
            $table->text('warranty_terms')->nullable();

            // Audit fields
            $table->text('notes')->nullable();
            $table->json('custom_fields')->nullable(); // For additional custom data
            $table->timestamp('last_verified_at')->nullable(); // Last physical verification
            $table->foreignId('last_verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Indexes for performance
            $table->index(['product_id', 'location_id']);
            $table->index(['status', 'location_id']);
            $table->index(['unique_tag']);
            $table->index(['barcode']);
            $table->index(['huid_number']);
            $table->index(['is_on_display']);
            $table->index(['purchase_date']);
            $table->index(['sale_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_items');
    }
};
