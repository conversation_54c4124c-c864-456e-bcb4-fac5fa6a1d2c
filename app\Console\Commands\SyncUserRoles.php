<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class SyncUserRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jewelsoft:sync-user-roles
                            {--dry-run : Show what would be changed without making changes}
                            {--force : Force sync even if roles match}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync the role column with Spatie <PERSON>vel Permission roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 JewelSoft User Role Synchronization');
        $this->info('=====================================');

        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
        }

        $users = User::with('roles')->get();
        $syncCount = 0;
        $errorCount = 0;

        $this->info("📊 Found {$users->count()} users to check");
        $this->newLine();

        foreach ($users as $user) {
            try {
                $spatieRole = $user->getRoleNames()->first();
                $currentRole = $user->role;

                // Check if sync is needed
                if (!$force && $spatieRole === $currentRole) {
                    $this->line("✅ {$user->name} ({$user->email}) - Role already synced: {$spatieRole}");
                    continue;
                }

                if ($spatieRole !== $currentRole) {
                    $this->warn("🔄 {$user->name} ({$user->email}):");
                    $this->line("   Current role column: " . ($currentRole ?? 'NULL'));
                    $this->line("   Spatie role: " . ($spatieRole ?? 'NULL'));

                    if (!$dryRun) {
                        $user->updateQuietly(['role' => $spatieRole]);
                        $this->info("   ✅ Updated role column to: " . ($spatieRole ?? 'NULL'));
                    } else {
                        $this->info("   🔍 Would update role column to: " . ($spatieRole ?? 'NULL'));
                    }

                    $syncCount++;
                } else {
                    $this->line("✅ {$user->name} ({$user->email}) - Role already synced: {$spatieRole}");
                }

            } catch (\Exception $e) {
                $this->error("❌ Error processing {$user->name} ({$user->email}): " . $e->getMessage());
                $errorCount++;
            }
        }

        $this->newLine();
        $this->info('📈 Synchronization Summary:');
        $this->info("Users processed: {$users->count()}");
        $this->info("Users synced: {$syncCount}");
        $this->info("Errors: {$errorCount}");

        if ($dryRun && $syncCount > 0) {
            $this->newLine();
            $this->warn('🔍 This was a dry run. Run without --dry-run to apply changes.');
        } elseif (!$dryRun && $syncCount > 0) {
            $this->newLine();
            $this->info('✅ Role synchronization completed successfully!');
        } elseif ($syncCount === 0) {
            $this->newLine();
            $this->info('✅ All user roles are already in sync!');
        }

        return $errorCount > 0 ? 1 : 0;
    }
}
