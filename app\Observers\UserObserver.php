<?php

namespace App\Observers;

use App\Models\User;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        // Sync role column with Spatie roles after user creation
        $this->syncRoleColumn($user);
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        // If roles were updated via <PERSON><PERSON>, sync the role column
        if ($user->wasChanged('role')) {
            // Role column was updated directly, no need to sync
            return;
        }

        // Check if Spatie roles might have changed and sync if needed
        $this->syncRoleColumn($user);
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        //
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        //
    }

    /**
     * Sync the role column with the primary Spatie role
     */
    private function syncRoleColumn(User $user): void
    {
        $primaryRole = $user->getRoleNames()->first();

        if ($primaryRole && $user->role !== $primaryRole) {
            // Update without triggering events to avoid infinite loop
            $user->updateQuietly(['role' => $primaryRole]);
        } elseif (!$primaryRole && $user->role !== null) {
            // Clear role column if no roles assigned
            $user->updateQuietly(['role' => null]);
        }
    }
}
