<?php

namespace App\Services;

use App\Models\Inventory;
use App\Models\InventoryItem;
use App\Models\Product;
use App\Models\Location;
use App\Models\StockMovement;
use App\Services\BarcodeService;
use App\Services\MetalRateService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class InventoryService
{
    protected $barcodeService;
    protected $metalRateService;

    public function __construct(BarcodeService $barcodeService, MetalRateService $metalRateService)
    {
        $this->barcodeService = $barcodeService;
        $this->metalRateService = $metalRateService;
    }

    /**
     * Create new inventory item with automatic tag and barcode generation
     */
    public function createInventoryItem($data)
    {
        DB::beginTransaction();
        
        try {
            // Generate unique identifiers
            if (!isset($data['unique_tag'])) {
                $location = Location::find($data['location_id']);
                $data['unique_tag'] = $this->barcodeService->generateUniqueTag('IT', $location->code ?? null);
            }
            
            // Calculate costs if not provided
            if (!isset($data['total_cost'])) {
                $data['total_cost'] = ($data['cost_price'] ?? 0) + 
                                    ($data['making_charges'] ?? 0) + 
                                    ($data['stone_charges'] ?? 0) + 
                                    ($data['other_charges'] ?? 0);
            }
            
            // Set audit fields
            $data['created_by'] = auth()->id();
            
            // Create inventory item
            $item = InventoryItem::create($data);
            
            // Generate barcode
            if (!$item->barcode) {
                $barcode = $this->barcodeService->generateItemBarcode($item);
                $item->update(['barcode' => $barcode]);
            }
            
            // Generate QR code
            if (!$item->qr_code) {
                $qrCode = $this->barcodeService->generateItemQRCode($item);
                $item->update(['qr_code' => $qrCode]);
            }
            
            // Update aggregate inventory
            $this->updateAggregateInventory($item->product_id, $item->location_id);
            
            // Create stock movement
            StockMovement::create([
                'inventory_item_id' => $item->id,
                'product_id' => $item->product_id,
                'location_id' => $item->location_id,
                'movement_type' => 'in',
                'quantity' => 1,
                'unit_cost' => $item->total_cost,
                'reference_type' => 'new_item',
                'reference_id' => $item->id,
                'notes' => 'New inventory item created',
                'created_by' => auth()->id(),
            ]);
            
            DB::commit();
            
            return $item;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update aggregate inventory counts
     */
    public function updateAggregateInventory($productId, $locationId)
    {
        $availableCount = InventoryItem::where('product_id', $productId)
                                     ->where('location_id', $locationId)
                                     ->where('status', 'available')
                                     ->count();
        
        $reservedCount = InventoryItem::where('product_id', $productId)
                                    ->where('location_id', $locationId)
                                    ->where('status', 'reserved')
                                    ->count();
        
        $avgCostPrice = InventoryItem::where('product_id', $productId)
                                   ->where('location_id', $locationId)
                                   ->whereIn('status', ['available', 'reserved'])
                                   ->avg('total_cost');
        
        $avgSellingPrice = InventoryItem::where('product_id', $productId)
                                      ->where('location_id', $locationId)
                                      ->whereIn('status', ['available', 'reserved'])
                                      ->avg('selling_price');
        
        // Update or create aggregate inventory record
        Inventory::updateOrCreate(
            [
                'product_id' => $productId,
                'location_id' => $locationId,
            ],
            [
                'quantity_available' => $availableCount,
                'quantity_reserved' => $reservedCount,
                'cost_price' => $avgCostPrice ?: 0,
                'selling_price' => $avgSellingPrice ?: 0,
                'last_stock_update' => now(),
            ]
        );
    }

    /**
     * Get inventory analytics
     */
    public function getInventoryAnalytics($locationId = null, $days = 30)
    {
        $query = InventoryItem::with(['product', 'location']);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        $totalItems = $query->count();
        $availableItems = $query->clone()->available()->count();
        $reservedItems = $query->clone()->reserved()->count();
        $soldItems = $query->clone()->sold()->count();
        $displayItems = $query->clone()->onDisplay()->count();
        
        // Value calculations
        $totalValue = $query->clone()->whereIn('status', ['available', 'reserved'])->sum('total_cost');
        $sellingValue = $query->clone()->whereIn('status', ['available', 'reserved'])->sum('selling_price');
        $potentialProfit = $sellingValue - $totalValue;
        
        // Recent activity
        $recentSales = $query->clone()->sold()
                           ->where('sale_date', '>=', now()->subDays($days))
                           ->count();
        
        $recentAdditions = $query->clone()
                                ->where('created_at', '>=', now()->subDays($days))
                                ->count();
        
        // Items needing attention
        $needsVerification = $query->clone()->needsVerification()->count();
        $warrantyExpiring = $query->clone()->warrantyExpiring()->count();
        $damagedItems = $query->clone()->where('status', 'damaged')->count();
        
        return [
            'total_items' => $totalItems,
            'available_items' => $availableItems,
            'reserved_items' => $reservedItems,
            'sold_items' => $soldItems,
            'display_items' => $displayItems,
            'total_value' => $totalValue,
            'selling_value' => $sellingValue,
            'potential_profit' => $potentialProfit,
            'profit_margin' => $totalValue > 0 ? ($potentialProfit / $totalValue) * 100 : 0,
            'recent_sales' => $recentSales,
            'recent_additions' => $recentAdditions,
            'needs_verification' => $needsVerification,
            'warranty_expiring' => $warrantyExpiring,
            'damaged_items' => $damagedItems,
        ];
    }

    /**
     * Get low stock alerts
     */
    public function getLowStockAlerts($locationId = null)
    {
        $query = Inventory::with(['product', 'location'])
                         ->whereRaw('quantity_available <= minimum_stock_level')
                         ->where('minimum_stock_level', '>', 0);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        return $query->orderBy('quantity_available')->get();
    }

    /**
     * Get items needing verification
     */
    public function getItemsNeedingVerification($locationId = null, $days = 90)
    {
        $query = InventoryItem::with(['product', 'location'])
                             ->needsVerification($days)
                             ->whereIn('status', ['available', 'reserved', 'display']);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        return $query->orderBy('last_verified_at', 'asc')->get();
    }

    /**
     * Get warranty expiring items
     */
    public function getWarrantyExpiringItems($locationId = null, $days = 30)
    {
        $query = InventoryItem::with(['product', 'location', 'soldToCustomer'])
                             ->warrantyExpiring($days);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        return $query->orderBy('warranty_end_date')->get();
    }

    /**
     * Perform bulk operations
     */
    public function bulkUpdateStatus($itemIds, $status, $notes = null)
    {
        DB::beginTransaction();
        
        try {
            $items = InventoryItem::whereIn('id', $itemIds)->get();
            $results = [];
            
            foreach ($items as $item) {
                $oldStatus = $item->status;
                $item->update([
                    'status' => $status,
                    'condition_notes' => $notes,
                    'updated_by' => auth()->id(),
                ]);
                
                // Create stock movement
                StockMovement::create([
                    'inventory_item_id' => $item->id,
                    'product_id' => $item->product_id,
                    'location_id' => $item->location_id,
                    'movement_type' => 'adjustment',
                    'quantity' => 1,
                    'reference_type' => 'bulk_status_update',
                    'reference_id' => null,
                    'notes' => "Status changed from {$oldStatus} to {$status}. {$notes}",
                    'created_by' => auth()->id(),
                ]);
                
                // Update aggregate inventory
                $this->updateAggregateInventory($item->product_id, $item->location_id);
                
                $results[] = [
                    'item_id' => $item->id,
                    'tag' => $item->unique_tag,
                    'old_status' => $oldStatus,
                    'new_status' => $status,
                    'success' => true,
                ];
            }
            
            DB::commit();
            
            return $results;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Bulk transfer items
     */
    public function bulkTransferItems($itemIds, $newLocationId, $notes = null)
    {
        DB::beginTransaction();
        
        try {
            $items = InventoryItem::whereIn('id', $itemIds)->get();
            $results = [];
            
            foreach ($items as $item) {
                $oldLocationId = $item->location_id;
                
                if ($oldLocationId !== $newLocationId) {
                    $item->updateLocation($newLocationId);
                    
                    $results[] = [
                        'item_id' => $item->id,
                        'tag' => $item->unique_tag,
                        'old_location' => $oldLocationId,
                        'new_location' => $newLocationId,
                        'success' => true,
                    ];
                    
                    // Update aggregate inventory for both locations
                    $this->updateAggregateInventory($item->product_id, $oldLocationId);
                    $this->updateAggregateInventory($item->product_id, $newLocationId);
                }
            }
            
            DB::commit();
            
            return $results;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get inventory valuation report
     */
    public function getInventoryValuation($locationId = null, $asOfDate = null)
    {
        $asOfDate = $asOfDate ?: now();
        
        $query = InventoryItem::with(['product.metal', 'location'])
                             ->whereIn('status', ['available', 'reserved', 'display'])
                             ->where('created_at', '<=', $asOfDate);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        $items = $query->get();
        
        $valuation = [
            'total_items' => $items->count(),
            'cost_value' => $items->sum('total_cost'),
            'selling_value' => $items->sum('selling_price'),
            'potential_profit' => $items->sum('profit_amount'),
            'by_metal' => [],
            'by_location' => [],
            'by_product_type' => [],
            'as_of_date' => $asOfDate,
        ];
        
        // Group by metal
        $byMetal = $items->groupBy('product.metal.name');
        foreach ($byMetal as $metalName => $metalItems) {
            $valuation['by_metal'][$metalName] = [
                'count' => $metalItems->count(),
                'cost_value' => $metalItems->sum('total_cost'),
                'selling_value' => $metalItems->sum('selling_price'),
                'weight' => $metalItems->sum('actual_weight'),
            ];
        }
        
        // Group by location
        $byLocation = $items->groupBy('location.name');
        foreach ($byLocation as $locationName => $locationItems) {
            $valuation['by_location'][$locationName] = [
                'count' => $locationItems->count(),
                'cost_value' => $locationItems->sum('total_cost'),
                'selling_value' => $locationItems->sum('selling_price'),
            ];
        }
        
        // Group by product category
        $byCategory = $items->groupBy('product.category.name');
        foreach ($byCategory as $categoryName => $categoryItems) {
            $valuation['by_product_type'][$categoryName] = [
                'count' => $categoryItems->count(),
                'cost_value' => $categoryItems->sum('total_cost'),
                'selling_value' => $categoryItems->sum('selling_price'),
            ];
        }
        
        return $valuation;
    }

    /**
     * Get inventory turnover analysis
     */
    public function getInventoryTurnover($locationId = null, $days = 365)
    {
        $startDate = now()->subDays($days);
        
        $query = InventoryItem::with(['product']);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        // Items sold in the period
        $soldItems = $query->clone()
                          ->sold()
                          ->where('sale_date', '>=', $startDate)
                          ->get();
        
        // Current inventory
        $currentItems = $query->clone()
                            ->whereIn('status', ['available', 'reserved', 'display'])
                            ->get();
        
        $totalSoldValue = $soldItems->sum('total_cost');
        $currentInventoryValue = $currentItems->sum('total_cost');
        $averageInventoryValue = ($currentInventoryValue + $totalSoldValue) / 2;
        
        $turnoverRatio = $averageInventoryValue > 0 ? $totalSoldValue / $averageInventoryValue : 0;
        $turnoverDays = $turnoverRatio > 0 ? $days / $turnoverRatio : 0;
        
        return [
            'period_days' => $days,
            'items_sold' => $soldItems->count(),
            'sold_value' => $totalSoldValue,
            'current_inventory_value' => $currentInventoryValue,
            'average_inventory_value' => $averageInventoryValue,
            'turnover_ratio' => $turnoverRatio,
            'turnover_days' => $turnoverDays,
            'turnover_frequency' => $turnoverRatio * ($days / 365), // Annual frequency
        ];
    }

    /**
     * Get slow-moving inventory
     */
    public function getSlowMovingInventory($locationId = null, $days = 180)
    {
        $cutoffDate = now()->subDays($days);
        
        $query = InventoryItem::with(['product', 'location'])
                             ->whereIn('status', ['available', 'display'])
                             ->where('created_at', '<=', $cutoffDate);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        return $query->orderBy('created_at')->get();
    }

    /**
     * Get fast-moving inventory
     */
    public function getFastMovingInventory($locationId = null, $days = 30)
    {
        $startDate = now()->subDays($days);
        
        // Get products that have sold multiple items recently
        $fastMovingProducts = InventoryItem::select('product_id', DB::raw('COUNT(*) as sales_count'))
                                         ->sold()
                                         ->where('sale_date', '>=', $startDate)
                                         ->when($locationId, function ($query) use ($locationId) {
                                             $query->where('location_id', $locationId);
                                         })
                                         ->groupBy('product_id')
                                         ->having('sales_count', '>=', 2)
                                         ->orderBy('sales_count', 'desc')
                                         ->get();
        
        return $fastMovingProducts;
    }

    /**
     * Perform physical inventory verification
     */
    public function performPhysicalVerification($itemIds, $verificationData)
    {
        DB::beginTransaction();
        
        try {
            $results = [];
            
            foreach ($itemIds as $itemId) {
                $item = InventoryItem::find($itemId);
                if (!$item) continue;
                
                $itemData = $verificationData[$itemId] ?? [];
                $discrepancies = [];
                
                // Check weight discrepancy
                if (isset($itemData['actual_weight']) && $item->actual_weight) {
                    $weightDiff = abs($item->actual_weight - $itemData['actual_weight']);
                    if ($weightDiff > 0.01) { // 0.01g tolerance
                        $discrepancies[] = "Weight: Expected {$item->actual_weight}g, Found {$itemData['actual_weight']}g";
                    }
                }
                
                // Check condition
                if (isset($itemData['condition']) && $item->condition !== $itemData['condition']) {
                    $discrepancies[] = "Condition: Expected {$item->condition}, Found {$itemData['condition']}";
                }
                
                // Check location
                if (isset($itemData['location']) && $item->shelf_location !== $itemData['location']) {
                    $discrepancies[] = "Location: Expected {$item->shelf_location}, Found {$itemData['location']}";
                }
                
                // Update item with verification
                $item->verify(
                    count($discrepancies) > 0 
                        ? "Verification discrepancies: " . implode('; ', $discrepancies)
                        : "Physical verification completed - all details match"
                );
                
                // Update any corrected values
                if (isset($itemData['actual_weight'])) {
                    $item->update(['actual_weight' => $itemData['actual_weight']]);
                }
                if (isset($itemData['condition'])) {
                    $item->update(['condition' => $itemData['condition']]);
                }
                if (isset($itemData['shelf_location'])) {
                    $item->update(['shelf_location' => $itemData['shelf_location']]);
                }
                
                $results[] = [
                    'item_id' => $item->id,
                    'tag' => $item->unique_tag,
                    'discrepancies' => $discrepancies,
                    'status' => count($discrepancies) > 0 ? 'discrepancy' : 'verified',
                ];
            }
            
            DB::commit();
            
            return $results;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Generate inventory reports
     */
    public function generateInventoryReport($type, $locationId = null, $startDate = null, $endDate = null)
    {
        $startDate = $startDate ?: now()->subDays(30);
        $endDate = $endDate ?: now();
        
        switch ($type) {
            case 'stock_movement':
                return $this->generateStockMovementReport($locationId, $startDate, $endDate);
            case 'valuation':
                return $this->generateValuationReport($locationId, $endDate);
            case 'aging':
                return $this->generateAgingReport($locationId);
            case 'verification':
                return $this->generateVerificationReport($locationId);
            default:
                throw new \InvalidArgumentException("Unknown report type: {$type}");
        }
    }

    /**
     * Generate stock movement report
     */
    private function generateStockMovementReport($locationId, $startDate, $endDate)
    {
        $query = StockMovement::with(['inventoryItem.product', 'createdBy'])
                             ->whereBetween('created_at', [$startDate, $endDate]);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        $movements = $query->orderBy('created_at', 'desc')->get();
        
        return [
            'type' => 'stock_movement',
            'period' => [
                'start' => $startDate,
                'end' => $endDate,
            ],
            'location_id' => $locationId,
            'movements' => $movements,
            'summary' => [
                'total_movements' => $movements->count(),
                'stock_in' => $movements->where('movement_type', 'in')->count(),
                'stock_out' => $movements->where('movement_type', 'out')->count(),
                'transfers' => $movements->where('movement_type', 'transfer')->count(),
                'adjustments' => $movements->where('movement_type', 'adjustment')->count(),
            ],
        ];
    }

    /**
     * Generate valuation report
     */
    private function generateValuationReport($locationId, $asOfDate)
    {
        return $this->getInventoryValuation($locationId, $asOfDate);
    }

    /**
     * Generate aging report
     */
    private function generateAgingReport($locationId)
    {
        $query = InventoryItem::with(['product', 'location'])
                             ->whereIn('status', ['available', 'reserved', 'display']);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        $items = $query->get();
        
        $agingBuckets = [
            '0-30' => ['min' => 0, 'max' => 30, 'items' => []],
            '31-60' => ['min' => 31, 'max' => 60, 'items' => []],
            '61-90' => ['min' => 61, 'max' => 90, 'items' => []],
            '91-180' => ['min' => 91, 'max' => 180, 'items' => []],
            '181-365' => ['min' => 181, 'max' => 365, 'items' => []],
            '365+' => ['min' => 366, 'max' => null, 'items' => []],
        ];
        
        foreach ($items as $item) {
            $age = $item->age_in_days;
            
            foreach ($agingBuckets as $bucket => &$bucketData) {
                if ($age >= $bucketData['min'] && ($bucketData['max'] === null || $age <= $bucketData['max'])) {
                    $bucketData['items'][] = $item;
                    break;
                }
            }
        }
        
        // Calculate bucket summaries
        foreach ($agingBuckets as $bucket => &$bucketData) {
            $bucketData['count'] = count($bucketData['items']);
            $bucketData['value'] = collect($bucketData['items'])->sum('total_cost');
        }
        
        return [
            'type' => 'aging',
            'as_of_date' => now(),
            'location_id' => $locationId,
            'aging_buckets' => $agingBuckets,
            'total_items' => $items->count(),
            'total_value' => $items->sum('total_cost'),
        ];
    }

    /**
     * Generate verification report
     */
    private function generateVerificationReport($locationId)
    {
        $query = InventoryItem::with(['product', 'location', 'lastVerifiedBy'])
                             ->whereIn('status', ['available', 'reserved', 'display']);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        $items = $query->get();
        
        $verificationBuckets = [
            'never_verified' => $items->filter(function ($item) {
                return !$item->last_verified_at;
            }),
            'verified_0_30' => $items->filter(function ($item) {
                return $item->last_verified_at && $item->days_since_verification <= 30;
            }),
            'verified_31_90' => $items->filter(function ($item) {
                return $item->last_verified_at && $item->days_since_verification > 30 && $item->days_since_verification <= 90;
            }),
            'verified_90_plus' => $items->filter(function ($item) {
                return $item->last_verified_at && $item->days_since_verification > 90;
            }),
        ];
        
        return [
            'type' => 'verification',
            'as_of_date' => now(),
            'location_id' => $locationId,
            'verification_buckets' => $verificationBuckets,
            'total_items' => $items->count(),
            'needs_verification' => $verificationBuckets['never_verified']->count() + $verificationBuckets['verified_90_plus']->count(),
        ];
    }
}
