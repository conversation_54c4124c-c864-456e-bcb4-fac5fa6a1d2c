<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_dashboard');
    }

    public function index()
    {
        // Simple stats for testing
        $stats = [
            'today_sales' => [
                'amount' => 0,
                'count' => 0
            ],
            'monthly_sales' => [
                'amount' => 0,
                'count' => 0
            ],
            'total_customers' => 0,
            'pending_invoices' => [
                'amount' => 0,
                'count' => 0
            ],
            'low_stock_items' => 0,
            'metal_rates' => [],
            'recent_invoices' => [],
            'top_selling_categories' => [],
            'user_info' => [
                'name' => Auth::user()->name,
                'role' => 'Admin',
                'permissions' => Auth::user()->getAllPermissions()->pluck('name'),
                'location' => Auth::user()->location ? Auth::user()->location->name : 'N/A',
                'can_edit_rates' => $this->checkPermission('edit_metal_rates'),
                'can_view_reports' => $this->checkPermission('view_reports'),
                'can_manage_customers' => $this->checkPermission('manage_customers'),
            ],
        ];

        return view('admin.dashboard', compact('stats'));
    }

    private function getTodaySales(): array
    {
        $today = Carbon::today();

        $sales = DB::table('invoices')
            ->whereDate('invoice_date', $today)
            ->where('status', '!=', 'cancelled')
            ->selectRaw('
                COUNT(*) as count,
                SUM(total_amount) as amount,
                SUM(paid_amount) as paid
            ')
            ->first();

        return [
            'count' => $sales->count ?? 0,
            'amount' => $sales->amount ?? 0,
            'paid' => $sales->paid ?? 0,
        ];
    }

    private function getMonthlySales(): array
    {
        $thisMonth = Carbon::now()->startOfMonth();

        $sales = DB::table('invoices')
            ->where('invoice_date', '>=', $thisMonth)
            ->where('status', '!=', 'cancelled')
            ->selectRaw('
                COUNT(*) as count,
                SUM(total_amount) as amount,
                SUM(paid_amount) as paid
            ')
            ->first();

        return [
            'count' => $sales->count ?? 0,
            'amount' => $sales->amount ?? 0,
            'paid' => $sales->paid ?? 0,
        ];
    }

    private function getTotalCustomers(): int
    {
        return DB::table('customers')->where('is_active', true)->count();
    }

    private function getPendingInvoices(): array
    {
        $pending = DB::table('invoices')
            ->whereIn('status', ['sent', 'partially_paid', 'overdue'])
            ->selectRaw('
                COUNT(*) as count,
                SUM(balance_amount) as amount
            ')
            ->first();

        return [
            'count' => $pending->count ?? 0,
            'amount' => $pending->amount ?? 0,
        ];
    }

    private function getLowStockItems(): int
    {
        return DB::table('inventory')
            ->whereRaw('quantity_available <= minimum_stock_level')
            ->count();
    }

    private function getCurrentMetalRates(): array
    {
        return DB::table('metals')
            ->where('is_active', true)
            ->select('id', 'name', 'purity', 'current_rate_per_gram', 'rate_updated_at')
            ->orderBy('name')
            ->orderBy('purity')
            ->get()
            ->toArray();
    }

    private function getRecentInvoices(): array
    {
        return DB::table('invoices')
            ->join('customers', 'invoices.customer_id', '=', 'customers.id')
            ->select(
                'invoices.id',
                'invoices.invoice_number',
                'invoices.invoice_date',
                'invoices.total_amount',
                'invoices.status',
                'customers.first_name',
                'customers.last_name'
            )
            ->orderBy('invoices.created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($invoice) {
                $invoice->customer_name = trim($invoice->first_name . ' ' . $invoice->last_name);
                unset($invoice->first_name, $invoice->last_name);
                return $invoice;
            })
            ->toArray();
    }

    private function getTopSellingCategories(): array
    {
        return DB::table('invoice_items')
            ->join('products', 'invoice_items.product_id', '=', 'products.id')
            ->join('categories', 'products.category_id', '=', 'categories.id')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->where('invoices.status', '!=', 'cancelled')
            ->whereMonth('invoices.invoice_date', Carbon::now()->month)
            ->select(
                'categories.name',
                DB::raw('SUM(invoice_items.quantity) as total_quantity'),
                DB::raw('SUM(invoice_items.total_amount) as total_amount')
            )
            ->groupBy('categories.id', 'categories.name')
            ->orderBy('total_amount', 'desc')
            ->limit(5)
            ->get()
            ->toArray();
    }

    public function getMetalRates()
    {
        $rates = $this->getCurrentMetalRates();
        return response()->json($rates);
    }

    public function updateMetalRate(Request $request)
    {
        // Check permission
        if (!Auth::user()->canEditRates()) {
            return response()->json(['success' => false, 'message' => 'You do not have permission to edit metal rates'], 403);
        }

        $request->validate([
            'metal_id' => 'required|exists:metals,id',
            'rate' => 'required|numeric|min:0',
        ]);

        DB::table('metals')
            ->where('id', $request->metal_id)
            ->update([
                'current_rate_per_gram' => $request->rate,
                'rate_updated_at' => now(),
                'updated_at' => now(),
            ]);

        // Store in rate history
        DB::table('metal_rate_history')->updateOrCreate(
            [
                'metal_id' => $request->metal_id,
                'rate_date' => Carbon::today(),
            ],
            [
                'rate_per_gram' => $request->rate,
                'source' => 'manual',
                'updated_at' => now(),
            ]
        );

        return response()->json(['success' => true, 'message' => 'Metal rate updated successfully']);
    }

    /**
     * Safely check if user has permission, handling cases where permission doesn't exist
     */
    private function checkPermission(string $permission): bool
    {
        try {
            return Auth::user()->hasPermissionTo($permission);
        } catch (\Spatie\Permission\Exceptions\PermissionDoesNotExist $e) {
            return false;
        }
    }
}
