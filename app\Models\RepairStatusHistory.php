<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairStatusHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'repair_service_id',
        'status',
        'notes',
        'updated_by',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($history) {
            $history->updated_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function repairService(): BelongsTo
    {
        return $this->belongsTo(RepairService::class);
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'received' => 'Received',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'received' => 'blue',
            'in_progress' => 'yellow',
            'completed' => 'green',
            'delivered' => 'purple',
            'cancelled' => 'red',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    /**
     * Scopes
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByRepairService($query, $repairServiceId)
    {
        return $query->where('repair_service_id', $repairServiceId);
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Business Logic Methods
     */
    public function getSummary()
    {
        return [
            'id' => $this->id,
            'repair_service_id' => $this->repair_service_id,
            'job_number' => $this->repairService->job_number,
            'status' => $this->status_display,
            'notes' => $this->notes,
            'updated_by' => $this->updatedBy->name,
            'updated_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
