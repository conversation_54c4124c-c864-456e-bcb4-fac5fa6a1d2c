<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ExpenseController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_expenses')->only(['index', 'show']);
        $this->middleware('permission:manage_expenses')->only(['create', 'store', 'edit', 'update', 'destroy']);
        $this->middleware('permission:approve_expenses')->only(['approve', 'reject']);
    }

    /**
     * Display a listing of expenses
     */
    public function index(Request $request)
    {
        $query = Expense::with(['category', 'supplier', 'createdBy', 'approvedBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('expense_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('receipt_number', 'like', "%{$search}%")
                  ->orWhereHas('supplier', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by supplier
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('expense_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('expense_date', '<=', $request->date_to);
        }

        // Filter by amount range
        if ($request->filled('amount_from')) {
            $query->where('total_amount', '>=', $request->amount_from);
        }
        if ($request->filled('amount_to')) {
            $query->where('total_amount', '<=', $request->amount_to);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'expense_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $expenses = $query->paginate(20);

        // Get filter options
        $categories = ExpenseCategory::active()->orderBy('name')->get(['id', 'name']);
        $suppliers = Supplier::orderBy('name')->get(['id', 'name']);

        $statuses = [
            'pending' => 'Pending Approval',
            'approved' => 'Approved',
            'paid' => 'Paid',
            'rejected' => 'Rejected',
            'cancelled' => 'Cancelled',
        ];

        // Get summary statistics
        $stats = $this->getExpenseStats($request);

        return view('admin.expenses.index', compact('expenses', 'categories', 'suppliers', 'statuses', 'stats'));
    }

    /**
     * Show the form for creating a new expense
     */
    public function create()
    {
        $categories = ExpenseCategory::active()->orderBy('name')->get(['id', 'name']);
        $suppliers = Supplier::orderBy('name')->get(['id', 'name']);

        return view('admin.expenses.create', compact('categories', 'suppliers'));
    }

    /**
     * Store a newly created expense
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'expense_date' => 'required|date',
            'category_id' => 'required|exists:expense_categories,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'description' => 'required|string|max:500',
            'amount' => 'required|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string',
            'payment_reference' => 'nullable|string|max:100',
            'receipt_number' => 'nullable|string|max:100',
            'receipt_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $taxAmount = $validated['tax_amount'] ?? 0;
            $totalAmount = $validated['amount'] + $taxAmount;

            $expense = Expense::create([
                'expense_date' => $validated['expense_date'],
                'category_id' => $validated['category_id'],
                'supplier_id' => $validated['supplier_id'],
                'description' => $validated['description'],
                'amount' => $validated['amount'],
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'payment_method' => $validated['payment_method'],
                'payment_reference' => $validated['payment_reference'],
                'receipt_number' => $validated['receipt_number'],
                'notes' => $validated['notes'],
                'status' => 'pending',
            ]);

            // Handle receipt upload
            if ($request->hasFile('receipt_file')) {
                $expense->uploadReceipt($request->file('receipt_file'));
            }

            return redirect()->route('admin.expenses.show', $expense)
                           ->with('success', 'Expense created successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Failed to create expense: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified expense
     */
    public function show(Expense $expense)
    {
        $expense->load(['category', 'supplier', 'createdBy', 'approvedBy', 'journalEntries']);

        return view('admin.expenses.show', compact('expense'));
    }

    /**
     * Show the form for editing the expense
     */
    public function edit(Expense $expense)
    {
        if (!$expense->can_edit) {
            return redirect()->route('admin.expenses.show', $expense)
                           ->with('error', 'This expense cannot be edited.');
        }

        $categories = ExpenseCategory::active()->orderBy('name')->get(['id', 'name']);
        $suppliers = Supplier::orderBy('name')->get(['id', 'name']);

        return view('admin.expenses.edit', compact('expense', 'categories', 'suppliers'));
    }

    /**
     * Update the specified expense
     */
    public function update(Request $request, Expense $expense)
    {
        if (!$expense->can_edit) {
            return redirect()->route('admin.expenses.show', $expense)
                           ->with('error', 'This expense cannot be edited.');
        }

        $validated = $request->validate([
            'expense_date' => 'required|date',
            'category_id' => 'required|exists:expense_categories,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'description' => 'required|string|max:500',
            'amount' => 'required|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string',
            'payment_reference' => 'nullable|string|max:100',
            'receipt_number' => 'nullable|string|max:100',
            'receipt_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $taxAmount = $validated['tax_amount'] ?? 0;
            $totalAmount = $validated['amount'] + $taxAmount;

            $expense->update([
                'expense_date' => $validated['expense_date'],
                'category_id' => $validated['category_id'],
                'supplier_id' => $validated['supplier_id'],
                'description' => $validated['description'],
                'amount' => $validated['amount'],
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'payment_method' => $validated['payment_method'],
                'payment_reference' => $validated['payment_reference'],
                'receipt_number' => $validated['receipt_number'],
                'notes' => $validated['notes'],
            ]);

            // Handle receipt upload
            if ($request->hasFile('receipt_file')) {
                // Delete old receipt if exists
                if ($expense->receipt_path) {
                    Storage::disk('public')->delete($expense->receipt_path);
                }
                $expense->uploadReceipt($request->file('receipt_file'));
            }

            return redirect()->route('admin.expenses.show', $expense)
                           ->with('success', 'Expense updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Failed to update expense: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified expense
     */
    public function destroy(Expense $expense)
    {
        if (!$expense->can_delete) {
            return back()->with('error', 'This expense cannot be deleted.');
        }

        try {
            // Delete receipt file if exists
            if ($expense->receipt_path) {
                Storage::disk('public')->delete($expense->receipt_path);
            }

            $expense->delete();

            return redirect()->route('admin.expenses.index')
                           ->with('success', 'Expense deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete expense: ' . $e->getMessage());
        }
    }

    /**
     * Approve expense
     */
    public function approve(Request $request, Expense $expense)
    {
        try {
            $expense->approve();

            return response()->json([
                'success' => true,
                'message' => 'Expense approved successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Reject expense
     */
    public function reject(Request $request, Expense $expense)
    {
        $validated = $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $expense->reject($validated['reason']);

            return response()->json([
                'success' => true,
                'message' => 'Expense rejected successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Mark expense as paid
     */
    public function markAsPaid(Request $request, Expense $expense)
    {
        $validated = $request->validate([
            'payment_method' => 'nullable|string',
            'payment_reference' => 'nullable|string|max:100',
        ]);

        try {
            $expense->markAsPaid(
                $validated['payment_method'] ?? null,
                $validated['payment_reference'] ?? null
            );

            return response()->json([
                'success' => true,
                'message' => 'Expense marked as paid successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get expense statistics
     */
    protected function getExpenseStats($request)
    {
        $query = Expense::query();

        // Apply same filters as main query
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('expense_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('expense_date', '<=', $request->date_to);
        }

        return [
            'total_expenses' => $query->sum('total_amount'),
            'pending_count' => $query->where('status', 'pending')->count(),
            'approved_count' => $query->where('status', 'approved')->count(),
            'paid_count' => $query->where('status', 'paid')->count(),
            'rejected_count' => $query->where('status', 'rejected')->count(),
            'average_amount' => $query->avg('total_amount') ?: 0,
        ];
    }
}
