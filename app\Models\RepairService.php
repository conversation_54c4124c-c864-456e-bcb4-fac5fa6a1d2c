<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class RepairService extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'job_number',
        'customer_id',
        'location_id',
        'item_name',
        'item_description',
        'repair_description',
        'estimated_cost',
        'actual_cost',
        'received_date',
        'promised_date',
        'completed_date',
        'delivered_date',
        'status',
        'priority',
        'before_photo_path',
        'after_photo_path',
        'notes',
        'customer_instructions',
        'assigned_to',
        'created_by',
    ];

    protected $casts = [
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'received_date' => 'date',
        'promised_date' => 'date',
        'completed_date' => 'date',
        'delivered_date' => 'date',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($repairService) {
            if (!$repairService->job_number) {
                $repairService->job_number = static::generateJobNumber();
            }
            $repairService->created_by = auth()->id();
        });

        static::updating(function ($repairService) {
            // Log status changes
            if ($repairService->isDirty('status')) {
                $repairService->logStatusChange(
                    $repairService->getOriginal('status'),
                    $repairService->status
                );
            }
        });
    }

    /**
     * Relationships
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function statusHistory(): HasMany
    {
        return $this->hasMany(RepairStatusHistory::class);
    }

    public function repairItems(): HasMany
    {
        return $this->hasMany(RepairItem::class);
    }

    public function repairCharges(): HasMany
    {
        return $this->hasMany(RepairCharge::class);
    }

    public function customerNotifications(): HasMany
    {
        return $this->hasMany(CustomerNotification::class, 'related_id')
                   ->where('related_type', 'repair_service');
    }

    /**
     * Scopes
     */
    public function scopeReceived($query)
    {
        return $query->where('status', 'received');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeOverdue($query)
    {
        return $query->where('promised_date', '<', now())
                    ->whereNotIn('status', ['completed', 'delivered', 'cancelled']);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('received_date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('received_date', now()->month)
                    ->whereYear('received_date', now()->year);
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'received' => 'Received',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'received' => 'blue',
            'in_progress' => 'yellow',
            'completed' => 'green',
            'delivered' => 'purple',
            'cancelled' => 'red',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getPriorityDisplayAttribute()
    {
        $priorities = [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'urgent' => 'Urgent',
        ];

        return $priorities[$this->priority] ?? ucfirst($this->priority);
    }

    public function getPriorityColorAttribute()
    {
        $colors = [
            'low' => 'green',
            'medium' => 'blue',
            'high' => 'orange',
            'urgent' => 'red',
        ];

        return $colors[$this->priority] ?? 'gray';
    }

    public function getIsOverdueAttribute()
    {
        return $this->promised_date &&
               $this->promised_date < now() &&
               !in_array($this->status, ['completed', 'delivered', 'cancelled']);
    }

    public function getDaysOverdueAttribute()
    {
        if (!$this->is_overdue) {
            return 0;
        }

        return $this->promised_date->diffInDays(now());
    }

    public function getDaysInProgressAttribute()
    {
        $startDate = $this->received_date;
        $endDate = $this->delivered_date ?: now();

        return $startDate->diffInDays($endDate);
    }

    public function getEstimatedDaysAttribute()
    {
        if (!$this->promised_date) {
            return null;
        }

        return $this->received_date->diffInDays($this->promised_date);
    }

    public function getProgressPercentageAttribute()
    {
        $statusProgress = [
            'received' => 10,
            'in_progress' => 50,
            'completed' => 90,
            'delivered' => 100,
            'cancelled' => 0,
        ];

        return $statusProgress[$this->status] ?? 0;
    }

    public function getTotalChargesAttribute()
    {
        return $this->repairCharges->sum('amount');
    }

    public function getCanEditAttribute()
    {
        return !in_array($this->status, ['delivered', 'cancelled']);
    }

    public function getCanCompleteAttribute()
    {
        return $this->status === 'in_progress';
    }

    public function getCanDeliverAttribute()
    {
        return $this->status === 'completed';
    }

    public function getCanCancelAttribute()
    {
        return !in_array($this->status, ['delivered', 'cancelled']);
    }

    /**
     * Business Logic Methods
     */
    public static function generateJobNumber()
    {
        $prefix = 'REP';
        $year = now()->format('y');
        $month = now()->format('m');

        $lastJob = static::whereYear('created_at', now()->year)
                        ->whereMonth('created_at', now()->month)
                        ->orderBy('id', 'desc')
                        ->first();

        $sequence = $lastJob ? (int) substr($lastJob->job_number, -4) + 1 : 1;

        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function startWork($notes = null)
    {
        if ($this->status !== 'received') {
            throw new \Exception('Only received jobs can be started');
        }

        $this->update(['status' => 'in_progress']);

        $this->logStatusChange('received', 'in_progress', $notes);

        // Send notification to customer
        $this->notifyCustomer('work_started', [
            'job_number' => $this->job_number,
            'item_name' => $this->item_name,
        ]);

        return $this;
    }

    public function completeWork($actualCost = null, $notes = null)
    {
        if ($this->status !== 'in_progress') {
            throw new \Exception('Only in-progress jobs can be completed');
        }

        $updateData = [
            'status' => 'completed',
            'completed_date' => now(),
        ];

        if ($actualCost !== null) {
            $updateData['actual_cost'] = $actualCost;
        }

        $this->update($updateData);

        $this->logStatusChange('in_progress', 'completed', $notes);

        // Send notification to customer
        $this->notifyCustomer('work_completed', [
            'job_number' => $this->job_number,
            'item_name' => $this->item_name,
            'actual_cost' => $this->actual_cost,
        ]);

        return $this;
    }

    public function deliverItem($notes = null)
    {
        if ($this->status !== 'completed') {
            throw new \Exception('Only completed jobs can be delivered');
        }

        $this->update([
            'status' => 'delivered',
            'delivered_date' => now(),
        ]);

        $this->logStatusChange('completed', 'delivered', $notes);

        // Send notification to customer
        $this->notifyCustomer('item_delivered', [
            'job_number' => $this->job_number,
            'item_name' => $this->item_name,
        ]);

        return $this;
    }

    public function cancelJob($reason = null)
    {
        if (!$this->can_cancel) {
            throw new \Exception('This job cannot be cancelled');
        }

        $oldStatus = $this->status;
        $this->update(['status' => 'cancelled']);

        $this->logStatusChange($oldStatus, 'cancelled', $reason);

        // Send notification to customer
        $this->notifyCustomer('job_cancelled', [
            'job_number' => $this->job_number,
            'item_name' => $this->item_name,
            'reason' => $reason,
        ]);

        return $this;
    }

    public function assignTo($userId, $notes = null)
    {
        $oldAssignee = $this->assigned_to;
        $this->update(['assigned_to' => $userId]);

        // Log assignment change
        activity()
            ->performedOn($this)
            ->withProperties([
                'old_assignee' => $oldAssignee,
                'new_assignee' => $userId,
                'notes' => $notes,
            ])
            ->log('Repair job reassigned');

        return $this;
    }

    public function updateEstimate($estimatedCost, $promisedDate = null, $notes = null)
    {
        $updateData = ['estimated_cost' => $estimatedCost];

        if ($promisedDate) {
            $updateData['promised_date'] = $promisedDate;
        }

        $this->update($updateData);

        // Send notification to customer if estimate changed significantly
        if (abs($this->getOriginal('estimated_cost') - $estimatedCost) > 100) {
            $this->notifyCustomer('estimate_updated', [
                'job_number' => $this->job_number,
                'item_name' => $this->item_name,
                'new_estimate' => $estimatedCost,
                'promised_date' => $promisedDate,
            ]);
        }

        return $this;
    }

    public function addCharge($description, $amount, $chargeType = 'labor')
    {
        return $this->repairCharges()->create([
            'description' => $description,
            'amount' => $amount,
            'charge_type' => $chargeType,
            'created_by' => auth()->id(),
        ]);
    }

    public function uploadPhoto($photoPath, $type = 'before')
    {
        $field = $type . '_photo_path';
        $this->update([$field => $photoPath]);

        return $this;
    }

    protected function logStatusChange($oldStatus, $newStatus, $notes = null)
    {
        $this->statusHistory()->create([
            'status' => $newStatus,
            'notes' => $notes ?: "Status changed from {$oldStatus} to {$newStatus}",
            'updated_by' => auth()->id(),
        ]);
    }

    protected function notifyCustomer($type, $data = [])
    {
        // Create customer notification
        CustomerNotification::create([
            'customer_id' => $this->customer_id,
            'type' => $type,
            'title' => $this->getNotificationTitle($type),
            'message' => $this->getNotificationMessage($type, $data),
            'related_type' => 'repair_service',
            'related_id' => $this->id,
            'data' => $data,
        ]);
    }

    protected function getNotificationTitle($type)
    {
        $titles = [
            'work_started' => 'Repair Work Started',
            'work_completed' => 'Repair Work Completed',
            'item_delivered' => 'Item Delivered',
            'job_cancelled' => 'Repair Job Cancelled',
            'estimate_updated' => 'Repair Estimate Updated',
        ];

        return $titles[$type] ?? 'Repair Update';
    }

    protected function getNotificationMessage($type, $data)
    {
        $messages = [
            'work_started' => "We have started working on your {$data['item_name']} (Job #{$data['job_number']}).",
            'work_completed' => "Your {$data['item_name']} repair is completed (Job #{$data['job_number']}). Total cost: ₹{$data['actual_cost']}",
            'item_delivered' => "Your {$data['item_name']} has been delivered (Job #{$data['job_number']}).",
            'job_cancelled' => "Your repair job #{$data['job_number']} has been cancelled. Reason: {$data['reason']}",
            'estimate_updated' => "Repair estimate for {$data['item_name']} has been updated to ₹{$data['new_estimate']}",
        ];

        return $messages[$type] ?? 'Your repair job has been updated.';
    }

    public function getTimelineEvents()
    {
        $events = collect();

        // Add creation event
        $events->push([
            'type' => 'created',
            'title' => 'Job Created',
            'description' => 'Repair job was created and item received',
            'date' => $this->created_at,
            'user' => $this->createdBy->name,
            'icon' => 'plus',
            'color' => 'blue',
        ]);

        // Add status history events
        foreach ($this->statusHistory as $history) {
            $events->push([
                'type' => 'status_change',
                'title' => 'Status Updated',
                'description' => $history->notes,
                'date' => $history->created_at,
                'user' => $history->updatedBy->name,
                'icon' => 'refresh',
                'color' => $this->getStatusColor($history->status),
            ]);
        }

        // Add completion event
        if ($this->completed_date) {
            $events->push([
                'type' => 'completed',
                'title' => 'Work Completed',
                'description' => 'Repair work has been completed',
                'date' => $this->completed_date,
                'user' => $this->assignedTo?->name,
                'icon' => 'check',
                'color' => 'green',
            ]);
        }

        // Add delivery event
        if ($this->delivered_date) {
            $events->push([
                'type' => 'delivered',
                'title' => 'Item Delivered',
                'description' => 'Item has been delivered to customer',
                'date' => $this->delivered_date,
                'user' => null,
                'icon' => 'truck',
                'color' => 'purple',
            ]);
        }

        return $events->sortBy('date');
    }

    protected function getStatusColor($status)
    {
        $colors = [
            'received' => 'blue',
            'in_progress' => 'yellow',
            'completed' => 'green',
            'delivered' => 'purple',
            'cancelled' => 'red',
        ];

        return $colors[$status] ?? 'gray';
    }

    public function getSummary()
    {
        return [
            'job_number' => $this->job_number,
            'customer_name' => $this->customer->name,
            'customer_phone' => $this->customer->phone,
            'item_name' => $this->item_name,
            'repair_description' => $this->repair_description,
            'status' => $this->status_display,
            'priority' => $this->priority_display,
            'estimated_cost' => $this->estimated_cost,
            'actual_cost' => $this->actual_cost,
            'received_date' => $this->received_date->format('Y-m-d'),
            'promised_date' => $this->promised_date?->format('Y-m-d'),
            'completed_date' => $this->completed_date?->format('Y-m-d'),
            'delivered_date' => $this->delivered_date?->format('Y-m-d'),
            'is_overdue' => $this->is_overdue,
            'days_overdue' => $this->days_overdue,
            'days_in_progress' => $this->days_in_progress,
            'progress_percentage' => $this->progress_percentage,
            'assigned_to' => $this->assignedTo?->name,
            'location' => $this->location->name,
            'total_charges' => $this->total_charges,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
