<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PreventDuplicateSubmissions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only check POST, PUT, PATCH requests
        if (!in_array($request->method(), ['POST', 'PUT', 'PATCH'])) {
            return $next($request);
        }

        // Generate a unique key for this request
        $key = $this->generateRequestKey($request);
        
        // Check if this request was already processed recently
        if (Cache::has($key)) {
            Log::warning('Duplicate form submission prevented', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'key' => $key
            ]);
            
            // Return appropriate response based on request type
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Duplicate submission detected',
                    'message' => 'This request was already processed. Please wait before trying again.'
                ], 429);
            }
            
            return redirect()->back()
                ->with('error', 'Duplicate submission detected. Please wait before trying again.')
                ->withInput();
        }

        // Mark this request as being processed (expires in 30 seconds)
        Cache::put($key, true, 30);

        $response = $next($request);

        // If the request was successful, extend the cache to prevent duplicates
        // for a longer period (5 minutes)
        if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
            Cache::put($key, true, 300);
        } else {
            // If there was an error, remove the cache so user can retry immediately
            Cache::forget($key);
        }

        return $response;
    }

    /**
     * Generate a unique key for the request
     */
    private function generateRequestKey(Request $request): string
    {
        $components = [
            $request->method(),
            $request->path(),
            auth()->id() ?: $request->ip(),
        ];

        // For forms that create new records, include relevant form data
        if ($this->isCreateOperation($request)) {
            $relevantData = $this->getRelevantFormData($request);
            if (!empty($relevantData)) {
                $components[] = md5(serialize($relevantData));
            }
        }

        return 'duplicate_submission:' . md5(implode('|', $components));
    }

    /**
     * Check if this is a create operation
     */
    private function isCreateOperation(Request $request): bool
    {
        $path = $request->path();
        
        // Common patterns for create operations
        return $request->isMethod('POST') && (
            str_contains($path, '/store') ||
            str_contains($path, '/create') ||
            str_contains($path, 'saving-schemes') ||
            str_contains($path, 'customers') ||
            str_contains($path, 'payments') ||
            str_contains($path, 'plans')
        );
    }

    /**
     * Get relevant form data for duplicate detection
     */
    private function getRelevantFormData(Request $request): array
    {
        $relevantFields = [];
        $allData = $request->all();

        // Fields that commonly cause unique constraint violations
        $uniqueFields = [
            'email',
            'phone',
            'plan_code',
            'scheme_number',
            'payment_number',
            'transaction_number',
            'receipt_number',
            'customer_code',
            'employee_id',
            'sku',
            'barcode'
        ];

        foreach ($uniqueFields as $field) {
            if (isset($allData[$field]) && !empty($allData[$field])) {
                $relevantFields[$field] = $allData[$field];
            }
        }

        return $relevantFields;
    }
}
