<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('print_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('job_id', 50)->unique(); // Unique job identifier
            $table->foreignId('printer_configuration_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // User who initiated print

            // Print job details
            $table->enum('print_type', [
                'invoice', 'receipt', 'estimate', 'tag', 'label',
                'barcode', 'qr_code', 'report', 'custom'
            ]);
            $table->string('document_type', 100)->nullable(); // Specific document type
            $table->string('template_name', 100)->nullable(); // Template used

            // Related records
            $table->string('related_model_type')->nullable(); // Model class name
            $table->unsignedBigInteger('related_model_id')->nullable(); // Model ID
            $table->string('related_reference_number')->nullable(); // Invoice number, etc.

            // Print content
            $table->text('print_data')->nullable(); // JSON data for printing
            $table->longText('raw_content')->nullable(); // Raw ESC/POS commands
            $table->integer('copies')->default(1);

            // Print settings
            $table->json('print_settings')->nullable(); // Custom settings for this job
            $table->integer('paper_width')->nullable(); // Override paper width
            $table->boolean('auto_cut')->nullable(); // Override auto cut
            $table->integer('print_density')->nullable(); // Override density

            // Status and tracking
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->text('error_message')->nullable();
            $table->integer('retry_count')->default(0);
            $table->integer('max_retries')->default(3);

            // Timing
            $table->timestamp('queued_at')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->integer('processing_time_ms')->nullable(); // Processing time in milliseconds

            // Print quality and verification
            $table->boolean('print_verified')->default(false);
            $table->text('verification_notes')->nullable();
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('verified_at')->nullable();

            // File storage
            $table->string('preview_file_path')->nullable(); // Preview image/PDF
            $table->string('backup_file_path')->nullable(); // Backup of print content
            $table->integer('file_size_bytes')->nullable();

            // Analytics
            $table->json('print_analytics')->nullable(); // Print quality metrics, etc.
            $table->decimal('cost_estimate', 8, 4)->nullable(); // Estimated print cost
            $table->integer('pages_printed')->default(1);

            $table->timestamps();

            // Indexes
            $table->index(['printer_configuration_id', 'status']);
            $table->index(['user_id', 'created_at']);
            $table->index(['print_type', 'status']);
            $table->index(['related_model_type', 'related_model_id']);
            $table->index(['status', 'queued_at']);
            $table->index(['job_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('print_jobs');
    }
};
