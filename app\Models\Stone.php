<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Stone extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'unit',
        'base_rate_per_unit',
        'description',
        'is_active',
    ];

    protected $casts = [
        'base_rate_per_unit' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the products that use this stone.
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_stones')
                    ->withPivot(['weight', 'pieces', 'rate_per_unit', 'description'])
                    ->withTimestamps();
    }

    /**
     * Scope a query to only include active stones.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by stone type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }
}
