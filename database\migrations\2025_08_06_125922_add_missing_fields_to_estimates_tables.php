<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('estimates', function (Blueprint $table) {
            if (!Schema::hasColumn('estimates', 'total_gst')) {
                $table->decimal('total_gst', 15, 2)->default(0);
            }
            if (!Schema::hasColumn('estimates', 'terms_conditions')) {
                $table->text('terms_conditions')->nullable();
            }
            if (!Schema::hasColumn('estimates', 'status_notes')) {
                $table->text('status_notes')->nullable();
            }
            if (!Schema::hasColumn('estimates', 'status_updated_at')) {
                $table->timestamp('status_updated_at')->nullable();
            }
            if (!Schema::hasColumn('estimates', 'status_updated_by')) {
                $table->foreignId('status_updated_by')->nullable()->constrained('users');
            }
            if (!Schema::hasColumn('estimates', 'converted_to_invoice_id')) {
                $table->foreignId('converted_to_invoice_id')->nullable()->constrained('invoices');
            }
            if (!Schema::hasColumn('estimates', 'converted_at')) {
                $table->timestamp('converted_at')->nullable();
            }
            if (!Schema::hasColumn('estimates', 'converted_by')) {
                $table->foreignId('converted_by')->nullable()->constrained('users');
            }
            if (!Schema::hasColumn('estimates', 'extended_at')) {
                $table->timestamp('extended_at')->nullable();
            }
            if (!Schema::hasColumn('estimates', 'extended_by')) {
                $table->foreignId('extended_by')->nullable()->constrained('users');
            }
            if (!Schema::hasColumn('estimates', 'extension_notes')) {
                $table->text('extension_notes')->nullable();
            }
            if (!Schema::hasColumn('estimates', 'updated_by')) {
                $table->foreignId('updated_by')->nullable()->constrained('users');
            }
        });

        Schema::table('estimate_items', function (Blueprint $table) {
            if (!Schema::hasColumn('estimate_items', 'custom_item_name')) {
                $table->string('custom_item_name')->nullable();
            }
            if (!Schema::hasColumn('estimate_items', 'making_charges')) {
                $table->decimal('making_charges', 10, 2)->default(0);
            }
            if (!Schema::hasColumn('estimate_items', 'stone_charges')) {
                $table->decimal('stone_charges', 10, 2)->default(0);
            }
            if (!Schema::hasColumn('estimate_items', 'gst_rate')) {
                $table->decimal('gst_rate', 5, 2)->default(0);
            }
            if (!Schema::hasColumn('estimate_items', 'metal_rate_locked')) {
                $table->boolean('metal_rate_locked')->default(false);
            }
            if (!Schema::hasColumn('estimate_items', 'locked_metal_rate')) {
                $table->decimal('locked_metal_rate', 10, 2)->nullable();
            }
            if (!Schema::hasColumn('estimate_items', 'notes')) {
                $table->text('notes')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('estimates', function (Blueprint $table) {
            $table->dropColumn([
                'total_gst',
                'terms_conditions',
                'status_notes',
                'status_updated_at',
                'status_updated_by',
                'converted_to_invoice_id',
                'converted_at',
                'converted_by',
                'extended_at',
                'extended_by',
                'extension_notes',
                'updated_by',
            ]);
        });

        Schema::table('estimate_items', function (Blueprint $table) {
            $table->dropColumn([
                'custom_item_name',
                'making_charges',
                'stone_charges',
                'gst_rate',
                'metal_rate_locked',
                'locked_metal_rate',
                'notes',
            ]);
        });
    }
};
