<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update saving_schemes table structure
        Schema::table('saving_schemes', function (Blueprint $table) {
            // Add new columns that are expected by the models
            $table->foreignId('plan_id')->nullable()->constrained('saving_scheme_plans')->onDelete('cascade');
            $table->decimal('installment_amount', 10, 2)->nullable(); // Will be populated from monthly_amount
            $table->decimal('total_target_amount', 12, 2)->nullable();
            $table->decimal('interest_earned', 10, 2)->default(0);
            $table->decimal('penalty_amount', 10, 2)->default(0);
            $table->decimal('current_value', 12, 2)->default(0);
            $table->date('last_payment_date')->nullable();
            $table->date('next_due_date')->nullable();
            $table->integer('payments_completed')->default(0);
            $table->integer('payments_missed')->default(0);
            $table->text('closure_reason')->nullable();
            $table->date('closure_date')->nullable();
            $table->foreignId('closed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('cascade');
            $table->softDeletes();
        });

        // Update the status enum to match new values
        DB::statement("ALTER TABLE saving_schemes MODIFY COLUMN status ENUM('active', 'matured', 'closed', 'suspended', 'defaulted') DEFAULT 'active'");

        // Copy monthly_amount to installment_amount
        DB::statement("UPDATE saving_schemes SET installment_amount = monthly_amount WHERE installment_amount IS NULL");

        // Calculate total_target_amount (monthly_amount * duration_months)
        DB::statement("UPDATE saving_schemes SET total_target_amount = monthly_amount * duration_months WHERE total_target_amount IS NULL");

        // Set current_value to total_paid_amount
        DB::statement("UPDATE saving_schemes SET current_value = total_paid_amount WHERE current_value = 0");

        // Update scheme_payments table structure
        Schema::table('scheme_payments', function (Blueprint $table) {
            // Add new columns
            $table->string('payment_number')->unique()->nullable();
            $table->foreignId('scheme_id')->nullable()->constrained('saving_schemes')->onDelete('cascade');
            $table->date('due_date')->nullable();
            $table->decimal('late_fee', 8, 2)->default(0);
            $table->decimal('total_amount', 10, 2)->nullable(); // amount + late_fee
            $table->enum('payment_method', ['cash', 'card', 'upi', 'bank_transfer', 'cheque'])->nullable();
            $table->string('payment_reference')->nullable();
            $table->enum('status', ['pending', 'paid', 'overdue', 'waived'])->default('paid');
            $table->boolean('is_advance_payment')->default(false);
            $table->foreignId('received_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('paid_at')->nullable();
            $table->softDeletes();
        });

        // Copy data from old columns to new columns
        DB::statement("UPDATE scheme_payments SET payment_number = receipt_number WHERE payment_number IS NULL");
        DB::statement("UPDATE scheme_payments SET scheme_id = saving_scheme_id WHERE scheme_id IS NULL");
        DB::statement("UPDATE scheme_payments SET due_date = payment_date WHERE due_date IS NULL");
        DB::statement("UPDATE scheme_payments SET total_amount = amount WHERE total_amount IS NULL");
        DB::statement("UPDATE scheme_payments SET paid_at = created_at WHERE paid_at IS NULL");

        // Set payment_method based on payment_method_id (you may need to adjust this)
        DB::statement("UPDATE scheme_payments SET payment_method = 'cash' WHERE payment_method IS NULL");

        // Set payment_reference from reference_number
        DB::statement("UPDATE scheme_payments SET payment_reference = reference_number WHERE payment_reference IS NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('saving_schemes', function (Blueprint $table) {
            $table->dropForeign(['plan_id']);
            $table->dropForeign(['closed_by']);
            $table->dropForeign(['created_by']);
            $table->dropColumn([
                'plan_id', 'installment_amount', 'total_target_amount',
                'interest_earned', 'penalty_amount', 'current_value',
                'last_payment_date', 'next_due_date', 'payments_completed',
                'payments_missed', 'closure_reason', 'closure_date',
                'closed_by', 'created_by', 'deleted_at'
            ]);
        });

        Schema::table('scheme_payments', function (Blueprint $table) {
            $table->dropForeign(['scheme_id']);
            $table->dropForeign(['received_by']);
            $table->dropColumn([
                'payment_number', 'scheme_id', 'due_date', 'late_fee',
                'total_amount', 'payment_method', 'payment_reference',
                'status', 'is_advance_payment', 'received_by', 'paid_at',
                'deleted_at'
            ]);
        });
    }
};
