<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:system_settings');
    }

    /**
     * Display system settings
     */
    public function index()
    {
        $settings = $this->getSettings();
        
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update system settings
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            // Company Information
            'company_name' => 'required|string|max:255',
            'company_address' => 'required|string',
            'company_phone' => 'required|string|max:20',
            'company_email' => 'required|email|max:255',
            'company_website' => 'nullable|url|max:255',
            'company_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            
            // Business Settings
            'default_currency' => 'required|string|max:3',
            'currency_symbol' => 'required|string|max:5',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'making_charge_type' => 'required|in:percentage,fixed',
            'default_making_charge' => 'required|numeric|min:0',
            
            // Inventory Settings
            'low_stock_threshold' => 'required|integer|min:1',
            'auto_generate_sku' => 'boolean',
            'barcode_prefix' => 'nullable|string|max:10',
            
            // Invoice Settings
            'invoice_prefix' => 'required|string|max:10',
            'invoice_start_number' => 'required|integer|min:1',
            'estimate_prefix' => 'required|string|max:10',
            'estimate_start_number' => 'required|integer|min:1',
            
            // Email Settings
            'smtp_host' => 'nullable|string|max:255',
            'smtp_port' => 'nullable|integer',
            'smtp_username' => 'nullable|string|max:255',
            'smtp_password' => 'nullable|string|max:255',
            'smtp_encryption' => 'nullable|in:tls,ssl',
            
            // Notification Settings
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'low_stock_alerts' => 'boolean',
            'payment_reminders' => 'boolean',
            
            // System Settings
            'maintenance_mode' => 'boolean',
            'backup_frequency' => 'required|in:daily,weekly,monthly',
            'session_timeout' => 'required|integer|min:15|max:1440',
        ]);

        // Handle logo upload
        if ($request->hasFile('company_logo')) {
            $logoPath = $request->file('company_logo')->store('settings', 'public');
            $validated['company_logo'] = $logoPath;
        }

        // Save settings to cache and database
        foreach ($validated as $key => $value) {
            $this->setSetting($key, $value);
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully.');
    }

    /**
     * Get all settings
     */
    private function getSettings()
    {
        return [
            // Company Information
            'company_name' => $this->getSetting('company_name', 'JewelSoft'),
            'company_address' => $this->getSetting('company_address', ''),
            'company_phone' => $this->getSetting('company_phone', ''),
            'company_email' => $this->getSetting('company_email', ''),
            'company_website' => $this->getSetting('company_website', ''),
            'company_logo' => $this->getSetting('company_logo', ''),
            
            // Business Settings
            'default_currency' => $this->getSetting('default_currency', 'INR'),
            'currency_symbol' => $this->getSetting('currency_symbol', '₹'),
            'tax_rate' => $this->getSetting('tax_rate', 3.0),
            'making_charge_type' => $this->getSetting('making_charge_type', 'percentage'),
            'default_making_charge' => $this->getSetting('default_making_charge', 10.0),
            
            // Inventory Settings
            'low_stock_threshold' => $this->getSetting('low_stock_threshold', 5),
            'auto_generate_sku' => $this->getSetting('auto_generate_sku', true),
            'barcode_prefix' => $this->getSetting('barcode_prefix', 'JS'),
            
            // Invoice Settings
            'invoice_prefix' => $this->getSetting('invoice_prefix', 'INV'),
            'invoice_start_number' => $this->getSetting('invoice_start_number', 1),
            'estimate_prefix' => $this->getSetting('estimate_prefix', 'EST'),
            'estimate_start_number' => $this->getSetting('estimate_start_number', 1),
            
            // Email Settings
            'smtp_host' => $this->getSetting('smtp_host', ''),
            'smtp_port' => $this->getSetting('smtp_port', 587),
            'smtp_username' => $this->getSetting('smtp_username', ''),
            'smtp_password' => $this->getSetting('smtp_password', ''),
            'smtp_encryption' => $this->getSetting('smtp_encryption', 'tls'),
            
            // Notification Settings
            'email_notifications' => $this->getSetting('email_notifications', true),
            'sms_notifications' => $this->getSetting('sms_notifications', false),
            'low_stock_alerts' => $this->getSetting('low_stock_alerts', true),
            'payment_reminders' => $this->getSetting('payment_reminders', true),
            
            // System Settings
            'maintenance_mode' => $this->getSetting('maintenance_mode', false),
            'backup_frequency' => $this->getSetting('backup_frequency', 'daily'),
            'session_timeout' => $this->getSetting('session_timeout', 120),
        ];
    }

    /**
     * Get a setting value
     */
    private function getSetting($key, $default = null)
    {
        return Cache::remember("setting_{$key}", 3600, function () use ($key, $default) {
            // In a real application, you would fetch from a settings table
            // For now, we'll use cache with defaults
            return $default;
        });
    }

    /**
     * Set a setting value
     */
    private function setSetting($key, $value)
    {
        Cache::put("setting_{$key}", $value, 3600);
        // In a real application, you would also save to a settings table
    }

    /**
     * Clear settings cache
     */
    public function clearCache()
    {
        Cache::flush();
        
        return response()->json([
            'success' => true,
            'message' => 'Settings cache cleared successfully.'
        ]);
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $validated = $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            // Test email sending logic would go here
            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Backup system data
     */
    public function backup()
    {
        try {
            // Backup logic would go here
            return response()->json([
                'success' => true,
                'message' => 'System backup created successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create backup: ' . $e->getMessage()
            ]);
        }
    }
}
