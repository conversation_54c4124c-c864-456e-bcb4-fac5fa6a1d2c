<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExpenseCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'account_id',
        'is_active',
        'budget_limit',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'budget_limit' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            $category->created_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(ChartOfAccount::class, 'account_id');
    }

    public function expenses(): HasMany
    {
        return $this->hasMany(Expense::class, 'category_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeWithBudget($query)
    {
        return $query->whereNotNull('budget_limit');
    }

    /**
     * Accessors
     */
    public function getFormattedBudgetAttribute(): string
    {
        return $this->budget_limit ? '₹' . number_format($this->budget_limit, 2) : 'No Limit';
    }

    public function getTotalExpensesThisMonthAttribute(): float
    {
        return $this->expenses()
                   ->thisMonth()
                   ->where('status', 'paid')
                   ->sum('total_amount');
    }

    public function getBudgetUtilizationAttribute(): float
    {
        if (!$this->budget_limit) {
            return 0;
        }

        return ($this->total_expenses_this_month / $this->budget_limit) * 100;
    }

    public function getIsBudgetExceededAttribute(): bool
    {
        return $this->budget_limit && $this->total_expenses_this_month > $this->budget_limit;
    }

    public function getRemainingBudgetAttribute(): float
    {
        if (!$this->budget_limit) {
            return 0;
        }

        return max(0, $this->budget_limit - $this->total_expenses_this_month);
    }

    /**
     * Business Logic Methods
     */
    public function activate(): self
    {
        $this->update(['is_active' => true]);
        return $this;
    }

    public function deactivate(): self
    {
        $this->update(['is_active' => false]);
        return $this;
    }

    public function setBudget($amount): self
    {
        $this->update(['budget_limit' => $amount]);
        return $this;
    }

    public function removeBudget(): self
    {
        $this->update(['budget_limit' => null]);
        return $this;
    }

    public function getExpensesSummary($startDate = null, $endDate = null): array
    {
        $query = $this->expenses()->where('status', 'paid');

        if ($startDate) {
            $query->where('expense_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('expense_date', '<=', $endDate);
        }

        return [
            'total_amount' => $query->sum('total_amount'),
            'count' => $query->count(),
            'average_amount' => $query->avg('total_amount') ?: 0,
        ];
    }

    public static function createDefaultCategories(): void
    {
        $defaultCategories = [
            ['name' => 'Rent & Utilities', 'description' => 'Office rent, electricity, water, internet'],
            ['name' => 'Salaries & Benefits', 'description' => 'Employee salaries, benefits, bonuses'],
            ['name' => 'Marketing & Advertising', 'description' => 'Promotional activities, advertisements'],
            ['name' => 'Office Supplies', 'description' => 'Stationery, equipment, maintenance'],
            ['name' => 'Travel & Transportation', 'description' => 'Business travel, fuel, vehicle maintenance'],
            ['name' => 'Professional Services', 'description' => 'Legal, accounting, consulting fees'],
            ['name' => 'Insurance', 'description' => 'Business insurance premiums'],
            ['name' => 'Taxes & Licenses', 'description' => 'Government fees, licenses, permits'],
            ['name' => 'Inventory Purchase', 'description' => 'Raw materials, finished goods'],
            ['name' => 'Equipment & Technology', 'description' => 'Computers, software, machinery'],
            ['name' => 'Miscellaneous', 'description' => 'Other business expenses'],
        ];

        foreach ($defaultCategories as $category) {
            static::firstOrCreate(
                ['name' => $category['name']],
                array_merge($category, ['is_active' => true])
            );
        }
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'account_name' => $this->account?->account_name,
            'is_active' => $this->is_active,
            'budget_limit' => $this->budget_limit,
            'formatted_budget' => $this->formatted_budget,
            'total_expenses_this_month' => $this->total_expenses_this_month,
            'budget_utilization' => $this->budget_utilization,
            'is_budget_exceeded' => $this->is_budget_exceeded,
            'remaining_budget' => $this->remaining_budget,
            'expenses_count' => $this->expenses()->count(),
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
