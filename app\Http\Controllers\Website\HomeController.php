<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Location;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class HomeController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        // Get featured products
        $featuredProducts = Cache::remember('featured_products', 3600, function () {
            return Product::with(['category', 'images', 'inventory'])
                         ->where('is_active', true)
                         ->where('is_featured', true)
                         ->whereHas('inventory', function ($query) {
                             $query->where('quantity_available', '>', 0);
                         })
                         ->limit(8)
                         ->get();
        });

        // Get new arrivals
        $newArrivals = Cache::remember('new_arrivals', 1800, function () {
            return Product::with(['category', 'images', 'inventory'])
                         ->where('is_active', true)
                         ->whereHas('inventory', function ($query) {
                             $query->where('quantity_available', '>', 0);
                         })
                         ->orderBy('created_at', 'desc')
                         ->limit(6)
                         ->get();
        });

        // Get categories
        $categories = Cache::remember('main_categories', 3600, function () {
            return Category::where('is_active', true)
                          ->whereNull('parent_id')
                          ->withCount(['products' => function ($query) {
                              $query->where('is_active', true)
                                    ->whereHas('inventory', function ($subQuery) {
                                        $subQuery->where('quantity_available', '>', 0);
                                    });
                          }])
                          ->orderBy('sort_order')
                          ->get();
        });

        // Get testimonials/reviews
        $testimonials = $this->getTestimonials();

        // Get store locations
        $locations = Location::where('is_active', true)
                            ->orderBy('is_main_branch', 'desc')
                            ->limit(3)
                            ->get();

        return view('website.home', compact(
            'featuredProducts',
            'newArrivals',
            'categories',
            'testimonials',
            'locations'
        ));
    }

    /**
     * Display about us page
     */
    public function about()
    {
        $locations = Location::where('is_active', true)
                            ->orderBy('is_main_branch', 'desc')
                            ->get();

        $stats = [
            'years_experience' => 25,
            'happy_customers' => Customer::count(),
            'products_sold' => $this->getTotalProductsSold(),
            'store_locations' => $locations->count(),
        ];

        return view('website.about', compact('locations', 'stats'));
    }

    /**
     * Display contact page
     */
    public function contact()
    {
        $locations = Location::where('is_active', true)
                            ->orderBy('is_main_branch', 'desc')
                            ->get();

        return view('website.contact', compact('locations'));
    }

    /**
     * Handle contact form submission
     */
    public function submitContact(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'email' => 'required|email|max:100',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:200',
            'message' => 'required|string|max:1000',
        ]);

        try {
            // Create customer interaction for contact form
            $customer = Customer::where('email', $validated['email'])->first();

            if (!$customer) {
                // Create a basic customer record for contact form
                $customer = Customer::create([
                    'first_name' => $validated['name'],
                    'email' => $validated['email'],
                    'phone' => $validated['phone'],
                    'customer_type' => 'individual',
                    'source' => 'website_contact',
                ]);
            }

            // Create interaction record
            $customer->addInteraction(
                'inquiry',
                $validated['subject'],
                $validated['message'],
                [
                    'source' => 'website_contact_form',
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]
            );

            // Send notification email to admin (implement as needed)
            // Mail::to(config('mail.admin_email'))->send(new ContactFormMail($validated));

            return back()->with('success', 'Thank you for your message! We will get back to you soon.');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Sorry, there was an error sending your message. Please try again.');
        }
    }

    /**
     * Display services page
     */
    public function services()
    {
        $services = [
            [
                'title' => 'Custom Jewelry Design',
                'description' => 'Create unique, personalized jewelry pieces tailored to your vision.',
                'icon' => 'fas fa-gem',
                'features' => ['3D Design Preview', 'Expert Consultation', 'Premium Materials', 'Lifetime Warranty']
            ],
            [
                'title' => 'Jewelry Repair',
                'description' => 'Professional repair services for all types of jewelry.',
                'icon' => 'fas fa-tools',
                'features' => ['Ring Resizing', 'Stone Setting', 'Chain Repair', 'Polishing & Cleaning']
            ],
            [
                'title' => 'Appraisal Services',
                'description' => 'Certified jewelry appraisals for insurance and resale.',
                'icon' => 'fas fa-certificate',
                'features' => ['Certified Appraisers', 'Insurance Documentation', 'Market Valuation', 'Quick Turnaround']
            ],
            [
                'title' => 'Engraving',
                'description' => 'Personalize your jewelry with custom engraving.',
                'icon' => 'fas fa-pen-fancy',
                'features' => ['Hand Engraving', 'Laser Engraving', 'Multiple Fonts', 'Symbol Options']
            ]
        ];

        return view('website.services', compact('services'));
    }

    /**
     * Search functionality
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (empty($query)) {
            return redirect()->route('website.products');
        }

        $products = Product::with(['category', 'images', 'inventory'])
                          ->where('is_active', true)
                          ->whereHas('inventory', function ($query) {
                              $query->where('quantity_available', '>', 0);
                          })
                          ->where(function ($q) use ($query) {
                              $q->where('name', 'like', "%{$query}%")
                                ->orWhere('description', 'like', "%{$query}%")
                                ->orWhere('sku', 'like', "%{$query}%")
                                ->orWhereHas('category', function ($cq) use ($query) {
                                    $cq->where('name', 'like', "%{$query}%");
                                });
                          })
                          ->paginate(12);

        return view('website.search', compact('products', 'query'));
    }

    /**
     * Get testimonials
     */
    protected function getTestimonials()
    {
        return [
            [
                'name' => 'Sarah Johnson',
                'rating' => 5,
                'comment' => 'Absolutely beautiful jewelry! The custom engagement ring exceeded all expectations.',
                'image' => null,
                'verified' => true
            ],
            [
                'name' => 'Michael Chen',
                'rating' => 5,
                'comment' => 'Excellent service and quality. The repair work on my vintage watch was perfect.',
                'image' => null,
                'verified' => true
            ],
            [
                'name' => 'Priya Sharma',
                'rating' => 5,
                'comment' => 'Love shopping here! Great selection and the staff is very knowledgeable.',
                'image' => null,
                'verified' => true
            ]
        ];
    }

    /**
     * Get total products sold
     */
    protected function getTotalProductsSold()
    {
        // This would calculate from invoice items or POS transactions
        return 15000; // Placeholder
    }
}
