<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Product;
use App\Models\ProductImage;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\Metal;

class WebsiteDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create metals first
        $metals = [
            [
                'name' => 'Gold',
                'symbol' => 'Au',
                'purity' => '22K',
                'current_rate_per_gram' => 6500.00,
                'is_active' => true,
            ],
            [
                'name' => 'Gold',
                'symbol' => 'Au',
                'purity' => '18K',
                'current_rate_per_gram' => 5200.00,
                'is_active' => true,
            ],
            [
                'name' => 'Silver',
                'symbol' => 'Ag',
                'purity' => '925',
                'current_rate_per_gram' => 85.00,
                'is_active' => true,
            ],
        ];

        foreach ($metals as $metalData) {
            Metal::firstOrCreate(
                [
                    'name' => $metalData['name'],
                    'purity' => $metalData['purity']
                ],
                $metalData
            );
        }

        // Create categories
        $categories = [
            [
                'name' => 'Rings',
                'slug' => 'rings',
                'description' => 'Beautiful rings for every occasion',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Necklaces',
                'slug' => 'necklaces',
                'description' => 'Elegant necklaces to complement your style',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Earrings',
                'slug' => 'earrings',
                'description' => 'Stunning earrings for every taste',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Bracelets',
                'slug' => 'bracelets',
                'description' => 'Stylish bracelets and bangles',
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }

        // Create sample locations first (needed for inventory)
        $locations = [
            [
                'name' => 'JewelSoft Main Store',
                'code' => 'MAIN',
                'address' => '123 Jewelry Street, Bandra West, Mumbai, Maharashtra 400050',
                'phone' => '+91 98765 43210',
                'manager_name' => 'Rajesh Kumar',
                'is_main_branch' => true,
                'is_active' => true,
            ],
            [
                'name' => 'JewelSoft Andheri',
                'code' => 'AND',
                'address' => '456 Gold Plaza, Andheri East, Mumbai, Maharashtra 400069',
                'phone' => '+91 98765 43211',
                'manager_name' => 'Priya Sharma',
                'is_main_branch' => false,
                'is_active' => true,
            ],
            [
                'name' => 'JewelSoft Pune',
                'code' => 'PUNE',
                'address' => '789 Diamond Mall, Koregaon Park, Pune, Maharashtra 411001',
                'phone' => '+91 98765 43212',
                'manager_name' => 'Amit Patel',
                'is_main_branch' => false,
                'is_active' => true,
            ],
        ];

        foreach ($locations as $locationData) {
            Location::firstOrCreate(
                ['name' => $locationData['name']],
                $locationData
            );
        }

        // Create sample products
        $products = [
            [
                'name' => 'Diamond Solitaire Ring',
                'sku' => 'DSR001',
                'description' => 'Classic diamond solitaire ring in 18k white gold',
                'category_slug' => 'rings',
                'gross_weight' => 3.5,
                'net_weight' => 3.2,
                'making_charge_fixed' => 5000,
                'other_charges' => 1000,
                'metal_purity' => '18K',
                'is_active' => true,
                'is_featured' => true,
                'stock_quantity' => 5,
            ],
            [
                'name' => 'Gold Chain Necklace',
                'sku' => 'GCN001',
                'description' => 'Elegant 22k gold chain necklace',
                'category_slug' => 'necklaces',
                'gross_weight' => 15.2,
                'net_weight' => 14.8,
                'making_charge_fixed' => 3000,
                'other_charges' => 500,
                'metal_purity' => '22K',
                'is_active' => true,
                'is_featured' => true,
                'stock_quantity' => 8,
            ],
            [
                'name' => 'Pearl Drop Earrings',
                'sku' => 'PDE001',
                'description' => 'Beautiful pearl drop earrings in silver',
                'category_slug' => 'earrings',
                'gross_weight' => 2.1,
                'net_weight' => 1.9,
                'making_charge_fixed' => 800,
                'other_charges' => 200,
                'is_active' => true,
                'is_featured' => false,
                'stock_quantity' => 12,
            ],
            [
                'name' => 'Diamond Tennis Bracelet',
                'sku' => 'DTB001',
                'description' => 'Stunning diamond tennis bracelet in white gold',
                'category_slug' => 'bracelets',
                'gross_weight' => 12.8,
                'net_weight' => 12.0,
                'making_charge_fixed' => 8000,
                'other_charges' => 2000,
                'is_active' => true,
                'is_featured' => true,
                'stock_quantity' => 3,
            ],
            [
                'name' => 'Emerald Engagement Ring',
                'sku' => 'EER001',
                'description' => 'Vintage-style emerald engagement ring',
                'category_slug' => 'rings',
                'gross_weight' => 4.2,
                'net_weight' => 3.8,
                'making_charge_fixed' => 6000,
                'other_charges' => 1500,
                'is_active' => true,
                'is_featured' => true,
                'stock_quantity' => 2,
            ],
            [
                'name' => 'Silver Hoop Earrings',
                'sku' => 'SHE001',
                'description' => 'Classic silver hoop earrings',
                'category_slug' => 'earrings',
                'gross_weight' => 1.8,
                'net_weight' => 1.6,
                'making_charge_fixed' => 400,
                'other_charges' => 100,
                'is_active' => true,
                'is_featured' => false,
                'stock_quantity' => 20,
            ],
            [
                'name' => 'Ruby Pendant Necklace',
                'sku' => 'RPN001',
                'description' => 'Elegant ruby pendant on gold chain',
                'category_slug' => 'necklaces',
                'gross_weight' => 8.5,
                'net_weight' => 8.0,
                'making_charge_fixed' => 2500,
                'other_charges' => 800,
                'is_active' => true,
                'is_featured' => false,
                'stock_quantity' => 6,
            ],
            [
                'name' => 'Gold Bangle Set',
                'sku' => 'GBS001',
                'description' => 'Traditional gold bangle set (pair)',
                'category_slug' => 'bracelets',
                'gross_weight' => 25.6,
                'net_weight' => 24.8,
                'making_charge_fixed' => 4000,
                'other_charges' => 1000,
                'is_active' => true,
                'is_featured' => false,
                'stock_quantity' => 4,
            ],
        ];

        foreach ($products as $productData) {
            $category = Category::where('slug', $productData['category_slug'])->first();
            if (!$category) continue;

            // Find the appropriate metal
            $metal = null;
            if (isset($productData['metal_purity'])) {
                $metal = Metal::where('purity', $productData['metal_purity'])->first();
            }

            $product = Product::firstOrCreate(
                ['sku' => $productData['sku']],
                [
                    'name' => $productData['name'],
                    'description' => $productData['description'],
                    'category_id' => $category->id,
                    'metal_id' => $metal ? $metal->id : 1, // Default to first metal if not found
                    'gross_weight' => $productData['gross_weight'],
                    'net_weight' => $productData['net_weight'],
                    'making_charge_fixed' => $productData['making_charge_fixed'],
                    'other_charges' => $productData['other_charges'],
                    'is_active' => $productData['is_active'],
                    'is_featured' => $productData['is_featured'],
                ]
            );

            // Create inventory record
            Inventory::firstOrCreate(
                ['product_id' => $product->id],
                [
                    'quantity_available' => $productData['stock_quantity'],
                    'quantity_reserved' => 0,
                    'minimum_stock_level' => 2,
                    'location_id' => 1,
                ]
            );

            // Add sample product images (using Unsplash jewelry images)
            $imageUrls = [
                'rings' => [
                    'https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                ],
                'necklaces' => [
                    'https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    'https://images.unsplash.com/photo-1573408301185-9146fe634ad0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                ],
                'earrings' => [
                    'https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    'https://images.unsplash.com/photo-1611652022419-a9419f74343d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                ],
                'bracelets' => [
                    'https://images.unsplash.com/photo-1611591437281-460bfbe1220a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                ],
            ];

            $categoryImages = $imageUrls[$productData['category_slug']] ?? $imageUrls['rings'];
            $imageUrl = $categoryImages[array_rand($categoryImages)];

            ProductImage::firstOrCreate(
                [
                    'product_id' => $product->id,
                    'image_path' => $imageUrl,
                ],
                [
                    'alt_text' => $product->name,
                    'is_primary' => true,
                    'sort_order' => 1,
                ]
            );
        }



        $this->command->info('Website sample data seeded successfully!');
    }
}
