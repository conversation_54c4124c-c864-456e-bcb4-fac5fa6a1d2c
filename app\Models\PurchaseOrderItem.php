<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PurchaseOrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_order_id',
        'product_id',
        'quantity_ordered',
        'quantity_received',
        'unit_price',
        'total_price',
        'notes',
    ];

    protected $casts = [
        'quantity_ordered' => 'decimal:3',
        'quantity_received' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_price = $item->quantity_ordered * $item->unit_price;
        });
    }

    /**
     * Relationships
     */
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function goodsReceiptNoteItems(): HasMany
    {
        return $this->hasMany(GoodsReceiptNoteItem::class);
    }

    /**
     * Accessors
     */
    public function getPendingQuantityAttribute()
    {
        return max(0, $this->quantity_ordered - $this->quantity_received);
    }

    public function getReceiptPercentageAttribute()
    {
        if ($this->quantity_ordered == 0) {
            return 0;
        }

        return round(($this->quantity_received / $this->quantity_ordered) * 100, 2);
    }

    public function getIsFullyReceivedAttribute()
    {
        return $this->quantity_received >= $this->quantity_ordered;
    }

    public function getIsPartiallyReceivedAttribute()
    {
        return $this->quantity_received > 0 && $this->quantity_received < $this->quantity_ordered;
    }

    public function getReceiptStatusAttribute()
    {
        if ($this->quantity_received == 0) {
            return 'pending';
        } elseif ($this->quantity_received >= $this->quantity_ordered) {
            return 'completed';
        } else {
            return 'partial';
        }
    }

    public function getReceiptStatusDisplayAttribute()
    {
        $statuses = [
            'pending' => 'Pending',
            'partial' => 'Partially Received',
            'completed' => 'Fully Received',
        ];

        return $statuses[$this->receipt_status] ?? 'Unknown';
    }

    public function getReceiptStatusColorAttribute()
    {
        $colors = [
            'pending' => 'red',
            'partial' => 'yellow',
            'completed' => 'green',
        ];

        return $colors[$this->receipt_status] ?? 'gray';
    }

    /**
     * Business Logic Methods
     */
    public function receiveQuantity($quantity, $qualityRating = null, $notes = null)
    {
        if ($quantity <= 0) {
            throw new \Exception('Received quantity must be greater than 0');
        }

        if (($this->quantity_received + $quantity) > $this->quantity_ordered) {
            throw new \Exception('Cannot receive more than ordered quantity');
        }

        $this->increment('quantity_received', $quantity);

        // Log the receipt
        activity()
            ->performedOn($this)
            ->withProperties([
                'quantity_received' => $quantity,
                'quality_rating' => $qualityRating,
                'notes' => $notes,
            ])
            ->log('Quantity received for purchase order item');

        return $this;
    }

    public function updateQuantityOrdered($newQuantity)
    {
        if ($newQuantity < $this->quantity_received) {
            throw new \Exception('Cannot reduce ordered quantity below received quantity');
        }

        $oldQuantity = $this->quantity_ordered;
        $this->update([
            'quantity_ordered' => $newQuantity,
            'total_price' => $newQuantity * $this->unit_price,
        ]);

        // Log the change
        activity()
            ->performedOn($this)
            ->withProperties([
                'old_quantity' => $oldQuantity,
                'new_quantity' => $newQuantity,
            ])
            ->log('Purchase order item quantity updated');

        return $this;
    }

    public function updateUnitPrice($newPrice)
    {
        $oldPrice = $this->unit_price;
        $this->update([
            'unit_price' => $newPrice,
            'total_price' => $this->quantity_ordered * $newPrice,
        ]);

        // Log the change
        activity()
            ->performedOn($this)
            ->withProperties([
                'old_price' => $oldPrice,
                'new_price' => $newPrice,
            ])
            ->log('Purchase order item price updated');

        return $this;
    }

    public function getReceiptHistory()
    {
        return $this->goodsReceiptNoteItems()
                   ->with(['goodsReceiptNote'])
                   ->orderBy('created_at', 'desc')
                   ->get();
    }

    public function getTotalReceivedValue()
    {
        return $this->quantity_received * $this->unit_price;
    }

    public function getPendingValue()
    {
        return $this->pending_quantity * $this->unit_price;
    }

    public function getSummary()
    {
        return [
            'id' => $this->id,
            'product_name' => $this->product->name,
            'product_code' => $this->product->product_code,
            'quantity_ordered' => $this->quantity_ordered,
            'quantity_received' => $this->quantity_received,
            'pending_quantity' => $this->pending_quantity,
            'unit_price' => $this->unit_price,
            'total_price' => $this->total_price,
            'receipt_status' => $this->receipt_status_display,
            'receipt_percentage' => $this->receipt_percentage,
            'total_received_value' => $this->getTotalReceivedValue(),
            'pending_value' => $this->getPendingValue(),
            'notes' => $this->notes,
        ];
    }
}
