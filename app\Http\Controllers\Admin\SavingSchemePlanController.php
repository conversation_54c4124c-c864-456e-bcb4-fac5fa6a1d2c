<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SavingSchemePlan;
use App\Services\UniqueConstraintService;
use App\Rules\UniqueWithSuggestions;
use Illuminate\Http\Request;
use Illuminate\Database\QueryException;
use Illuminate\Database\UniqueConstraintViolationException;

class SavingSchemePlanController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_saving_scheme_plans')->only(['index', 'show']);
        $this->middleware('permission:manage_saving_scheme_plans')->only(['create', 'store', 'edit', 'update', 'destroy']);
    }

    /**
     * Display a listing of saving scheme plans
     */
    public function index(Request $request)
    {
        $query = SavingSchemePlan::with(['createdBy', 'savingSchemes']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('plan_name', 'like', "%{$search}%")
                  ->orWhere('plan_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by plan type
        if ($request->filled('plan_type')) {
            $query->where('plan_type', $request->plan_type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $plans = $query->paginate(20);

        $planTypes = [
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
            'yearly' => 'Yearly',
            'custom' => 'Custom',
        ];

        $statuses = [
            'active' => 'Active',
            'inactive' => 'Inactive',
        ];

        return view('admin.saving-scheme-plans.index', compact('plans', 'planTypes', 'statuses'));
    }

    /**
     * Show the form for creating a new plan
     */
    public function create()
    {
        return view('admin.saving-scheme-plans.create');
    }

    /**
     * Store a newly created plan
     */
    public function store(Request $request)
    {
        // Pre-validate unique constraints with suggestions
        $uniqueViolations = UniqueConstraintService::validateUniqueFields(
            $request->all(),
            'saving_scheme_plans',
            ['plan_name', 'plan_code']
        );

        if (!empty($uniqueViolations)) {
            $errors = [];
            foreach ($uniqueViolations as $field => $violation) {
                $errors[$field] = $violation['message'];
            }
            
            return back()->withInput()
                        ->withErrors($errors)
                        ->with('unique_suggestions', $uniqueViolations);
        }

        $validated = $request->validate([
            'plan_name' => ['required', 'string', 'max:100', new UniqueWithSuggestions('saving_scheme_plans')],
            'description' => 'nullable|string|max:500',
            'plan_type' => 'required|in:monthly,quarterly,yearly,custom',
            'duration_months' => 'required|integer|min:1|max:120',
            'minimum_amount' => 'required|numeric|min:1',
            'maximum_amount' => 'nullable|numeric|min:1',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'bonus_percentage' => 'required|numeric|min:0|max:100',
            'allow_partial_withdrawal' => 'boolean',
            'allow_premature_closure' => 'boolean',
            'premature_closure_penalty' => 'required|numeric|min:0|max:50',
            'grace_period_days' => 'required|integer|min:0|max:30',
            'late_fee_amount' => 'required|numeric|min:0',
            'terms_conditions' => 'nullable|array',
        ]);

        // Validate maximum amount
        if ($validated['maximum_amount'] && $validated['maximum_amount'] <= $validated['minimum_amount']) {
            return back()->withInput()
                        ->with('error', 'Maximum amount must be greater than minimum amount.');
        }

        // Use the UniqueConstraintService to execute with retry logic
        try {
            $plan = UniqueConstraintService::executeWithRetry(function () use ($validated) {
                return SavingSchemePlan::create($validated);
            });

            return redirect()->route('admin.saving-scheme-plans.show', $plan)
                           ->with('success', 'Saving scheme plan created successfully.');

        } catch (QueryException|UniqueConstraintViolationException $e) {
            // This should be handled by the global exception handler, but just in case
            if (UniqueConstraintService::isUniqueConstraintViolation($e)) {
                return back()->withInput()
                            ->with('error', 'A plan with similar details already exists. Please check the plan name and try again.');
            }
            
            return back()->withInput()
                        ->with('error', 'Database error: ' . $e->getMessage());
                        
        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Failed to create plan: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified plan
     */
    public function show(SavingSchemePlan $savingSchemePlan)
    {
        $savingSchemePlan->load(['createdBy', 'savingSchemes.customer']);

        // Calculate plan statistics
        $stats = [
            'total_schemes' => $savingSchemePlan->savingSchemes()->count(),
            'active_schemes' => $savingSchemePlan->savingSchemes()->where('status', 'active')->count(),
            'matured_schemes' => $savingSchemePlan->savingSchemes()->where('status', 'matured')->count(),
            'total_value' => $savingSchemePlan->savingSchemes()->sum('current_value'),
            'average_installment' => $savingSchemePlan->savingSchemes()->avg('installment_amount'),
        ];

        return view('admin.saving-scheme-plans.show', compact('savingSchemePlan', 'stats'));
    }

    /**
     * Show the form for editing the plan
     */
    public function edit(SavingSchemePlan $savingSchemePlan)
    {
        return view('admin.saving-scheme-plans.edit', compact('savingSchemePlan'));
    }

    /**
     * Update the specified plan
     */
    public function update(Request $request, SavingSchemePlan $savingSchemePlan)
    {
        $validated = $request->validate([
            'plan_name' => 'required|string|max:100|unique:saving_scheme_plans,plan_name,' . $savingSchemePlan->id,
            'description' => 'nullable|string|max:500',
            'minimum_amount' => 'required|numeric|min:1',
            'maximum_amount' => 'nullable|numeric|min:1',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'bonus_percentage' => 'required|numeric|min:0|max:100',
            'allow_partial_withdrawal' => 'boolean',
            'allow_premature_closure' => 'boolean',
            'premature_closure_penalty' => 'required|numeric|min:0|max:50',
            'grace_period_days' => 'required|integer|min:0|max:30',
            'late_fee_amount' => 'required|numeric|min:0',
            'terms_conditions' => 'nullable|array',
        ]);

        // Validate maximum amount
        if ($validated['maximum_amount'] && $validated['maximum_amount'] <= $validated['minimum_amount']) {
            return back()->withInput()
                        ->with('error', 'Maximum amount must be greater than minimum amount.');
        }

        try {
            $savingSchemePlan->update($validated);

            return redirect()->route('admin.saving-scheme-plans.show', $savingSchemePlan)
                           ->with('success', 'Plan updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Failed to update plan: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified plan
     */
    public function destroy(SavingSchemePlan $savingSchemePlan)
    {
        // Check if plan has active schemes
        if ($savingSchemePlan->savingSchemes()->whereIn('status', ['active', 'suspended'])->exists()) {
            return back()->with('error', 'Cannot delete plan with active schemes.');
        }

        try {
            $savingSchemePlan->delete();

            return redirect()->route('admin.saving-scheme-plans.index')
                           ->with('success', 'Plan deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete plan: ' . $e->getMessage());
        }
    }

    /**
     * Activate plan
     */
    public function activate(SavingSchemePlan $savingSchemePlan)
    {
        try {
            $savingSchemePlan->activate();

            return response()->json([
                'success' => true,
                'message' => 'Plan activated successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Deactivate plan
     */
    public function deactivate(SavingSchemePlan $savingSchemePlan)
    {
        try {
            $savingSchemePlan->deactivate();

            return response()->json([
                'success' => true,
                'message' => 'Plan deactivated successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }
}
