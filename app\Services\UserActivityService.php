<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class UserActivityService
{
    /**
     * Log user creation activity
     */
    public static function logUserCreated(User $user, User $createdBy = null): void
    {
        $createdBy = $createdBy ?? Auth::user();
        
        Log::channel('user_activity')->info('User created', [
            'action' => 'user_created',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'created_by_id' => $createdBy?->id,
            'created_by_email' => $createdBy?->email,
            'user_role' => $user->getRoleNames()->first(),
            'user_location' => $user->location?->name,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Log user update activity
     */
    public static function logUserUpdated(User $user, array $changes, User $updatedBy = null): void
    {
        $updatedBy = $updatedBy ?? Auth::user();
        
        // Filter sensitive information
        $filteredChanges = collect($changes)->except([
            'password', 'two_factor_secret', 'two_factor_recovery_codes'
        ])->toArray();

        Log::channel('user_activity')->info('User updated', [
            'action' => 'user_updated',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'updated_by_id' => $updatedBy?->id,
            'updated_by_email' => $updatedBy?->email,
            'changes' => $filteredChanges,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Log user deletion activity
     */
    public static function logUserDeleted(User $user, User $deletedBy = null): void
    {
        $deletedBy = $deletedBy ?? Auth::user();
        
        Log::channel('user_activity')->warning('User deleted', [
            'action' => 'user_deleted',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'deleted_by_id' => $deletedBy?->id,
            'deleted_by_email' => $deletedBy?->email,
            'user_role' => $user->getRoleNames()->first(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Log role assignment activity
     */
    public static function logRoleAssigned(User $user, array $roles, User $assignedBy = null): void
    {
        $assignedBy = $assignedBy ?? Auth::user();
        
        Log::channel('user_activity')->info('User roles assigned', [
            'action' => 'roles_assigned',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'assigned_by_id' => $assignedBy?->id,
            'assigned_by_email' => $assignedBy?->email,
            'roles' => $roles,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Log user status change activity
     */
    public static function logStatusChanged(User $user, bool $newStatus, User $changedBy = null): void
    {
        $changedBy = $changedBy ?? Auth::user();
        
        Log::channel('user_activity')->info('User status changed', [
            'action' => 'status_changed',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'changed_by_id' => $changedBy?->id,
            'changed_by_email' => $changedBy?->email,
            'old_status' => !$newStatus,
            'new_status' => $newStatus,
            'status_text' => $newStatus ? 'activated' : 'deactivated',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Log 2FA toggle activity
     */
    public static function log2FAToggled(User $user, bool $enabled, User $toggledBy = null): void
    {
        $toggledBy = $toggledBy ?? Auth::user();
        
        Log::channel('user_activity')->info('User 2FA toggled', [
            'action' => '2fa_toggled',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'toggled_by_id' => $toggledBy?->id,
            'toggled_by_email' => $toggledBy?->email,
            '2fa_enabled' => $enabled,
            'action_text' => $enabled ? 'enabled' : 'disabled',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Log account lock/unlock activity
     */
    public static function logAccountLockToggled(User $user, bool $isLocked, User $toggledBy = null): void
    {
        $toggledBy = $toggledBy ?? Auth::user();
        
        Log::channel('user_activity')->warning('User account lock toggled', [
            'action' => 'account_lock_toggled',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'toggled_by_id' => $toggledBy?->id,
            'toggled_by_email' => $toggledBy?->email,
            'is_locked' => $isLocked,
            'action_text' => $isLocked ? 'locked' : 'unlocked',
            'locked_until' => $user->locked_until?->toISOString(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Log password reset activity
     */
    public static function logPasswordReset(User $user, User $resetBy = null): void
    {
        $resetBy = $resetBy ?? Auth::user();
        
        Log::channel('user_activity')->info('User password reset', [
            'action' => 'password_reset',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'reset_by_id' => $resetBy?->id,
            'reset_by_email' => $resetBy?->email,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
    }
}
