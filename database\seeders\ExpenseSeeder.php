<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use App\Models\Supplier;
use App\Models\User;
use Carbon\Carbon;

class ExpenseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user for created_by
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        $expenses = [
            [
                'expense_date' => Carbon::now()->subDays(5),
                'description' => 'Gold purchase for inventory',
                'reference_number' => 'INV-2025-001',
                'amount' => 325000.00,
                'gst_amount' => 9750.00,
                'payment_method' => 'bank_transfer',
                'status' => 'paid',
                'notes' => '10 tola gold purchase from Mumbai supplier',
                'expense_category_id' => 1, // Raw Materials
                'supplier_id' => 1, // Mumbai Gold Suppliers
                'created_by' => $user->id,
            ],
            [
                'expense_date' => Carbon::now()->subDays(3),
                'description' => 'Diamond stones procurement',
                'reference_number' => 'INV-2025-002',
                'amount' => 150000.00,
                'gst_amount' => 4500.00,
                'payment_method' => 'cheque',
                'status' => 'approved',
                'notes' => '5 carat diamond stones for premium jewelry',
                'expense_category_id' => 2, // Stones & Gems
                'supplier_id' => 3, // Diamond House
                'created_by' => $user->id,
            ],
            [
                'expense_date' => Carbon::now()->subDays(2),
                'description' => 'Jewelry making tools',
                'reference_number' => 'INV-2025-003',
                'amount' => 25000.00,
                'gst_amount' => 4500.00,
                'payment_method' => 'cash',
                'status' => 'paid',
                'notes' => 'New polishing machine and hand tools',
                'expense_category_id' => 3, // Tools & Equipment
                'supplier_id' => 5, // Tools & Equipment Co
                'created_by' => $user->id,
            ],
            [
                'expense_date' => Carbon::now()->subDays(1),
                'description' => 'Store rent for August',
                'reference_number' => 'RENT-AUG-2025',
                'amount' => 50000.00,
                'gst_amount' => 9000.00,
                'payment_method' => 'bank_transfer',
                'status' => 'pending',
                'notes' => 'Monthly store rent payment',
                'expense_category_id' => 7, // Rent & Lease
                'supplier_id' => null,
                'created_by' => $user->id,
            ],
            [
                'expense_date' => Carbon::now(),
                'description' => 'Packaging materials stock',
                'reference_number' => 'INV-2025-004',
                'amount' => 8500.00,
                'gst_amount' => 1530.00,
                'payment_method' => 'upi',
                'status' => 'pending',
                'notes' => 'Jewelry boxes, pouches, and gift bags',
                'expense_category_id' => 4, // Packaging Materials
                'supplier_id' => 4, // Packaging Solutions
                'created_by' => $user->id,
            ],
            [
                'expense_date' => Carbon::now()->subWeek(),
                'description' => 'Electricity bill payment',
                'reference_number' => 'ELEC-JUL-2025',
                'amount' => 12000.00,
                'gst_amount' => 2160.00,
                'payment_method' => 'bank_transfer',
                'status' => 'paid',
                'notes' => 'Monthly electricity bill for store',
                'expense_category_id' => 6, // Utilities
                'supplier_id' => null,
                'created_by' => $user->id,
            ],
        ];

        foreach ($expenses as $expense) {
            Expense::create($expense);
        }
    }
}
