<?php

namespace App\Services;

use App\Models\InventoryItem;
use App\Models\Product;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class BarcodeService
{
    /**
     * Generate unique barcode for inventory item
     */
    public function generateItemBarcode(InventoryItem $item)
    {
        // Format: 8901 (company prefix) + 8-digit item ID + check digit
        $companyPrefix = '8901';
        $itemId = str_pad($item->id, 8, '0', STR_PAD_LEFT);
        $barcode = $companyPrefix . $itemId;
        
        // Calculate EAN-13 check digit
        $checkDigit = $this->calculateEAN13CheckDigit($barcode);
        $fullBarcode = $barcode . $checkDigit;
        
        return $fullBarcode;
    }

    /**
     * Generate unique barcode for product
     */
    public function generateProductBarcode(Product $product)
    {
        // Format: 8902 (product prefix) + 8-digit product ID + check digit
        $productPrefix = '8902';
        $productId = str_pad($product->id, 8, '0', STR_PAD_LEFT);
        $barcode = $productPrefix . $productId;
        
        // Calculate EAN-13 check digit
        $checkDigit = $this->calculateEAN13CheckDigit($barcode);
        $fullBarcode = $barcode . $checkDigit;
        
        return $fullBarcode;
    }

    /**
     * Generate QR code for inventory item
     */
    public function generateItemQRCode(InventoryItem $item)
    {
        $qrData = [
            'type' => 'inventory_item',
            'id' => $item->id,
            'tag' => $item->unique_tag,
            'product_name' => $item->product->name,
            'location' => $item->location->name,
            'price' => $item->selling_price,
            'weight' => $item->actual_weight,
            'purity' => $item->purity,
            'huid' => $item->huid_number,
            'url' => route('admin.inventory-items.show', $item),
            'generated_at' => now()->toISOString(),
        ];
        
        return base64_encode(json_encode($qrData));
    }

    /**
     * Generate QR code image
     */
    public function generateQRCodeImage(InventoryItem $item, $size = 200)
    {
        $qrData = $this->generateItemQRCode($item);
        
        // Generate QR code image
        $qrCode = QrCode::format('png')
                        ->size($size)
                        ->margin(2)
                        ->generate($qrData);
        
        // Save to storage
        $filename = "qr-codes/item-{$item->id}-" . now()->format('YmdHis') . ".png";
        Storage::disk('public')->put($filename, $qrCode);
        
        return $filename;
    }

    /**
     * Generate barcode image (Code 128)
     */
    public function generateBarcodeImage($barcode, $width = 300, $height = 50)
    {
        // This would typically use a barcode generation library
        // For now, we'll create a simple text-based representation
        $filename = "barcodes/barcode-{$barcode}-" . now()->format('YmdHis') . ".png";
        
        // Create a simple barcode image (in production, use a proper barcode library)
        $image = imagecreate($width, $height);
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        
        imagefill($image, 0, 0, $white);
        
        // Draw simple bars (this is a placeholder - use proper barcode library)
        for ($i = 0; $i < strlen($barcode); $i++) {
            $x = ($i * ($width / strlen($barcode)));
            if ($i % 2 === 0) {
                imagefilledrectangle($image, $x, 0, $x + 2, $height - 15, $black);
            }
        }
        
        // Add text
        imagestring($image, 2, 10, $height - 15, $barcode, $black);
        
        // Save image
        $path = storage_path('app/public/' . $filename);
        imagepng($image, $path);
        imagedestroy($image);
        
        return $filename;
    }

    /**
     * Generate unique tag
     */
    public function generateUniqueTag($prefix = 'IT', $locationCode = null)
    {
        $locationPrefix = $locationCode ? strtoupper(substr($locationCode, 0, 2)) : '';
        $dateCode = now()->format('ymd');
        $randomCode = strtoupper(Str::random(4));
        
        do {
            $tag = $prefix . $locationPrefix . $dateCode . $randomCode;
            $randomCode = strtoupper(Str::random(4));
        } while (InventoryItem::where('unique_tag', $tag)->exists());

        return $tag;
    }

    /**
     * Generate HUID number
     */
    public function generateHUIDNumber()
    {
        // HUID format: 4-digit year + 4-digit sequence
        $year = now()->format('Y');
        $sequence = str_pad(InventoryItem::whereYear('created_at', now()->year)->count() + 1, 4, '0', STR_PAD_LEFT);
        
        do {
            $huid = $year . $sequence;
            $sequence = str_pad((int)$sequence + 1, 4, '0', STR_PAD_LEFT);
        } while (InventoryItem::where('huid_number', $huid)->exists());

        return $huid;
    }

    /**
     * Calculate EAN-13 check digit
     */
    private function calculateEAN13CheckDigit($barcode)
    {
        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $sum += $barcode[$i] * (($i % 2 === 0) ? 1 : 3);
        }
        
        return (10 - ($sum % 10)) % 10;
    }

    /**
     * Validate barcode format
     */
    public function validateBarcode($barcode)
    {
        // Check if barcode is 13 digits
        if (!preg_match('/^\d{13}$/', $barcode)) {
            return false;
        }
        
        // Validate check digit
        $checkDigit = substr($barcode, -1);
        $calculatedCheckDigit = $this->calculateEAN13CheckDigit(substr($barcode, 0, 12));
        
        return $checkDigit == $calculatedCheckDigit;
    }

    /**
     * Parse QR code data
     */
    public function parseQRCode($qrData)
    {
        try {
            $decoded = json_decode(base64_decode($qrData), true);
            
            if (!$decoded || !isset($decoded['type']) || $decoded['type'] !== 'inventory_item') {
                return null;
            }
            
            return $decoded;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Generate batch of tags
     */
    public function generateBatchTags($count, $prefix = 'IT', $locationCode = null)
    {
        $tags = [];
        
        for ($i = 0; $i < $count; $i++) {
            $tags[] = $this->generateUniqueTag($prefix, $locationCode);
        }
        
        return $tags;
    }

    /**
     * Generate printable barcode labels
     */
    public function generatePrintableLabels($items, $format = 'standard')
    {
        $labels = [];
        
        foreach ($items as $item) {
            $labels[] = [
                'item_id' => $item->id,
                'unique_tag' => $item->unique_tag,
                'barcode' => $item->barcode,
                'qr_code' => $this->generateItemQRCode($item),
                'product_name' => $item->product->name,
                'price' => $item->selling_price,
                'weight' => $item->actual_weight,
                'purity' => $item->purity,
                'location' => $item->location->name,
                'format' => $format,
            ];
        }
        
        return $labels;
    }

    /**
     * Bulk generate barcodes for items
     */
    public function bulkGenerateBarcodes($itemIds)
    {
        $items = InventoryItem::whereIn('id', $itemIds)->get();
        $results = [];
        
        foreach ($items as $item) {
            if (!$item->barcode) {
                $barcode = $this->generateItemBarcode($item);
                $item->update(['barcode' => $barcode]);
                $results[] = [
                    'item_id' => $item->id,
                    'tag' => $item->unique_tag,
                    'barcode' => $barcode,
                    'success' => true,
                ];
            } else {
                $results[] = [
                    'item_id' => $item->id,
                    'tag' => $item->unique_tag,
                    'barcode' => $item->barcode,
                    'success' => false,
                    'message' => 'Barcode already exists',
                ];
            }
        }
        
        return $results;
    }

    /**
     * Bulk generate QR codes for items
     */
    public function bulkGenerateQRCodes($itemIds)
    {
        $items = InventoryItem::whereIn('id', $itemIds)->get();
        $results = [];
        
        foreach ($items as $item) {
            $qrCode = $this->generateItemQRCode($item);
            $item->update(['qr_code' => $qrCode]);
            
            $results[] = [
                'item_id' => $item->id,
                'tag' => $item->unique_tag,
                'qr_code' => $qrCode,
                'success' => true,
            ];
        }
        
        return $results;
    }

    /**
     * Search items by barcode or tag
     */
    public function searchByCode($code)
    {
        // Try to find by barcode first
        $item = InventoryItem::where('barcode', $code)->first();
        
        if (!$item) {
            // Try to find by unique tag
            $item = InventoryItem::where('unique_tag', $code)->first();
        }
        
        if (!$item) {
            // Try to find by QR code
            $item = InventoryItem::where('qr_code', $code)->first();
        }
        
        if (!$item) {
            // Try to find by HUID number
            $item = InventoryItem::where('huid_number', $code)->first();
        }
        
        return $item;
    }

    /**
     * Get barcode statistics
     */
    public function getBarcodeStatistics()
    {
        return [
            'total_items' => InventoryItem::count(),
            'items_with_barcode' => InventoryItem::whereNotNull('barcode')->count(),
            'items_with_qr_code' => InventoryItem::whereNotNull('qr_code')->count(),
            'items_with_huid' => InventoryItem::whereNotNull('huid_number')->count(),
            'items_without_barcode' => InventoryItem::whereNull('barcode')->count(),
            'items_without_qr_code' => InventoryItem::whereNull('qr_code')->count(),
        ];
    }
}
