<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MakingChargeTemplate;
use App\Models\Metal;
use App\Models\User;

class MakingChargeTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user for created_by
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        $templates = [
            [
                'name' => 'Gold Ring Standard',
                'description' => 'Standard making charges for gold rings',
                'metal_id' => 1, // Gold
                'product_type' => 'ring',
                'percentage_charge' => 15.00,
                'fixed_charge' => 0.00,
                'charge_per_gram' => 800.00,
                'min_charge' => 1000.00,
                'max_charge' => 10000.00,
                'wastage_percentage' => 8.00,
                'weight_range_min' => 2.000,
                'weight_range_max' => 20.000,
                'is_active' => true,
                'sort_order' => 1,
                'created_by' => $user->id,
            ],
            [
                'name' => 'Gold Necklace Premium',
                'description' => 'Premium making charges for gold necklaces',
                'metal_id' => 1, // Gold
                'product_type' => 'necklace',
                'percentage_charge' => 12.00,
                'fixed_charge' => 0.00,
                'charge_per_gram' => 600.00,
                'min_charge' => 5000.00,
                'max_charge' => 50000.00,
                'wastage_percentage' => 10.00,
                'weight_range_min' => 10.000,
                'weight_range_max' => 100.000,
                'is_active' => true,
                'sort_order' => 2,
                'created_by' => $user->id,
            ],
            [
                'name' => 'Silver Bracelet Standard',
                'description' => 'Standard making charges for silver bracelets',
                'metal_id' => 2, // Silver
                'product_type' => 'bracelet',
                'percentage_charge' => 20.00,
                'fixed_charge' => 500.00,
                'charge_per_gram' => 50.00,
                'min_charge' => 200.00,
                'max_charge' => 2000.00,
                'wastage_percentage' => 5.00,
                'weight_range_min' => 5.000,
                'weight_range_max' => 50.000,
                'is_active' => true,
                'sort_order' => 3,
                'created_by' => $user->id,
            ],
            [
                'name' => 'Gold Earrings Delicate',
                'description' => 'Making charges for delicate gold earrings',
                'metal_id' => 1, // Gold
                'product_type' => 'earrings',
                'percentage_charge' => 18.00,
                'fixed_charge' => 0.00,
                'charge_per_gram' => 1000.00,
                'min_charge' => 800.00,
                'max_charge' => 8000.00,
                'wastage_percentage' => 12.00,
                'weight_range_min' => 1.000,
                'weight_range_max' => 15.000,
                'is_active' => true,
                'sort_order' => 4,
                'created_by' => $user->id,
            ],
            [
                'name' => 'Platinum Ring Luxury',
                'description' => 'Luxury making charges for platinum rings',
                'metal_id' => 3, // Platinum (if exists)
                'product_type' => 'ring',
                'percentage_charge' => 10.00,
                'fixed_charge' => 2000.00,
                'charge_per_gram' => 1500.00,
                'min_charge' => 3000.00,
                'max_charge' => 25000.00,
                'wastage_percentage' => 6.00,
                'weight_range_min' => 3.000,
                'weight_range_max' => 25.000,
                'is_active' => true,
                'sort_order' => 5,
                'created_by' => $user->id,
            ],
            [
                'name' => 'Gold Chain Heavy',
                'description' => 'Making charges for heavy gold chains',
                'metal_id' => 1, // Gold
                'product_type' => 'chain',
                'percentage_charge' => 8.00,
                'fixed_charge' => 0.00,
                'charge_per_gram' => 400.00,
                'min_charge' => 8000.00,
                'max_charge' => 80000.00,
                'wastage_percentage' => 6.00,
                'weight_range_min' => 20.000,
                'weight_range_max' => 200.000,
                'is_active' => true,
                'sort_order' => 6,
                'created_by' => $user->id,
            ],
        ];

        foreach ($templates as $template) {
            // Check if metal exists, if not skip this template
            if ($template['metal_id'] && !Metal::find($template['metal_id'])) {
                continue;
            }
            MakingChargeTemplate::create($template);
        }
    }
}
