<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MakingChargeTemplate;
use App\Models\Metal;
use App\Models\Location;
use App\Services\MetalRateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MakingChargeTemplateController extends Controller
{
    protected $metalRateService;

    public function __construct(MetalRateService $metalRateService)
    {
        $this->metalRateService = $metalRateService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = MakingChargeTemplate::with(['metal', 'location']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('product_type', 'like', "%{$search}%");
            });
        }

        // Filter by metal
        if ($request->filled('metal_id')) {
            $query->where('metal_id', $request->metal_id);
        }

        // Filter by product type
        if ($request->filled('product_type')) {
            $query->where('product_type', $request->product_type);
        }

        // Filter by location
        if ($request->filled('location_id')) {
            $query->where('location_id', $request->location_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $templates = $query->orderBy('sort_order')->orderBy('name')->paginate(20);

        // Get filter options
        $metals = Metal::active()->orderBy('name')->get();
        $locations = Location::active()->orderBy('name')->get();
        $productTypes = MakingChargeTemplate::distinct()->pluck('product_type')->filter()->sort();

        // Get statistics
        $stats = [
            'total_templates' => MakingChargeTemplate::count(),
            'active_templates' => MakingChargeTemplate::active()->count(),
            'templates_by_metal' => MakingChargeTemplate::groupBy('metal_id')->count(),
            'avg_percentage_charge' => MakingChargeTemplate::active()->avg('percentage_charge'),
        ];

        return view('admin.making-charges.index', compact('templates', 'metals', 'locations', 'productTypes', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $metals = Metal::active()->orderBy('name')->get();
        $locations = Location::active()->orderBy('name')->get();

        // Get making charge recommendations for each metal
        $recommendations = [];
        foreach ($metals as $metal) {
            $recommendations[$metal->id] = $this->metalRateService->getMakingChargeRecommendations($metal->id);
        }

        return view('admin.making-charges.create', compact('metals', 'locations', 'recommendations'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'metal_id' => 'required|exists:metals,id',
            'product_type' => 'nullable|string|max:100',
            'percentage_charge' => 'nullable|numeric|min:0|max:100',
            'fixed_charge' => 'nullable|numeric|min:0',
            'min_charge' => 'nullable|numeric|min:0',
            'max_charge' => 'nullable|numeric|min:0',
            'wastage_percentage' => 'nullable|numeric|min:0|max:50',
            'weight_range_min' => 'nullable|numeric|min:0',
            'weight_range_max' => 'nullable|numeric|min:0',
            'additional_charges' => 'nullable|array',
            'additional_charges.*.name' => 'required_with:additional_charges|string|max:255',
            'additional_charges.*.amount' => 'required_with:additional_charges|numeric|min:0',
            'additional_charges.*.description' => 'nullable|string|max:500',
            'location_id' => 'nullable|exists:locations,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Validate weight range
        if ($validated['weight_range_min'] && $validated['weight_range_max'] &&
            $validated['weight_range_min'] >= $validated['weight_range_max']) {
            return back()->withInput()
                ->with('error', 'Minimum weight must be less than maximum weight.');
        }

        // Validate charges
        if ($validated['min_charge'] && $validated['max_charge'] &&
            $validated['min_charge'] >= $validated['max_charge']) {
            return back()->withInput()
                ->with('error', 'Minimum charge must be less than maximum charge.');
        }

        try {
            DB::beginTransaction();

            // Set sort order if not provided
            if (!isset($validated['sort_order'])) {
                $validated['sort_order'] = (MakingChargeTemplate::max('sort_order') ?? 0) + 1;
            }

            MakingChargeTemplate::create([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'metal_id' => $validated['metal_id'],
                'product_type' => $validated['product_type'],
                'percentage_charge' => $validated['percentage_charge'] ?? 0,
                'fixed_charge' => $validated['fixed_charge'] ?? 0,
                'min_charge' => $validated['min_charge'] ?? 0,
                'max_charge' => $validated['max_charge'],
                'wastage_percentage' => $validated['wastage_percentage'] ?? 0,
                'weight_range_min' => $validated['weight_range_min'],
                'weight_range_max' => $validated['weight_range_max'],
                'additional_charges' => $validated['additional_charges'],
                'location_id' => $validated['location_id'],
                'sort_order' => $validated['sort_order'],
                'is_active' => $validated['is_active'] ?? true,
                'created_by' => auth()->id(),
            ]);

            DB::commit();

            return redirect()->route('admin.making-charges.index')
                ->with('success', 'Making charge template created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error creating template: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(MakingChargeTemplate $makingCharge)
    {
        $makingCharge->load(['metal', 'location', 'creator', 'updater']);

        // Get usage statistics
        $stats = [
            'products_using_template' => 0, // This would need to be implemented based on how templates are used
            'avg_charges_calculated' => 0,
            'total_revenue_impact' => 0,
        ];

        // Get sample calculations
        $sampleWeights = [1, 5, 10, 20, 50];
        $sampleCalculations = [];

        $currentRate = $this->metalRateService->getCurrentRate($makingCharge->metal_id);

        foreach ($sampleWeights as $weight) {
            $metalValue = $weight * $currentRate;
            $calculation = $makingCharge->calculateCharges($metalValue, $weight);
            $sampleCalculations[] = [
                'weight' => $weight,
                'metal_value' => $metalValue,
                'calculation' => $calculation,
            ];
        }

        return view('admin.making-charges.show', compact('makingCharge', 'stats', 'sampleCalculations', 'currentRate'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MakingChargeTemplate $makingCharge)
    {
        $metals = Metal::active()->orderBy('name')->get();
        $locations = Location::active()->orderBy('name')->get();

        // Get current recommendations for this metal
        $recommendations = $this->metalRateService->getMakingChargeRecommendations($makingCharge->metal_id, $makingCharge->product_type);

        return view('admin.making-charges.edit', compact('makingCharge', 'metals', 'locations', 'recommendations'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MakingChargeTemplate $makingCharge)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'metal_id' => 'required|exists:metals,id',
            'product_type' => 'nullable|string|max:100',
            'percentage_charge' => 'nullable|numeric|min:0|max:100',
            'fixed_charge' => 'nullable|numeric|min:0',
            'min_charge' => 'nullable|numeric|min:0',
            'max_charge' => 'nullable|numeric|min:0',
            'wastage_percentage' => 'nullable|numeric|min:0|max:50',
            'weight_range_min' => 'nullable|numeric|min:0',
            'weight_range_max' => 'nullable|numeric|min:0',
            'additional_charges' => 'nullable|array',
            'additional_charges.*.name' => 'required_with:additional_charges|string|max:255',
            'additional_charges.*.amount' => 'required_with:additional_charges|numeric|min:0',
            'additional_charges.*.description' => 'nullable|string|max:500',
            'location_id' => 'nullable|exists:locations,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Validate weight range
        if ($validated['weight_range_min'] && $validated['weight_range_max'] &&
            $validated['weight_range_min'] >= $validated['weight_range_max']) {
            return back()->withInput()
                ->with('error', 'Minimum weight must be less than maximum weight.');
        }

        // Validate charges
        if ($validated['min_charge'] && $validated['max_charge'] &&
            $validated['min_charge'] >= $validated['max_charge']) {
            return back()->withInput()
                ->with('error', 'Minimum charge must be less than maximum charge.');
        }

        try {
            DB::beginTransaction();

            $validated['updated_by'] = auth()->id();

            $makingCharge->update($validated);

            DB::commit();

            return redirect()->route('admin.making-charges.index')
                ->with('success', 'Making charge template updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error updating template: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MakingChargeTemplate $makingCharge)
    {
        try {
            $makingCharge->delete();

            return redirect()->route('admin.making-charges.index')
                ->with('success', 'Making charge template deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Error deleting template: ' . $e->getMessage());
        }
    }

    /**
     * Calculate charges for given parameters
     */
    public function calculateCharges(Request $request)
    {
        $validated = $request->validate([
            'metal_id' => 'required|exists:metals,id',
            'weight' => 'required|numeric|min:0.001',
            'product_type' => 'nullable|string|max:100',
            'location_id' => 'nullable|exists:locations,id',
            'template_id' => 'nullable|exists:making_charge_templates,id',
        ]);

        try {
            $metalId = $validated['metal_id'];
            $weight = $validated['weight'];
            $productType = $validated['product_type'];
            $locationId = $validated['location_id'];

            // Get current metal rate
            $currentRate = $this->metalRateService->getCurrentRate($metalId);
            $metalValue = $weight * $currentRate;

            if ($validated['template_id']) {
                // Use specific template
                $template = MakingChargeTemplate::find($validated['template_id']);
                $calculation = $template->calculateCharges($metalValue, $weight);
                $templateUsed = $template;
            } else {
                // Find best matching template
                $template = MakingChargeTemplate::active()
                    ->forMetal($metalId)
                    ->forWeight($weight)
                    ->forLocation($locationId)
                    ->when($productType, function ($query) use ($productType) {
                        $query->where(function ($q) use ($productType) {
                            $q->where('product_type', $productType)
                              ->orWhereNull('product_type');
                        });
                    })
                    ->orderBy('sort_order')
                    ->first();

                if ($template) {
                    $calculation = $template->calculateCharges($metalValue, $weight);
                    $templateUsed = $template;
                } else {
                    // Use default calculation
                    $recommendations = $this->metalRateService->getMakingChargeRecommendations($metalId, $productType);
                    $defaultPercentage = $recommendations['making_charge_percentage']['recommended'];

                    $calculation = [
                        'base_making_charges' => $metalValue * ($defaultPercentage / 100),
                        'additional_charges' => 0,
                        'total_making_charges' => $metalValue * ($defaultPercentage / 100),
                        'template_used' => 'Default calculation',
                        'percentage_applied' => $defaultPercentage,
                        'fixed_charge_applied' => 0,
                    ];
                    $templateUsed = null;
                }
            }

            // Calculate wastage if template has wastage percentage
            $wastageCalculation = null;
            if ($templateUsed && $templateUsed->wastage_percentage > 0) {
                $wastageCalculation = $this->metalRateService->calculateWastageCharges(
                    $metalId,
                    $weight,
                    $weight * (1 - $templateUsed->wastage_percentage / 100),
                    $templateUsed->wastage_percentage
                );
            }

            return response()->json([
                'success' => true,
                'metal_rate' => $currentRate,
                'metal_value' => $metalValue,
                'weight' => $weight,
                'calculation' => $calculation,
                'wastage_calculation' => $wastageCalculation,
                'template_used' => $templateUsed,
                'recommendations' => $this->metalRateService->getMakingChargeRecommendations($metalId, $productType),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating charges: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Get templates for specific criteria
     */
    public function getTemplates(Request $request)
    {
        $query = MakingChargeTemplate::active()->with('metal');

        if ($request->filled('metal_id')) {
            $query->forMetal($request->metal_id);
        }

        if ($request->filled('product_type')) {
            $query->forProductType($request->product_type);
        }

        if ($request->filled('weight')) {
            $query->forWeight($request->weight);
        }

        if ($request->filled('location_id')) {
            $query->forLocation($request->location_id);
        }

        $templates = $query->orderBy('sort_order')->get();

        return response()->json($templates);
    }

    /**
     * Toggle template status
     */
    public function toggleStatus(MakingChargeTemplate $makingCharge)
    {
        try {
            $makingCharge->update([
                'is_active' => !$makingCharge->is_active,
                'updated_by' => auth()->id(),
            ]);

            $status = $makingCharge->is_active ? 'activated' : 'deactivated';

            return response()->json([
                'success' => true,
                'message' => "Template {$status} successfully.",
                'is_active' => $makingCharge->is_active,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating template status: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Duplicate template
     */
    public function duplicate(MakingChargeTemplate $makingCharge)
    {
        try {
            $newTemplate = $makingCharge->replicate();
            $newTemplate->name = $makingCharge->name . ' (Copy)';
            $newTemplate->created_by = auth()->id();
            $newTemplate->updated_by = null;
            $newTemplate->save();

            return redirect()->route('admin.making-charges.edit', $newTemplate)
                ->with('success', 'Template duplicated successfully. Please review and update as needed.');

        } catch (\Exception $e) {
            return back()->with('error', 'Error duplicating template: ' . $e->getMessage());
        }
    }
}
