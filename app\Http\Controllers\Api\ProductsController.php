<?php

namespace App\Http\Controllers\Api;

use App\Models\Product;
use App\Models\Category;
use App\Models\Metal;
use App\Http\Resources\ProductResource;
use App\Http\Resources\ProductCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductsController extends BaseApiController
{
    /**
     * Display a listing of products
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Check cache first
            $cacheKey = $this->generateCacheKey($request, 'products');
            $cachedResponse = $this->getCachedResponse($cacheKey);
            
            if ($cachedResponse) {
                return response()->json($cachedResponse);
            }

            $query = Product::with(['category', 'metal', 'images', 'inventoryItems']);

            // Apply filters
            $allowedFilters = ['name', 'sku', 'category_id', 'metal_id', 'is_active'];
            $query = $this->applyFilters($query, $request, $allowedFilters);

            // Price range filter
            if ($request->has('min_price')) {
                $query->where('total_amount', '>=', $request->min_price);
            }
            if ($request->has('max_price')) {
                $query->where('total_amount', '<=', $request->max_price);
            }

            // Weight range filter
            if ($request->has('min_weight')) {
                $query->where('weight', '>=', $request->min_weight);
            }
            if ($request->has('max_weight')) {
                $query->where('weight', '<=', $request->max_weight);
            }

            // Stock status filter
            if ($request->has('in_stock')) {
                $inStock = $request->boolean('in_stock');
                if ($inStock) {
                    $query->whereHas('inventoryItems', function ($q) {
                        $q->where('quantity', '>', 0);
                    });
                } else {
                    $query->whereDoesntHave('inventoryItems')
                          ->orWhereHas('inventoryItems', function ($q) {
                              $q->where('quantity', '<=', 0);
                          });
                }
            }

            // Apply sorting
            $allowedSorts = ['name', 'sku', 'total_amount', 'weight', 'created_at'];
            $query = $this->applySorting($query, $request, $allowedSorts);

            // Get pagination parameters
            $paginationParams = $this->getPaginationParams($request);
            $products = $query->paginate($paginationParams['per_page']);

            $response = [
                'success' => true,
                'message' => 'Products retrieved successfully',
                'data' => ProductResource::collection($products->items()),
                'pagination' => [
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                    'per_page' => $products->perPage(),
                    'total' => $products->total(),
                    'from' => $products->firstItem(),
                    'to' => $products->lastItem(),
                    'has_more_pages' => $products->hasMorePages(),
                ],
                'filters' => [
                    'categories' => Category::where('is_active', true)->select('id', 'name')->get(),
                    'metals' => Metal::where('is_active', true)->select('id', 'name')->get(),
                ],
                'metadata' => $this->buildMetadata($request),
            ];

            // Cache the response
            $this->cacheResponse($cacheKey, $response, 300); // 5 minutes

            $this->logApiActivity($request, 'index', 'products');

            return response()->json($response);
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Display the specified product
     */
    public function show(Request $request, $id): JsonResponse
    {
        try {
            $product = Product::with([
                'category', 
                'metal', 
                'images', 
                'inventoryItems',
                'specifications',
                'reviews' => function ($query) {
                    $query->where('is_approved', true)->latest()->limit(5);
                }
            ])->find($id);

            if (!$product) {
                return $this->sendNotFoundResponse('Product not found');
            }

            $this->logApiActivity($request, 'show', 'products', $id);

            return $this->sendResponse(
                new ProductResource($product),
                'Product retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Store a newly created product
     */
    public function store(Request $request): JsonResponse
    {
        try {
            if (!$this->checkPermission('create_products')) {
                return $this->sendForbiddenResponse('Insufficient permissions to create products');
            }

            $validatedData = $this->validateRequest($request, [
                'name' => 'required|string|max:255',
                'sku' => 'required|string|unique:products,sku',
                'category_id' => 'required|exists:categories,id',
                'metal_id' => 'required|exists:metals,id',
                'weight' => 'required|numeric|min:0',
                'purity' => 'required|string|max:50',
                'making_charges' => 'required|numeric|min:0',
                'stone_charges' => 'nullable|numeric|min:0',
                'other_charges' => 'nullable|numeric|min:0',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'images' => 'nullable|array',
                'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            $product = Product::create($validatedData);

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    $path = $image->store('products', 'public');
                    $product->images()->create([
                        'image_path' => $path,
                        'is_primary' => $product->images()->count() === 0,
                    ]);
                }
            }

            $product->load(['category', 'metal', 'images']);

            $this->logApiActivity($request, 'store', 'products', $product->id);

            return $this->sendResponse(
                new ProductResource($product),
                'Product created successfully',
                201
            );
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Update the specified product
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            if (!$this->checkPermission('update_products')) {
                return $this->sendForbiddenResponse('Insufficient permissions to update products');
            }

            $product = Product::find($id);
            if (!$product) {
                return $this->sendNotFoundResponse('Product not found');
            }

            $validatedData = $this->validateRequest($request, [
                'name' => 'sometimes|required|string|max:255',
                'sku' => 'sometimes|required|string|unique:products,sku,' . $id,
                'category_id' => 'sometimes|required|exists:categories,id',
                'metal_id' => 'sometimes|required|exists:metals,id',
                'weight' => 'sometimes|required|numeric|min:0',
                'purity' => 'sometimes|required|string|max:50',
                'making_charges' => 'sometimes|required|numeric|min:0',
                'stone_charges' => 'nullable|numeric|min:0',
                'other_charges' => 'nullable|numeric|min:0',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
            ]);

            $product->update($validatedData);
            $product->load(['category', 'metal', 'images']);

            $this->logApiActivity($request, 'update', 'products', $id);

            return $this->sendResponse(
                new ProductResource($product),
                'Product updated successfully'
            );
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Remove the specified product
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            if (!$this->checkPermission('delete_products')) {
                return $this->sendForbiddenResponse('Insufficient permissions to delete products');
            }

            $product = Product::find($id);
            if (!$product) {
                return $this->sendNotFoundResponse('Product not found');
            }

            // Check if product has any sales
            if ($product->invoiceItems()->exists()) {
                return $this->sendError('Cannot delete product with existing sales records', [], 409);
            }

            $product->delete();

            $this->logApiActivity($request, 'destroy', 'products', $id);

            return $this->sendResponse(null, 'Product deleted successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Search products
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validatedData = $this->validateRequest($request, [
                'query' => 'required|string|min:2',
                'category_id' => 'nullable|exists:categories,id',
                'metal_id' => 'nullable|exists:metals,id',
                'min_price' => 'nullable|numeric|min:0',
                'max_price' => 'nullable|numeric|min:0',
            ]);

            $query = Product::with(['category', 'metal', 'images'])
                           ->where('is_active', true);

            // Search in name, SKU, and description
            $searchTerm = $validatedData['query'];
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('sku', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });

            // Apply additional filters
            if (isset($validatedData['category_id'])) {
                $query->where('category_id', $validatedData['category_id']);
            }

            if (isset($validatedData['metal_id'])) {
                $query->where('metal_id', $validatedData['metal_id']);
            }

            if (isset($validatedData['min_price'])) {
                $query->where('total_amount', '>=', $validatedData['min_price']);
            }

            if (isset($validatedData['max_price'])) {
                $query->where('total_amount', '<=', $validatedData['max_price']);
            }

            $paginationParams = $this->getPaginationParams($request);
            $products = $query->paginate($paginationParams['per_page']);

            $this->logApiActivity($request, 'search', 'products');

            return $this->sendPaginatedResponse($products, 'Search results retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Get product categories
     */
    public function categories(Request $request): JsonResponse
    {
        try {
            $categories = Category::where('is_active', true)
                                ->withCount('products')
                                ->orderBy('name')
                                ->get();

            $this->logApiActivity($request, 'categories', 'products');

            return $this->sendResponse($categories, 'Categories retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Get product metals
     */
    public function metals(Request $request): JsonResponse
    {
        try {
            $metals = Metal::where('is_active', true)
                          ->withCount('products')
                          ->orderBy('name')
                          ->get();

            $this->logApiActivity($request, 'metals', 'products');

            return $this->sendResponse($metals, 'Metals retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Get product stock status
     */
    public function stock(Request $request, $id): JsonResponse
    {
        try {
            $product = Product::with('inventoryItems')->find($id);
            if (!$product) {
                return $this->sendNotFoundResponse('Product not found');
            }

            $stockInfo = [
                'product_id' => $product->id,
                'sku' => $product->sku,
                'name' => $product->name,
                'total_quantity' => $product->inventoryItems->sum('quantity'),
                'available_quantity' => $product->inventoryItems->where('quantity', '>', 0)->sum('quantity'),
                'reserved_quantity' => 0, // Would be calculated based on pending orders
                'locations' => $product->inventoryItems->map(function ($item) {
                    return [
                        'location' => $item->location ?? 'Main Store',
                        'quantity' => $item->quantity,
                        'last_updated' => $item->updated_at,
                    ];
                }),
                'is_in_stock' => $product->inventoryItems->sum('quantity') > 0,
                'low_stock_threshold' => 5,
                'is_low_stock' => $product->inventoryItems->sum('quantity') <= 5,
            ];

            $this->logApiActivity($request, 'stock', 'products', $id);

            return $this->sendResponse($stockInfo, 'Stock information retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }
}
