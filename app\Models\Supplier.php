<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Supplier extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'company_name',
        'gst_number',
        'phone',
        'email',
        'address',
        'city',
        'state',
        'pincode',
        'contact_person',
        'credit_limit',
        'credit_days',
        'notes',
        'is_active',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function purchaseOrders(): HasMany
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    public function goodsReceiptNotes(): HasMany
    {
        return $this->hasMany(GoodsReceiptNote::class);
    }

    public function supplierPayments(): HasMany
    {
        return $this->hasMany(SupplierPayment::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }

    public function scopeByState($query, $state)
    {
        return $query->where('state', $state);
    }

    public function scopeWithGst($query)
    {
        return $query->whereNotNull('gst_number');
    }

    /**
     * Accessors
     */
    public function getFullAddressAttribute()
    {
        return trim($this->address . ', ' . $this->city . ', ' . $this->state . ' - ' . $this->pincode);
    }

    public function getStatusDisplayAttribute()
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    public function getStatusColorAttribute()
    {
        return $this->is_active ? 'green' : 'red';
    }

    public function getCreditUtilizationAttribute()
    {
        if ($this->credit_limit <= 0) {
            return 0;
        }

        $outstandingAmount = $this->getOutstandingAmount();
        return round(($outstandingAmount / $this->credit_limit) * 100, 2);
    }

    public function getCreditAvailableAttribute()
    {
        return max(0, $this->credit_limit - $this->getOutstandingAmount());
    }

    /**
     * Business Logic Methods
     */
    public function getOutstandingAmount()
    {
        return $this->purchaseOrders()
                   ->whereIn('status', ['confirmed', 'partially_received'])
                   ->sum('total_amount') -
               $this->supplierPayments()
                   ->where('status', 'completed')
                   ->sum('amount');
    }

    public function getTotalPurchaseValue($days = null)
    {
        $query = $this->purchaseOrders()->where('status', 'completed');

        if ($days) {
            $query->where('created_at', '>=', now()->subDays($days));
        }

        return $query->sum('total_amount');
    }

    public function getAverageDeliveryTime()
    {
        $completedOrders = $this->purchaseOrders()
                               ->where('status', 'completed')
                               ->whereNotNull('expected_delivery_date')
                               ->get();

        if ($completedOrders->isEmpty()) {
            return null;
        }

        $totalDays = 0;
        foreach ($completedOrders as $order) {
            $deliveryDate = $order->goodsReceiptNotes()->latest()->first()?->received_date;
            if ($deliveryDate) {
                $totalDays += Carbon::parse($order->po_date)->diffInDays($deliveryDate);
            }
        }

        return $completedOrders->count() > 0 ? round($totalDays / $completedOrders->count(), 1) : null;
    }

    public function getQualityRating()
    {
        $grns = $this->goodsReceiptNotes()
                    ->whereNotNull('quality_rating')
                    ->get();

        if ($grns->isEmpty()) {
            return null;
        }

        return round($grns->avg('quality_rating'), 1);
    }

    public function getPerformanceMetrics($days = 30)
    {
        $startDate = now()->subDays($days);

        $totalOrders = $this->purchaseOrders()
                           ->where('created_at', '>=', $startDate)
                           ->count();

        $completedOrders = $this->purchaseOrders()
                               ->where('created_at', '>=', $startDate)
                               ->where('status', 'completed')
                               ->count();

        $onTimeDeliveries = $this->purchaseOrders()
                                ->where('created_at', '>=', $startDate)
                                ->where('status', 'completed')
                                ->whereHas('goodsReceiptNotes', function ($query) {
                                    $query->whereRaw('received_date <= expected_delivery_date');
                                })
                                ->count();

        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'completion_rate' => $totalOrders > 0 ? round(($completedOrders / $totalOrders) * 100, 2) : 0,
            'on_time_deliveries' => $onTimeDeliveries,
            'on_time_delivery_rate' => $completedOrders > 0 ? round(($onTimeDeliveries / $completedOrders) * 100, 2) : 0,
            'total_purchase_value' => $this->getTotalPurchaseValue($days),
            'average_delivery_time' => $this->getAverageDeliveryTime(),
            'quality_rating' => $this->getQualityRating(),
            'outstanding_amount' => $this->getOutstandingAmount(),
            'credit_utilization' => $this->credit_utilization,
        ];
    }

    public function canPlaceOrder($orderAmount)
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->credit_limit > 0) {
            return ($this->getOutstandingAmount() + $orderAmount) <= $this->credit_limit;
        }

        return true;
    }

    public function getRecentOrders($limit = 5)
    {
        return $this->purchaseOrders()
                   ->with(['items.product'])
                   ->orderBy('created_at', 'desc')
                   ->limit($limit)
                   ->get();
    }

    public function getTopProducts($limit = 10)
    {
        return Product::whereHas('purchaseOrderItems', function ($query) {
                    $query->whereHas('purchaseOrder', function ($subQuery) {
                        $subQuery->where('supplier_id', $this->id);
                    });
                })
                ->withSum(['purchaseOrderItems as total_quantity' => function ($query) {
                    $query->whereHas('purchaseOrder', function ($subQuery) {
                        $subQuery->where('supplier_id', $this->id);
                    });
                }], 'quantity_ordered')
                ->withSum(['purchaseOrderItems as total_value' => function ($query) {
                    $query->whereHas('purchaseOrder', function ($subQuery) {
                        $subQuery->where('supplier_id', $this->id);
                    });
                }], 'total_price')
                ->orderBy('total_value', 'desc')
                ->limit($limit)
                ->get();
    }

    public function updateCreditLimit($newLimit, $reason = null)
    {
        $oldLimit = $this->credit_limit;
        $this->update(['credit_limit' => $newLimit]);

        // Log credit limit change
        activity()
            ->performedOn($this)
            ->withProperties([
                'old_limit' => $oldLimit,
                'new_limit' => $newLimit,
                'reason' => $reason,
            ])
            ->log('Credit limit updated');

        return $this;
    }

    public function deactivate($reason = null)
    {
        $this->update(['is_active' => false]);

        // Log deactivation
        activity()
            ->performedOn($this)
            ->withProperties(['reason' => $reason])
            ->log('Supplier deactivated');

        return $this;
    }

    public function activate($reason = null)
    {
        $this->update(['is_active' => true]);

        // Log activation
        activity()
            ->performedOn($this)
            ->withProperties(['reason' => $reason])
            ->log('Supplier activated');

        return $this;
    }

    public function getSummary()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'company_name' => $this->company_name,
            'contact_person' => $this->contact_person,
            'phone' => $this->phone,
            'email' => $this->email,
            'city' => $this->city,
            'state' => $this->state,
            'gst_number' => $this->gst_number,
            'status' => $this->status_display,
            'credit_limit' => $this->credit_limit,
            'credit_available' => $this->credit_available,
            'credit_utilization' => $this->credit_utilization . '%',
            'outstanding_amount' => $this->getOutstandingAmount(),
            'total_orders' => $this->purchaseOrders()->count(),
            'performance_metrics' => $this->getPerformanceMetrics(),
        ];
    }
}
