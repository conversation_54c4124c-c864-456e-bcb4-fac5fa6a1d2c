<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class RepairJob extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'customer_id',
        'product_id',
        'job_number',
        'item_description',
        'issue_description',
        'estimated_cost',
        'actual_cost',
        'estimated_completion_date',
        'actual_completion_date',
        'status',
        'priority',
        'assigned_to',
        'received_by',
        'delivered_by',
        'notes',
        'images',
        'receipt_number',
        'delivery_receipt_number',
        'created_by',
        'location_id',
    ];

    protected $casts = [
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'estimated_completion_date' => 'date',
        'actual_completion_date' => 'date',
        'images' => 'array',
    ];

    // Status constants
    const STATUS_RECEIVED = 'received';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_READY_FOR_DELIVERY = 'ready_for_delivery';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_CANCELLED = 'cancelled';

    // Priority constants
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * Relationships
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function receivedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    public function deliveredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivered_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function statusHistory(): HasMany
    {
        return $this->hasMany(RepairStatusHistory::class);
    }

    public function charges(): HasMany
    {
        return $this->hasMany(RepairCharge::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(RepairItem::class);
    }

    /**
     * Scopes
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeOverdue($query)
    {
        return $query->where('estimated_completion_date', '<', now())
                    ->whereNotIn('status', [self::STATUS_COMPLETED, self::STATUS_DELIVERED, self::STATUS_CANCELLED]);
    }

    public function scopeInProgress($query)
    {
        return $query->whereIn('status', [self::STATUS_RECEIVED, self::STATUS_IN_PROGRESS]);
    }

    /**
     * Accessors & Mutators
     */
    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            self::STATUS_RECEIVED => 'Received',
            self::STATUS_IN_PROGRESS => 'In Progress',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_READY_FOR_DELIVERY => 'Ready for Delivery',
            self::STATUS_DELIVERED => 'Delivered',
            self::STATUS_CANCELLED => 'Cancelled',
            default => 'Unknown'
        };
    }

    public function getPriorityLabelAttribute()
    {
        return match($this->priority) {
            self::PRIORITY_LOW => 'Low',
            self::PRIORITY_MEDIUM => 'Medium',
            self::PRIORITY_HIGH => 'High',
            self::PRIORITY_URGENT => 'Urgent',
            default => 'Medium'
        };
    }

    public function getIsOverdueAttribute()
    {
        return $this->estimated_completion_date && 
               $this->estimated_completion_date->isPast() && 
               !in_array($this->status, [self::STATUS_COMPLETED, self::STATUS_DELIVERED, self::STATUS_CANCELLED]);
    }

    public function getTotalCostAttribute()
    {
        return $this->charges()->sum('amount') + ($this->actual_cost ?? $this->estimated_cost ?? 0);
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($repairJob) {
            if (!$repairJob->job_number) {
                $repairJob->job_number = static::generateJobNumber();
            }
            $repairJob->created_by = auth()->id();
        });

        static::created(function ($repairJob) {
            // Create initial status history
            $repairJob->statusHistory()->create([
                'status' => $repairJob->status,
                'notes' => 'Repair job created',
                'updated_by' => auth()->id(),
            ]);
        });

        static::updating(function ($repairJob) {
            if ($repairJob->isDirty('status')) {
                // Create status history entry
                $repairJob->statusHistory()->create([
                    'status' => $repairJob->status,
                    'notes' => 'Status updated to ' . $repairJob->status_label,
                    'updated_by' => auth()->id(),
                ]);
            }
        });
    }

    /**
     * Generate unique job number
     */
    public static function generateJobNumber()
    {
        $prefix = 'RJ';
        $date = now()->format('Ymd');
        $lastJob = static::whereDate('created_at', now())->latest()->first();
        $sequence = $lastJob ? (int)substr($lastJob->job_number, -4) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
