<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Company Settings
        Schema::create('company_settings', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('gst_number')->unique();
            $table->text('address');
            $table->string('phone');
            $table->string('email');
            $table->string('website')->nullable();
            $table->string('logo_path')->nullable();
            $table->json('business_hours')->nullable();
            $table->decimal('default_wastage_percentage', 5, 2)->default(8.00);
            $table->decimal('default_making_charge_per_gram', 10, 2)->default(500.00);
            $table->string('default_currency', 3)->default('INR');
            $table->timestamps();
        });

        // Locations/Branches
        Schema::create('locations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('address');
            $table->string('phone')->nullable();
            $table->string('manager_name')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_main_branch')->default(false);
            $table->timestamps();
        });

        // Metal Types
        Schema::create('metals', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Gold, Silver, Platinum
            $table->string('purity'); // 22K, 18K, 925, 999
            $table->string('symbol', 10); // AU, AG, PT
            $table->decimal('current_rate_per_gram', 10, 2)->default(0);
            $table->timestamp('rate_updated_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique(['name', 'purity']);
        });

        // Metal Rate History
        Schema::create('metal_rate_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('metal_id')->constrained()->onDelete('cascade');
            $table->decimal('rate_per_gram', 10, 2);
            $table->date('rate_date');
            $table->string('source')->nullable(); // API, Manual
            $table->timestamps();

            $table->unique(['metal_id', 'rate_date']);
        });

        // Stone Types
        Schema::create('stones', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Diamond, Ruby, Emerald
            $table->string('type'); // Precious, Semi-precious
            $table->string('unit'); // Carat, Piece
            $table->decimal('base_rate_per_unit', 10, 2)->default(0);
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // HSN Codes
        Schema::create('hsn_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code', 10)->unique();
            $table->text('description');
            $table->decimal('cgst_rate', 5, 2)->default(0);
            $table->decimal('sgst_rate', 5, 2)->default(0);
            $table->decimal('igst_rate', 5, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Product Categories
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('image_path')->nullable();
            $table->foreignId('parent_id')->nullable()->constrained('categories')->onDelete('cascade');
            $table->foreignId('hsn_code_id')->nullable()->constrained()->onDelete('set null');
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Making Charge Templates
        Schema::create('making_charge_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('metal_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('charge_type', ['per_gram', 'percentage', 'fixed']);
            $table->decimal('charge_value', 10, 2);
            $table->decimal('min_charge', 10, 2)->nullable();
            $table->decimal('max_charge', 10, 2)->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('making_charge_templates');
        Schema::dropIfExists('categories');
        Schema::dropIfExists('hsn_codes');
        Schema::dropIfExists('stones');
        Schema::dropIfExists('metal_rate_history');
        Schema::dropIfExists('metals');
        Schema::dropIfExists('locations');
        Schema::dropIfExists('company_settings');
    }
};
