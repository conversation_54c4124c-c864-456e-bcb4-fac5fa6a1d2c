<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Chart of Accounts
        if (!Schema::hasTable('chart_of_accounts')) {
            Schema::create('chart_of_accounts', function (Blueprint $table) {
                $table->id();
                $table->string('account_code')->unique();
                $table->string('account_name');
                $table->enum('account_type', ['asset', 'liability', 'equity', 'revenue', 'expense']);
                $table->string('account_category');
                $table->foreignId('parent_account_id')->nullable()->constrained('chart_of_accounts')->onDelete('set null');
                $table->text('description')->nullable();
                $table->boolean('is_active')->default(true);
                $table->boolean('is_system_account')->default(false);
                $table->decimal('opening_balance', 15, 2)->default(0);
                $table->decimal('current_balance', 15, 2)->default(0);
                $table->enum('balance_type', ['debit', 'credit']);
                $table->boolean('tax_applicable')->default(false);
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['account_type']);
                $table->index(['account_category']);
                $table->index(['is_active']);
                $table->index(['parent_account_id']);
            });
        }

        // Journal Entries (create before account_transactions due to foreign key)
        if (!Schema::hasTable('journal_entries')) {
            Schema::create('journal_entries', function (Blueprint $table) {
                $table->id();
                $table->string('journal_number')->unique();
                $table->date('entry_date');
                $table->text('description');
                $table->string('reference_type')->nullable();
                $table->unsignedBigInteger('reference_id')->nullable();
                $table->decimal('total_debit', 15, 2)->default(0);
                $table->decimal('total_credit', 15, 2)->default(0);
                $table->enum('status', ['draft', 'posted', 'cancelled'])->default('draft');
                $table->datetime('posted_at')->nullable();
                $table->foreignId('posted_by')->nullable()->constrained('users')->onDelete('set null');
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->text('notes')->nullable();
                $table->timestamps();
                $table->softDeletes();

                $table->index(['entry_date']);
                $table->index(['status']);
                $table->index(['reference_type', 'reference_id']);
                $table->index(['posted_at']);
            });
        }

        // Account Transactions (create after journal_entries)
        if (!Schema::hasTable('account_transactions')) {
            Schema::create('account_transactions', function (Blueprint $table) {
                $table->id();
                $table->string('transaction_number')->unique();
                $table->foreignId('account_id')->constrained('chart_of_accounts')->onDelete('cascade');
                $table->date('transaction_date');
                $table->string('transaction_type');
                $table->decimal('debit_amount', 15, 2)->default(0);
                $table->decimal('credit_amount', 15, 2)->default(0);
                $table->text('description');
                $table->string('reference_type')->nullable();
                $table->unsignedBigInteger('reference_id')->nullable();
                $table->foreignId('journal_entry_id')->nullable()->constrained()->onDelete('set null');
                $table->boolean('reconciled')->default(false);
                $table->datetime('reconciled_at')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['account_id']);
                $table->index(['transaction_date']);
                $table->index(['transaction_type']);
                $table->index(['reference_type', 'reference_id']);
                $table->index(['reconciled']);
            });
        }

        // Expense Categories
        if (!Schema::hasTable('expense_categories')) {
            Schema::create('expense_categories', function (Blueprint $table) {
                $table->id();
                $table->string('name')->unique();
                $table->text('description')->nullable();
                $table->foreignId('account_id')->nullable()->constrained('chart_of_accounts')->onDelete('set null');
                $table->boolean('is_active')->default(true);
                $table->decimal('budget_limit', 15, 2)->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['is_active']);
                $table->index(['account_id']);
            });
        }

        // Expenses
        if (!Schema::hasTable('expenses')) {
            Schema::create('expenses', function (Blueprint $table) {
                $table->id();
                $table->string('expense_number')->unique();
                $table->date('expense_date');
                $table->foreignId('category_id')->constrained('expense_categories')->onDelete('cascade');
                $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('set null');
                $table->text('description');
                $table->decimal('amount', 15, 2);
                $table->decimal('tax_amount', 10, 2)->default(0);
                $table->decimal('total_amount', 15, 2);
                $table->string('payment_method')->nullable();
                $table->string('payment_reference')->nullable();
                $table->string('receipt_number')->nullable();
                $table->string('receipt_path')->nullable();
                $table->enum('status', ['pending', 'approved', 'paid', 'rejected', 'cancelled'])->default('pending');
                $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
                $table->datetime('approved_at')->nullable();
                $table->datetime('paid_at')->nullable();
                $table->text('notes')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['expense_date']);
                $table->index(['category_id']);
                $table->index(['supplier_id']);
                $table->index(['status']);
                $table->index(['approved_by']);
                $table->index(['created_by']);
            });
        }

        // Financial Reports (for caching report data)
        if (!Schema::hasTable('financial_reports')) {
            Schema::create('financial_reports', function (Blueprint $table) {
                $table->id();
                $table->string('report_type');
                $table->string('report_name');
                $table->date('start_date')->nullable();
                $table->date('end_date')->nullable();
                $table->json('report_data');
                $table->json('parameters')->nullable();
                $table->datetime('generated_at');
                $table->foreignId('generated_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();

                $table->index(['report_type']);
                $table->index(['start_date', 'end_date']);
                $table->index(['generated_at']);
            });
        }

        // Budget Plans
        if (!Schema::hasTable('budget_plans')) {
            Schema::create('budget_plans', function (Blueprint $table) {
                $table->id();
                $table->string('budget_name');
                $table->integer('budget_year');
                $table->enum('budget_period', ['monthly', 'quarterly', 'yearly']);
                $table->date('start_date');
                $table->date('end_date');
                $table->enum('status', ['draft', 'active', 'completed', 'cancelled'])->default('draft');
                $table->text('description')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
                $table->datetime('approved_at')->nullable();
                $table->timestamps();
                $table->softDeletes();

                $table->index(['budget_year']);
                $table->index(['budget_period']);
                $table->index(['status']);
                $table->index(['start_date', 'end_date']);
            });
        }

        // Budget Line Items
        if (!Schema::hasTable('budget_line_items')) {
            Schema::create('budget_line_items', function (Blueprint $table) {
                $table->id();
                $table->foreignId('budget_plan_id')->constrained()->onDelete('cascade');
                $table->foreignId('account_id')->constrained('chart_of_accounts')->onDelete('cascade');
                $table->foreignId('category_id')->nullable()->constrained('expense_categories')->onDelete('set null');
                $table->decimal('budgeted_amount', 15, 2);
                $table->decimal('actual_amount', 15, 2)->default(0);
                $table->decimal('variance_amount', 15, 2)->default(0);
                $table->decimal('variance_percentage', 5, 2)->default(0);
                $table->text('notes')->nullable();
                $table->timestamps();

                $table->index(['budget_plan_id']);
                $table->index(['account_id']);
                $table->index(['category_id']);
            });
        }

        // Tax Settings
        if (!Schema::hasTable('tax_settings')) {
            Schema::create('tax_settings', function (Blueprint $table) {
                $table->id();
                $table->string('tax_name');
                $table->string('tax_code')->unique();
                $table->decimal('tax_rate', 5, 2);
                $table->enum('tax_type', ['percentage', 'fixed']);
                $table->boolean('is_compound')->default(false);
                $table->boolean('is_active')->default(true);
                $table->date('effective_from');
                $table->date('effective_to')->nullable();
                $table->foreignId('tax_account_id')->nullable()->constrained('chart_of_accounts')->onDelete('set null');
                $table->text('description')->nullable();
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['tax_code']);
                $table->index(['is_active']);
                $table->index(['effective_from', 'effective_to']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tax_settings');
        Schema::dropIfExists('budget_line_items');
        Schema::dropIfExists('budget_plans');
        Schema::dropIfExists('financial_reports');
        Schema::dropIfExists('expenses');
        Schema::dropIfExists('expense_categories');
        Schema::dropIfExists('journal_entries');
        Schema::dropIfExists('account_transactions');
        Schema::dropIfExists('chart_of_accounts');
    }
};
