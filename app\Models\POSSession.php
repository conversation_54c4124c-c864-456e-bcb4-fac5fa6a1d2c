<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class POSSession extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pos_sessions';

    protected $fillable = [
        'session_number',
        'user_id',
        'location_id',
        'terminal_id',
        'opened_at',
        'closed_at',
        'opening_cash',
        'closing_cash',
        'expected_cash',
        'cash_difference',
        'total_sales',
        'total_transactions',
        'status',
        'notes',
        'closed_by',
    ];

    protected $casts = [
        'opened_at' => 'datetime',
        'closed_at' => 'datetime',
        'opening_cash' => 'decimal:2',
        'closing_cash' => 'decimal:2',
        'expected_cash' => 'decimal:2',
        'cash_difference' => 'decimal:2',
        'total_sales' => 'decimal:2',
        'total_transactions' => 'integer',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($session) {
            if (!$session->session_number) {
                $session->session_number = static::generateSessionNumber();
            }
        });
    }

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function closedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'closed_by');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(POSTransaction::class);
    }

    public function cashMovements(): HasMany
    {
        return $this->hasMany(CashMovement::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('opened_at', today());
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'active' => 'Active',
            'closed' => 'Closed',
            'suspended' => 'Suspended',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'active' => 'green',
            'closed' => 'gray',
            'suspended' => 'yellow',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getDurationAttribute()
    {
        $start = $this->opened_at;
        $end = $this->closed_at ?: now();

        return $start->diffForHumans($end, true);
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    public function getCanCloseAttribute()
    {
        return $this->status === 'active';
    }

    public function getCashVarianceAttribute()
    {
        if ($this->expected_cash && $this->closing_cash) {
            return $this->closing_cash - $this->expected_cash;
        }
        return 0;
    }

    /**
     * Business Logic Methods
     */
    public static function generateSessionNumber()
    {
        $prefix = 'POS';
        $date = now()->format('ymd');

        $lastSession = static::whereDate('created_at', today())
                            ->orderBy('id', 'desc')
                            ->first();

        $sequence = $lastSession ? (int) substr($lastSession->session_number, -3) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    public static function openSession($userId, $locationId, $terminalId, $openingCash, $notes = null)
    {
        // Check if user has an active session
        $activeSession = static::where('user_id', $userId)
                              ->where('status', 'active')
                              ->first();

        if ($activeSession) {
            throw new \Exception('User already has an active POS session');
        }

        return static::create([
            'user_id' => $userId,
            'location_id' => $locationId,
            'terminal_id' => $terminalId,
            'opened_at' => now(),
            'opening_cash' => $openingCash,
            'status' => 'active',
            'notes' => $notes,
        ]);
    }

    public function closeSession($closingCash, $notes = null, $closedBy = null)
    {
        if ($this->status !== 'active') {
            throw new \Exception('Only active sessions can be closed');
        }

        // Calculate expected cash
        $cashSales = $this->transactions()
                         ->where('payment_method', 'cash')
                         ->where('status', 'completed')
                         ->sum('total_amount');

        $cashMovements = $this->cashMovements()
                             ->where('movement_type', 'cash_in')
                             ->sum('amount') -
                        $this->cashMovements()
                             ->where('movement_type', 'cash_out')
                             ->sum('amount');

        $expectedCash = $this->opening_cash + $cashSales + $cashMovements;

        $this->update([
            'closed_at' => now(),
            'closing_cash' => $closingCash,
            'expected_cash' => $expectedCash,
            'cash_difference' => $closingCash - $expectedCash,
            'status' => 'closed',
            'notes' => $notes ? ($this->notes . "\n\nClosing Notes: " . $notes) : $this->notes,
            'closed_by' => $closedBy ?: auth()->id(),
        ]);

        return $this;
    }

    public function addCashMovement($type, $amount, $reason, $reference = null)
    {
        return $this->cashMovements()->create([
            'movement_type' => $type,
            'amount' => $amount,
            'reason' => $reason,
            'reference' => $reference,
            'created_by' => auth()->id(),
        ]);
    }

    public function getSessionSummary()
    {
        $transactions = $this->transactions()->where('status', 'completed');

        $paymentSummary = $transactions->groupBy('payment_method')
                                     ->map(function ($group) {
                                         return [
                                             'count' => $group->count(),
                                             'total' => $group->sum('total_amount'),
                                         ];
                                     });

        return [
            'session_number' => $this->session_number,
            'user_name' => $this->user->name,
            'location_name' => $this->location->name,
            'opened_at' => $this->opened_at->format('Y-m-d H:i:s'),
            'closed_at' => $this->closed_at?->format('Y-m-d H:i:s'),
            'duration' => $this->duration,
            'status' => $this->status_display,
            'opening_cash' => $this->opening_cash,
            'closing_cash' => $this->closing_cash,
            'expected_cash' => $this->expected_cash,
            'cash_difference' => $this->cash_difference,
            'total_sales' => $this->total_sales,
            'total_transactions' => $this->total_transactions,
            'payment_summary' => $paymentSummary,
            'cash_variance' => $this->cash_variance,
        ];
    }

    public function updateSessionTotals()
    {
        $completedTransactions = $this->transactions()->where('status', 'completed');

        $this->update([
            'total_sales' => $completedTransactions->sum('total_amount'),
            'total_transactions' => $completedTransactions->count(),
        ]);

        return $this;
    }

    public function suspendSession($reason = null)
    {
        if ($this->status !== 'active') {
            throw new \Exception('Only active sessions can be suspended');
        }

        $this->update([
            'status' => 'suspended',
            'notes' => $reason ? ($this->notes . "\n\nSuspended: " . $reason) : $this->notes,
        ]);

        return $this;
    }

    public function resumeSession()
    {
        if ($this->status !== 'suspended') {
            throw new \Exception('Only suspended sessions can be resumed');
        }

        $this->update([
            'status' => 'active',
            'notes' => $this->notes . "\n\nResumed at: " . now()->format('Y-m-d H:i:s'),
        ]);

        return $this;
    }

    public function getTransactionsByHour()
    {
        return $this->transactions()
                   ->where('status', 'completed')
                   ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count, SUM(total_amount) as total')
                   ->groupBy('hour')
                   ->orderBy('hour')
                   ->get();
    }

    public function getTopSellingProducts($limit = 10)
    {
        return Product::whereHas('invoiceItems.invoice.posTransaction', function ($query) {
                    $query->where('pos_session_id', $this->id);
                })
                ->withSum(['invoiceItems as total_quantity' => function ($query) {
                    $query->whereHas('invoice.posTransaction', function ($subQuery) {
                        $subQuery->where('pos_session_id', $this->id);
                    });
                }], 'quantity')
                ->withSum(['invoiceItems as total_value' => function ($query) {
                    $query->whereHas('invoice.posTransaction', function ($subQuery) {
                        $subQuery->where('pos_session_id', $this->id);
                    });
                }], 'total_amount')
                ->orderBy('total_value', 'desc')
                ->limit($limit)
                ->get();
    }
}
