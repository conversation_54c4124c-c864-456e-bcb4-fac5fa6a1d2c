<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'sku' => $this->sku,
            'barcode' => $this->barcode,
            'huid_number' => $this->huid_number,
            'tag_number' => $this->tag_number,
            'description' => $this->description,
            
            // Weights
            'gross_weight' => $this->gross_weight,
            'net_weight' => $this->net_weight,
            'stone_weight' => $this->stone_weight,
            
            // Pricing
            'making_charges' => $this->making_charges,
            'wastage_percentage' => $this->wastage_percentage,
            'stone_charges' => $this->stone_charges,
            'other_charges' => $this->other_charges,
            'total_amount' => $this->total_amount,
            
            // Status
            'status' => $this->status,
            'is_featured' => $this->is_featured,
            'view_count' => $this->view_count,
            
            // Relationships
            'category' => new CategoryResource($this->whenLoaded('category')),
            'metal' => new MetalResource($this->whenLoaded('metal')),
            'hsn_code' => new HsnCodeResource($this->whenLoaded('hsnCode')),
            'images' => ProductImageResource::collection($this->whenLoaded('images')),
            'stones' => ProductStoneResource::collection($this->whenLoaded('stones')),
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // SEO
            'seo_meta' => $this->seo_meta,
            
            // Computed fields
            'current_metal_rate' => $this->when($this->relationLoaded('metal'), function () {
                return $this->metal?->current_rate;
            }),
            
            'calculated_price' => $this->when($request->routeIs('api.*'), function () {
                return $this->calculateCurrentPrice();
            }),
            
            'availability' => $this->when($request->routeIs('api.*'), function () {
                return [
                    'in_stock' => $this->inventory_count > 0,
                    'quantity' => $this->inventory_count ?? 0,
                    'locations' => $this->whenLoaded('inventory', function () {
                        return $this->inventory->groupBy('location.name')->map(function ($items) {
                            return $items->sum('quantity');
                        });
                    }),
                ];
            }),
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'version' => '1.0',
                'generated_at' => now()->toISOString(),
            ],
        ];
    }
}
