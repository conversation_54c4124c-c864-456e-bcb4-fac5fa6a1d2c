<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\ActivityLog;
use App\Models\SystemSetting;
use App\Models\BackupLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SystemAdministrationController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:system_administration')->only(['index']);
        $this->middleware('permission:manage_users')->only(['users']);
        $this->middleware('permission:view_system_logs')->only(['logs']);
        $this->middleware('permission:system_settings')->only(['settings']);
        $this->middleware('permission:manage_system_health')->only(['health', 'systemHealth']);
        $this->middleware('permission:manage_database')->only(['database', 'optimizeDatabase']);
        $this->middleware('permission:manage_cache')->only(['cache', 'clearCache']);
        $this->middleware('permission:manage_queues')->only(['queues', 'manageQueues']);
    }

    /**
     * System administration dashboard
     */
    public function index()
    {
        $systemStats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'total_sessions' => $this->getActiveSessions(),
            'system_uptime' => $this->getSystemUptime(),
            'database_size' => $this->getDatabaseSize(),
            'storage_usage' => $this->getStorageUsage(),
            'cache_usage' => $this->getCacheUsage(),
            'last_backup' => $this->getLastBackupInfo(),
        ];

        $recentActivities = ActivityLog::with('user')
                                     ->orderByDesc('created_at')
                                     ->limit(10)
                                     ->get();

        $systemHealth = $this->getSystemHealthStatus();
        $securityAlerts = $this->getSecurityAlerts();

        return view('admin.system.index', compact(
            'systemStats', 'recentActivities', 'systemHealth', 'securityAlerts'
        ));
    }

    /**
     * User management dashboard
     */
    public function users(Request $request)
    {
        $query = User::with(['roles', 'permissions']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Role filter
        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        $userStats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'admin_users' => User::whereHas('roles', function ($q) {
                $q->where('name', 'admin');
            })->count(),
            'recent_logins' => User::where('last_login_at', '>=', now()->subDays(7))->count(),
        ];

        return view('admin.system.users', compact('users', 'userStats'));
    }

    /**
     * System logs viewer
     */
    public function logs(Request $request)
    {
        $logType = $request->get('type', 'activity'); // activity, error, security, audit
        $dateRange = $request->get('date_range', '7'); // days

        $logs = [];
        $logStats = [];

        switch ($logType) {
            case 'activity':
                $logs = ActivityLog::with('user')
                                 ->where('created_at', '>=', now()->subDays($dateRange))
                                 ->orderByDesc('created_at')
                                 ->paginate(50);
                break;
            case 'error':
                $logs = $this->getErrorLogs($dateRange);
                break;
            case 'security':
                $logs = $this->getSecurityLogs($dateRange);
                break;
            case 'audit':
                $logs = $this->getAuditLogs($dateRange);
                break;
        }

        $logStats = [
            'total_activities' => ActivityLog::where('created_at', '>=', now()->subDays($dateRange))->count(),
            'unique_users' => ActivityLog::where('created_at', '>=', now()->subDays($dateRange))
                                       ->distinct('user_id')->count(),
            'error_count' => $this->getErrorCount($dateRange),
            'security_events' => $this->getSecurityEventCount($dateRange),
        ];

        return view('admin.system.logs', compact('logs', 'logStats', 'logType', 'dateRange'));
    }

    /**
     * System settings management
     */
    public function settings(Request $request)
    {
        if ($request->isMethod('post')) {
            return $this->updateSettings($request);
        }

        $settings = SystemSetting::all()->pluck('value', 'key');
        $settingsGroups = [
            'general' => [
                'app_name' => 'Application Name',
                'app_description' => 'Application Description',
                'timezone' => 'Default Timezone',
                'date_format' => 'Date Format',
                'currency' => 'Default Currency',
            ],
            'security' => [
                'session_timeout' => 'Session Timeout (minutes)',
                'password_min_length' => 'Minimum Password Length',
                'max_login_attempts' => 'Maximum Login Attempts',
                'lockout_duration' => 'Account Lockout Duration (minutes)',
                'two_factor_enabled' => 'Enable Two-Factor Authentication',
            ],
            'backup' => [
                'auto_backup_enabled' => 'Enable Automatic Backups',
                'backup_frequency' => 'Backup Frequency',
                'backup_retention_days' => 'Backup Retention (days)',
                'backup_storage' => 'Backup Storage Location',
            ],
            'notifications' => [
                'email_notifications' => 'Enable Email Notifications',
                'sms_notifications' => 'Enable SMS Notifications',
                'low_stock_threshold' => 'Low Stock Alert Threshold',
                'admin_email' => 'Administrator Email',
            ],
        ];

        return view('admin.system.settings', compact('settings', 'settingsGroups'));
    }

    /**
     * Database management
     */
    public function database()
    {
        $databaseStats = [
            'total_size' => $this->getDatabaseSize(),
            'table_count' => $this->getTableCount(),
            'record_count' => $this->getTotalRecordCount(),
            'index_size' => $this->getIndexSize(),
            'last_optimization' => $this->getLastOptimizationDate(),
        ];

        $tableStats = $this->getTableStatistics();
        $recentBackups = BackupLog::orderByDesc('created_at')->limit(10)->get();

        return view('admin.system.database', compact('databaseStats', 'tableStats', 'recentBackups'));
    }

    /**
     * System backup management
     */
    public function backups()
    {
        $backups = BackupLog::orderByDesc('created_at')->paginate(20);
        
        $backupStats = [
            'total_backups' => BackupLog::count(),
            'successful_backups' => BackupLog::where('status', 'completed')->count(),
            'failed_backups' => BackupLog::where('status', 'failed')->count(),
            'total_backup_size' => BackupLog::where('status', 'completed')->sum('file_size'),
            'last_backup' => BackupLog::where('status', 'completed')->latest()->first(),
        ];

        return view('admin.system.backups', compact('backups', 'backupStats'));
    }

    /**
     * Create database backup
     */
    public function createBackup(Request $request)
    {
        try {
            $backupType = $request->get('type', 'full'); // full, data_only, structure_only
            $includeFiles = $request->boolean('include_files', false);

            $backup = BackupLog::create([
                'type' => $backupType,
                'status' => 'in_progress',
                'started_at' => now(),
                'created_by' => auth()->id(),
            ]);

            // Queue backup job
            dispatch(new \App\Jobs\CreateDatabaseBackup($backup->id, $backupType, $includeFiles));

            return response()->json([
                'success' => true,
                'message' => 'Backup process started successfully.',
                'backup_id' => $backup->id
            ]);
        } catch (\Exception $e) {
            Log::error('Backup creation failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to start backup process: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * System maintenance
     */
    public function maintenance()
    {
        $maintenanceStats = [
            'cache_size' => $this->getCacheSize(),
            'log_files_size' => $this->getLogFilesSize(),
            'temp_files_size' => $this->getTempFilesSize(),
            'session_files_count' => $this->getSessionFilesCount(),
            'last_maintenance' => $this->getLastMaintenanceDate(),
        ];

        $maintenanceTasks = [
            'clear_cache' => 'Clear Application Cache',
            'clear_logs' => 'Clear Old Log Files',
            'optimize_database' => 'Optimize Database Tables',
            'clean_temp_files' => 'Clean Temporary Files',
            'update_search_index' => 'Update Search Index',
        ];

        return view('admin.system.maintenance', compact('maintenanceStats', 'maintenanceTasks'));
    }

    /**
     * Run maintenance task
     */
    public function runMaintenanceTask(Request $request)
    {
        $task = $request->get('task');
        
        try {
            switch ($task) {
                case 'clear_cache':
                    Artisan::call('cache:clear');
                    Artisan::call('config:clear');
                    Artisan::call('view:clear');
                    $message = 'Application cache cleared successfully.';
                    break;
                    
                case 'clear_logs':
                    $this->clearOldLogs();
                    $message = 'Old log files cleared successfully.';
                    break;
                    
                case 'optimize_database':
                    $this->optimizeDatabase();
                    $message = 'Database tables optimized successfully.';
                    break;
                    
                case 'clean_temp_files':
                    $this->cleanTempFiles();
                    $message = 'Temporary files cleaned successfully.';
                    break;
                    
                case 'update_search_index':
                    // Implement search index update
                    $message = 'Search index updated successfully.';
                    break;
                    
                default:
                    throw new \Exception('Unknown maintenance task.');
            }

            // Log maintenance activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'action' => 'maintenance_task',
                'description' => "Executed maintenance task: {$task}",
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            Log::error("Maintenance task failed: {$task} - " . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Maintenance task failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Security monitoring
     */
    public function security()
    {
        $securityStats = [
            'failed_logins_today' => $this->getFailedLoginsToday(),
            'blocked_ips' => $this->getBlockedIpsCount(),
            'suspicious_activities' => $this->getSuspiciousActivitiesCount(),
            'security_alerts' => $this->getSecurityAlertsCount(),
        ];

        $recentSecurityEvents = $this->getRecentSecurityEvents();
        $blockedIps = $this->getBlockedIps();

        return view('admin.system.security', compact('securityStats', 'recentSecurityEvents', 'blockedIps'));
    }

    // Helper Methods
    private function getActiveSessions()
    {
        return DB::table('sessions')->count();
    }

    private function getSystemUptime()
    {
        // This would typically come from server monitoring
        return '15 days, 3 hours';
    }

    private function getDatabaseSize()
    {
        $size = DB::select("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'size' FROM information_schema.tables WHERE table_schema = ?", [config('database.connections.mysql.database')]);
        return $size[0]->size ?? 0;
    }

    private function getStorageUsage()
    {
        $totalSize = 0;
        $files = Storage::allFiles('public');
        foreach ($files as $file) {
            $totalSize += Storage::size($file);
        }
        return round($totalSize / 1024 / 1024, 2); // MB
    }

    private function getCacheUsage()
    {
        // Implementation depends on cache driver
        return 0;
    }

    private function getLastBackupInfo()
    {
        return BackupLog::where('status', 'completed')->latest()->first();
    }

    private function getSystemHealthStatus()
    {
        return [
            'database' => 'healthy',
            'cache' => 'healthy',
            'storage' => 'healthy',
            'queue' => 'healthy',
        ];
    }

    private function getSecurityAlerts()
    {
        return [
            // Implementation for security alerts
        ];
    }

    private function updateSettings(Request $request)
    {
        $settings = $request->except(['_token', '_method']);
        
        foreach ($settings as $key => $value) {
            SystemSetting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return redirect()->route('admin.system.settings')
                        ->with('success', 'Settings updated successfully.');
    }

    // Additional helper methods would be implemented here...
    private function getErrorLogs($days) { return collect(); }
    private function getSecurityLogs($days) { return collect(); }
    private function getAuditLogs($days) { return collect(); }
    private function getErrorCount($days) { return 0; }
    private function getSecurityEventCount($days) { return 0; }
    private function getTableCount() { return 0; }
    private function getTotalRecordCount() { return 0; }
    private function getIndexSize() { return 0; }
    private function getLastOptimizationDate() { return null; }
    private function getTableStatistics() { return []; }
    private function getCacheSize() { return 0; }
    private function getLogFilesSize() { return 0; }
    private function getTempFilesSize() { return 0; }
    private function getSessionFilesCount() { return 0; }
    private function getLastMaintenanceDate() { return null; }
    private function clearOldLogs() { }
    private function optimizeDatabase() { }
    private function cleanTempFiles() { }
    private function getFailedLoginsToday() { return 0; }
    private function getBlockedIpsCount() { return 0; }
    private function getSuspiciousActivitiesCount() { return 0; }
    private function getSecurityAlertsCount() { return 0; }
    private function getRecentSecurityEvents() { return []; }
    private function getBlockedIps() { return []; }
}
