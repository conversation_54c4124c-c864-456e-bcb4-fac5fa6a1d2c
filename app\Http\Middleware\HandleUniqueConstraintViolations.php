<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Database\QueryException;
use Illuminate\Database\UniqueConstraintViolationException;
use App\Exceptions\UniqueConstraintHandler;
use App\Services\UniqueConstraintService;
use Illuminate\Support\Facades\Log;

class HandleUniqueConstraintViolations
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            return $next($request);
        } catch (UniqueConstraintViolationException $e) {
            Log::error('🚨 UniqueConstraintViolationException caught in middleware', [
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'data' => $request->all(),
            ]);
            
            return UniqueConstraintHandler::handle($e, $request);
        } catch (QueryException $e) {
            // Only handle unique constraint violations, let other QueryExceptions bubble up
            if (UniqueConstraintService::isUniqueConstraintViolation($e)) {
                Log::error('🚨 QueryException (Unique Constraint) caught in middleware', [
                    'exception' => get_class($e),
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'url' => $request->fullUrl(),
                    'method' => $request->method(),
                    'data' => $request->all(),
                ]);
                
                return UniqueConstraintHandler::handle($e, $request);
            }
            
            // Re-throw if it's not a unique constraint violation (like missing column errors)
            throw $e;
        }
    }
}