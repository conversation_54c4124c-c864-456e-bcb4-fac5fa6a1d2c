<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ChartOfAccount;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ChartOfAccountsController extends Controller
{
    /**
     * Display chart of accounts
     */
    public function index(Request $request)
    {
        $query = ChartOfAccount::with(['parent', 'children']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('account_name', 'like', "%{$search}%")
                  ->orWhere('account_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by account type
        if ($request->filled('account_type')) {
            $query->where('account_type', $request->account_type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $accounts = $query->orderBy('account_code')->paginate(20);

        // Get account type statistics
        $stats = [
            'total_accounts' => ChartOfAccount::count(),
            'active_accounts' => ChartOfAccount::where('is_active', true)->count(),
            'asset_accounts' => ChartOfAccount::where('account_type', 'asset')->count(),
            'liability_accounts' => ChartOfAccount::where('account_type', 'liability')->count(),
            'equity_accounts' => ChartOfAccount::where('account_type', 'equity')->count(),
            'revenue_accounts' => ChartOfAccount::where('account_type', 'revenue')->count(),
            'expense_accounts' => ChartOfAccount::where('account_type', 'expense')->count(),
        ];

        return view('admin.financial.chart-of-accounts.index', compact('accounts', 'stats'));
    }

    /**
     * Show account hierarchy
     */
    public function hierarchy()
    {
        $accountTypes = [
            'asset' => ChartOfAccount::where('account_type', 'asset')
                                   ->where('is_active', true)
                                   ->whereNull('parent_account_id')
                                   ->with('children')
                                   ->orderBy('account_code')
                                   ->get(),
            'liability' => ChartOfAccount::where('account_type', 'liability')
                                       ->where('is_active', true)
                                       ->whereNull('parent_account_id')
                                       ->with('children')
                                       ->orderBy('account_code')
                                       ->get(),
            'equity' => ChartOfAccount::where('account_type', 'equity')
                                    ->where('is_active', true)
                                    ->whereNull('parent_account_id')
                                    ->with('children')
                                    ->orderBy('account_code')
                                    ->get(),
            'revenue' => ChartOfAccount::where('account_type', 'revenue')
                                     ->where('is_active', true)
                                     ->whereNull('parent_account_id')
                                     ->with('children')
                                     ->orderBy('account_code')
                                     ->get(),
            'expense' => ChartOfAccount::where('account_type', 'expense')
                                     ->where('is_active', true)
                                     ->whereNull('parent_account_id')
                                     ->with('children')
                                     ->orderBy('account_code')
                                     ->get(),
        ];

        return view('admin.financial.chart-of-accounts.hierarchy', compact('accountTypes'));
    }

    /**
     * Show create form
     */
    public function create()
    {
        $parentAccounts = ChartOfAccount::where('is_active', true)
                                      ->whereNull('parent_account_id')
                                      ->orderBy('account_code')
                                      ->get();

        return view('admin.financial.chart-of-accounts.create', compact('parentAccounts'));
    }

    /**
     * Store new account
     */
    public function store(Request $request)
    {
        $request->validate([
            'account_name' => 'required|string|max:255',
            'account_type' => 'required|in:asset,liability,equity,revenue,expense',
            'account_category' => 'nullable|string|max:100',
            'parent_account_id' => 'nullable|exists:chart_of_accounts,id',
            'description' => 'nullable|string|max:500',
            'opening_balance' => 'nullable|numeric',
            'balance_type' => 'required|in:debit,credit',
            'tax_applicable' => 'boolean',
        ]);

        $account = ChartOfAccount::create([
            'account_name' => $request->account_name,
            'account_type' => $request->account_type,
            'account_category' => $request->account_category,
            'parent_account_id' => $request->parent_account_id,
            'description' => $request->description,
            'opening_balance' => $request->opening_balance ?? 0,
            'current_balance' => $request->opening_balance ?? 0,
            'balance_type' => $request->balance_type,
            'tax_applicable' => $request->boolean('tax_applicable'),
            'is_active' => true,
            'is_system_account' => false,
        ]);

        return redirect()->route('admin.chart-of-accounts.index')
                        ->with('success', 'Account created successfully.');
    }

    /**
     * Show account details
     */
    public function show(ChartOfAccount $chartOfAccount)
    {
        $chartOfAccount->load(['parent', 'children', 'journalEntries.journalEntry']);

        // Get recent transactions
        $recentTransactions = $chartOfAccount->journalEntries()
                                           ->with('journalEntry')
                                           ->whereHas('journalEntry', function ($q) {
                                               $q->where('status', 'posted');
                                           })
                                           ->orderByDesc('created_at')
                                           ->limit(10)
                                           ->get();

        // Calculate monthly balances for chart
        $monthlyBalances = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i)->endOfMonth();
            $balance = $this->getAccountBalanceAsOf($chartOfAccount->id, $date->format('Y-m-d'));
            $monthlyBalances[] = [
                'month' => $date->format('M Y'),
                'balance' => $balance
            ];
        }

        return view('admin.financial.chart-of-accounts.show', compact(
            'chartOfAccount', 'recentTransactions', 'monthlyBalances'
        ));
    }

    /**
     * Show edit form
     */
    public function edit(ChartOfAccount $chartOfAccount)
    {
        if ($chartOfAccount->is_system_account) {
            return redirect()->route('admin.chart-of-accounts.index')
                           ->with('error', 'System accounts cannot be edited.');
        }

        $parentAccounts = ChartOfAccount::where('is_active', true)
                                      ->whereNull('parent_account_id')
                                      ->where('id', '!=', $chartOfAccount->id)
                                      ->orderBy('account_code')
                                      ->get();

        return view('admin.financial.chart-of-accounts.edit', compact('chartOfAccount', 'parentAccounts'));
    }

    /**
     * Update account
     */
    public function update(Request $request, ChartOfAccount $chartOfAccount)
    {
        if ($chartOfAccount->is_system_account) {
            return redirect()->route('admin.chart-of-accounts.index')
                           ->with('error', 'System accounts cannot be modified.');
        }

        $request->validate([
            'account_name' => 'required|string|max:255',
            'account_type' => 'required|in:asset,liability,equity,revenue,expense',
            'account_category' => 'nullable|string|max:100',
            'parent_account_id' => [
                'nullable',
                'exists:chart_of_accounts,id',
                Rule::notIn([$chartOfAccount->id]) // Prevent self-reference
            ],
            'description' => 'nullable|string|max:500',
            'balance_type' => 'required|in:debit,credit',
            'tax_applicable' => 'boolean',
        ]);

        $chartOfAccount->update([
            'account_name' => $request->account_name,
            'account_type' => $request->account_type,
            'account_category' => $request->account_category,
            'parent_account_id' => $request->parent_account_id,
            'description' => $request->description,
            'balance_type' => $request->balance_type,
            'tax_applicable' => $request->boolean('tax_applicable'),
        ]);

        return redirect()->route('admin.chart-of-accounts.index')
                        ->with('success', 'Account updated successfully.');
    }

    /**
     * Toggle account status
     */
    public function toggleStatus(ChartOfAccount $chartOfAccount)
    {
        if ($chartOfAccount->is_system_account) {
            return response()->json([
                'success' => false,
                'message' => 'System accounts cannot be deactivated.'
            ]);
        }

        // Check if account has transactions
        if (!$chartOfAccount->is_active && $chartOfAccount->journalEntries()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot deactivate account with existing transactions.'
            ]);
        }

        $chartOfAccount->update(['is_active' => !$chartOfAccount->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Account status updated successfully.',
            'is_active' => $chartOfAccount->is_active
        ]);
    }

    /**
     * Delete account
     */
    public function destroy(ChartOfAccount $chartOfAccount)
    {
        if ($chartOfAccount->is_system_account) {
            return redirect()->route('admin.chart-of-accounts.index')
                           ->with('error', 'System accounts cannot be deleted.');
        }

        if (!$chartOfAccount->canBeDeleted()) {
            return redirect()->route('admin.chart-of-accounts.index')
                           ->with('error', 'Account cannot be deleted as it has transactions or child accounts.');
        }

        $chartOfAccount->delete();

        return redirect()->route('admin.chart-of-accounts.index')
                        ->with('success', 'Account deleted successfully.');
    }

    /**
     * Initialize default accounts
     */
    public function initializeDefaults()
    {
        try {
            ChartOfAccount::createDefaultAccounts();
            
            return redirect()->route('admin.chart-of-accounts.index')
                           ->with('success', 'Default chart of accounts created successfully.');
        } catch (\Exception $e) {
            return redirect()->route('admin.chart-of-accounts.index')
                           ->with('error', 'Error creating default accounts: ' . $e->getMessage());
        }
    }

    /**
     * Get account balance as of a specific date
     */
    private function getAccountBalanceAsOf($accountId, $asOfDate)
    {
        $account = ChartOfAccount::find($accountId);
        if (!$account) return 0;

        $debitTotal = $account->journalEntries()
                            ->whereHas('journalEntry', function ($q) use ($asOfDate) {
                                $q->where('status', 'posted')
                                  ->where('transaction_date', '<=', $asOfDate);
                            })
                            ->sum('debit_amount');

        $creditTotal = $account->journalEntries()
                             ->whereHas('journalEntry', function ($q) use ($asOfDate) {
                                 $q->where('status', 'posted')
                                   ->where('transaction_date', '<=', $asOfDate);
                             })
                             ->sum('credit_amount');

        if (in_array($account->account_type, ['asset', 'expense'])) {
            return $account->opening_balance + $debitTotal - $creditTotal;
        }
        
        return $account->opening_balance + $creditTotal - $debitTotal;
    }
}
