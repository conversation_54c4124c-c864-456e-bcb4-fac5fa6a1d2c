<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create {--email=<EMAIL>} {--password=password123} {--name=Admin User}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create or update admin user credentials';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->option('email');
        $password = $this->option('password');
        $name = $this->option('name');

        // Create or update admin user
        $admin = User::updateOrCreate(
            ['email' => $email],
            [
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'phone' => '+91 98765 43210',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create super admin role if it doesn't exist
        $superAdminRole = Role::firstOrCreate(['name' => 'super-admin']);
        
        // Assign super admin role to the admin user
        if (!$admin->hasRole('super-admin')) {
            $admin->assignRole('super-admin');
        }

        $this->info('Admin user created/updated successfully!');
        $this->info('');
        $this->info('=== ADMIN CREDENTIALS ===');
        $this->info("Email: {$email}");
        $this->info("Password: {$password}");
        $this->info('Role: Super Admin');
        $this->info('');
        $this->info('You can now login to:');
        $this->info('- Admin Panel: http://localhost:8000/login');
        $this->info('- Website: http://localhost:8000/auth/login');
        $this->info('');
    }
}
