<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\SavingSchemePlan;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Database\QueryException;
use App\Exceptions\UniqueConstraintHandler;
use App\Services\UniqueConstraintService;

class TriggerUniqueConstraintTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:trigger-unique-constraint';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test triggering a unique constraint violation to verify handling';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Unique Constraint Violation Handling');
        $this->newLine();

        // Test 1: Create duplicate user
        $this->info('Test 1: Creating duplicate user to trigger UniqueConstraintViolationException');
        
        $testEmail = '<EMAIL>';
        
        try {
            // Create first user
            $user1 = User::create([
                'name' => 'Test User 1',
                'email' => $testEmail,
                'password' => bcrypt('password'),
            ]);
            $this->info("✅ Created first user with ID: {$user1->id}");
            
            // Try to create duplicate - this should trigger the exception
            $user2 = User::create([
                'name' => 'Test User 2',
                'email' => $testEmail, // Same email - should cause violation
                'password' => bcrypt('password'),
            ]);
            
            $this->error("❌ Duplicate user was created - this shouldn't happen!");
            
        } catch (UniqueConstraintViolationException $e) {
            $this->info("✅ UniqueConstraintViolationException caught successfully!");
            $this->line("   Exception: " . get_class($e));
            $this->line("   Message: " . $e->getMessage());
            $this->line("   File: " . $e->getFile() . ":" . $e->getLine());
            
            // Test our handler
            $this->info("Testing UniqueConstraintHandler...");
            try {
                $response = UniqueConstraintHandler::handle($e, null);
                $this->info("✅ Handler processed the exception successfully");
                if (method_exists($response, 'getContent')) {
                    $content = json_decode($response->getContent(), true);
                    $this->line("   Response: " . ($content['message'] ?? 'No message'));
                }
            } catch (\Exception $handlerException) {
                $this->error("❌ Handler failed: " . $handlerException->getMessage());
            }
            
        } catch (QueryException $e) {
            $this->info("✅ QueryException caught (this is also expected)");
            $this->line("   Exception: " . get_class($e));
            $this->line("   Message: " . $e->getMessage());
            $this->line("   File: " . $e->getFile() . ":" . $e->getLine());
            
            // Check if it's a unique constraint violation
            $isUniqueViolation = UniqueConstraintService::isUniqueConstraintViolation($e);
            $this->info($isUniqueViolation ? "✅ Correctly identified as unique constraint violation" : "❌ Not identified as unique constraint violation");
            
            if ($isUniqueViolation) {
                try {
                    $response = UniqueConstraintHandler::handle($e, null);
                    $this->info("✅ Handler processed the QueryException successfully");
                } catch (\Exception $handlerException) {
                    $this->error("❌ Handler failed: " . $handlerException->getMessage());
                }
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Unexpected exception: " . get_class($e));
            $this->error("   Message: " . $e->getMessage());
            $this->error("   File: " . $e->getFile() . ":" . $e->getLine());
        }

        $this->newLine();
        
        // Test 2: Test with SavingSchemePlan using the trait
        $this->info('Test 2: Testing SavingSchemePlan with HandlesUniqueConstraints trait');
        
        try {
            // Create first plan
            $plan1 = SavingSchemePlan::create([
                'plan_name' => 'Duplicate Test Plan',
                'plan_type' => 'monthly',
                'duration_months' => 12,
                'minimum_amount' => 1000,
                'interest_rate' => 8.5,
                'bonus_percentage' => 10,
                'premature_closure_penalty' => 2,
                'grace_period_days' => 5,
                'late_fee_amount' => 100,
                'created_by' => 1,
            ]);
            $this->info("✅ Created first plan with ID: {$plan1->id} and plan_code: {$plan1->plan_code}");
            
            // Try to create another with same plan_name
            $plan2 = SavingSchemePlan::create([
                'plan_name' => 'Duplicate Test Plan', // Same name
                'plan_type' => 'monthly',
                'duration_months' => 12,
                'minimum_amount' => 1000,
                'interest_rate' => 8.5,
                'bonus_percentage' => 10,
                'premature_closure_penalty' => 2,
                'grace_period_days' => 5,
                'late_fee_amount' => 100,
                'created_by' => 1,
            ]);
            
            $this->error("❌ Duplicate plan was created - this shouldn't happen!");
            $this->line("   Plan 2 ID: {$plan2->id}, plan_code: {$plan2->plan_code}");
            
        } catch (UniqueConstraintViolationException|QueryException $e) {
            $this->info("✅ Exception caught for duplicate plan");
            $this->line("   Exception: " . get_class($e));
            $this->line("   Message: " . $e->getMessage());
        }

        // Test 3: Test using saveWithUniqueHandling
        $this->info('Test 3: Testing saveWithUniqueHandling method');
        
        try {
            $plan = new SavingSchemePlan([
                'plan_name' => 'Duplicate Test Plan', // Same name as before
                'plan_type' => 'monthly',
                'duration_months' => 12,
                'minimum_amount' => 1000,
                'interest_rate' => 8.5,
                'bonus_percentage' => 10,
                'premature_closure_penalty' => 2,
                'grace_period_days' => 5,
                'late_fee_amount' => 100,
                'created_by' => 1,
            ]);
            
            $result = $plan->saveWithUniqueHandling();
            
            if ($result) {
                $this->info("✅ saveWithUniqueHandling succeeded!");
                $this->line("   Plan ID: {$plan->id}, plan_code: {$plan->plan_code}");
            } else {
                $this->warn("⚠️  saveWithUniqueHandling returned false");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ saveWithUniqueHandling failed: " . $e->getMessage());
        }

        $this->newLine();
        $this->info('🧹 Cleaning up test data...');
        
        // Cleanup
        try {
            User::where('email', $testEmail)->delete();
            SavingSchemePlan::where('plan_name', 'Duplicate Test Plan')->delete();
            $this->info('✅ Test data cleaned up');
        } catch (\Exception $e) {
            $this->warn('Could not clean up test data: ' . $e->getMessage());
        }

        $this->newLine();
        $this->info('🎉 Unique constraint violation test completed!');
        
        return 0;
    }
}