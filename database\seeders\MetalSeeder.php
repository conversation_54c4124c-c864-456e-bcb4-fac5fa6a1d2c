<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Metal;

class MetalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $metals = [
            [
                'name' => 'Gold',
                'purity' => '24K',
                'symbol' => 'AU',
                'current_rate_per_gram' => 7200.00,
                'rate_updated_at' => now(),
                'is_active' => true,
            ],
            [
                'name' => 'Gold',
                'purity' => '22K',
                'symbol' => 'AU',
                'current_rate_per_gram' => 6600.00,
                'rate_updated_at' => now(),
                'is_active' => true,
            ],
            [
                'name' => 'Gold',
                'purity' => '18K',
                'symbol' => 'AU',
                'current_rate_per_gram' => 5400.00,
                'rate_updated_at' => now(),
                'is_active' => true,
            ],
            [
                'name' => 'Gold',
                'purity' => '14K',
                'symbol' => 'AU',
                'current_rate_per_gram' => 4200.00,
                'rate_updated_at' => now(),
                'is_active' => true,
            ],
            [
                'name' => 'Silver',
                'purity' => '999',
                'symbol' => 'AG',
                'current_rate_per_gram' => 92.00,
                'rate_updated_at' => now(),
                'is_active' => true,
            ],
            [
                'name' => 'Silver',
                'purity' => '925',
                'symbol' => 'AG',
                'current_rate_per_gram' => 85.00,
                'rate_updated_at' => now(),
                'is_active' => true,
            ],
            [
                'name' => 'Platinum',
                'purity' => '950',
                'symbol' => 'PT',
                'current_rate_per_gram' => 3200.00,
                'rate_updated_at' => now(),
                'is_active' => true,
            ],
        ];

        foreach ($metals as $metalData) {
            Metal::firstOrCreate(
                [
                    'name' => $metalData['name'],
                    'purity' => $metalData['purity'],
                ],
                $metalData
            );
        }

        $this->command->info('Metal rates seeded successfully!');
    }
}
