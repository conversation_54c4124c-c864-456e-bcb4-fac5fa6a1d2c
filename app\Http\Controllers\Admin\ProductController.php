<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Metal;
use App\Models\Stone;
use App\Models\HsnCode;
use App\Models\ProductImage;
use App\Models\Inventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::with(['category', 'metal', 'images', 'inventory'])
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%")
                  ->orWhere('huid_number', 'like', "%{$search}%")
                  ->orWhere('tag_number', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Metal filter
        if ($request->filled('metal')) {
            $query->where('metal_id', $request->metal);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Stock filter
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'in_stock':
                    $query->whereHas('inventory', function ($q) {
                        $q->where('quantity_available', '>', 5);
                    });
                    break;
                case 'low_stock':
                    $query->whereHas('inventory', function ($q) {
                        $q->whereBetween('quantity_available', [1, 5]);
                    });
                    break;
                case 'out_of_stock':
                    $query->whereHas('inventory', function ($q) {
                        $q->where('quantity_available', '<=', 0);
                    });
                    break;
            }
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('total_amount', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('total_amount', '<=', $request->max_price);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $allowedSorts = ['name', 'sku', 'total_amount', 'created_at', 'view_count'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $products = $query->paginate(20);
        $categories = Category::active()->get();
        $metals = Metal::active()->get();

        // Get statistics
        $stats = [
            'total_products' => Product::count(),
            'active_products' => Product::where('is_active', true)->count(),
            'out_of_stock' => Product::whereHas('inventory', function ($q) {
                $q->where('quantity_available', '<=', 0);
            })->count(),
            'low_stock' => Product::whereHas('inventory', function ($q) {
                $q->whereBetween('quantity_available', [1, 5]);
            })->count(),
        ];

        return view('admin.products.index', compact('products', 'categories', 'metals', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::active()->get();
        $metals = Metal::active()->get();
        $stones = Stone::active()->get();
        $hsnCodes = HsnCode::active()->get();

        return view('admin.products.create', compact('categories', 'metals', 'stones', 'hsnCodes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'metal_id' => 'required|exists:metals,id',
            'hsn_code_id' => 'nullable|exists:hsn_codes,id',
            'description' => 'nullable|string',
            'gross_weight' => 'required|numeric|min:0',
            'net_weight' => 'required|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'stone_pieces' => 'nullable|integer|min:0',
            'wastage_percentage' => 'nullable|numeric|min:0|max:100',
            'making_charge_per_gram' => 'nullable|numeric|min:0',
            'making_charge_fixed' => 'nullable|numeric|min:0',
            'other_charges' => 'nullable|numeric|min:0',
            'huid_number' => 'nullable|string|unique:products,huid_number',
            'barcode' => 'nullable|string|unique:products,barcode',
            'is_hallmarked' => 'boolean',
            'hallmark_number' => 'nullable|string',
            'hallmark_date' => 'nullable|date',
            'is_featured' => 'boolean',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'cost_price' => 'nullable|numeric|min:0',
            'selling_price' => 'nullable|numeric|min:0',
            'mrp' => 'nullable|numeric|min:0',
            'quantity' => 'nullable|integer|min:0',
            'minimum_stock_level' => 'nullable|integer|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Create product
            $product = Product::create($validated);

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $image) {
                    $path = $image->store('products', 'public');

                    ProductImage::create([
                        'product_id' => $product->id,
                        'image_path' => $path,
                        'alt_text' => $product->name,
                        'sort_order' => $index,
                        'is_primary' => $index === 0,
                    ]);
                }
            }

            // Create inventory record
            Inventory::create([
                'product_id' => $product->id,
                'location_id' => auth()->user()->location_id ?? 1,
                'quantity_available' => $validated['quantity'] ?? 0,
                'cost_price' => $validated['cost_price'] ?? 0,
                'selling_price' => $validated['selling_price'] ?? 0,
                'mrp' => $validated['mrp'] ?? 0,
                'minimum_stock_level' => $validated['minimum_stock_level'] ?? 0,
                'last_stock_update' => now(),
            ]);

            DB::commit();

            return redirect()->route('admin.products.index')
                ->with('success', 'Product created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error creating product: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $product->load(['category', 'metal', 'stones', 'images', 'inventory', 'hsnCode']);

        return view('admin.products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $categories = Category::active()->get();
        $metals = Metal::active()->get();
        $stones = Stone::active()->get();
        $hsnCodes = HsnCode::active()->get();

        return view('admin.products.edit', compact('product', 'categories', 'metals', 'stones', 'hsnCodes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'metal_id' => 'required|exists:metals,id',
            'hsn_code_id' => 'nullable|exists:hsn_codes,id',
            'description' => 'nullable|string',
            'gross_weight' => 'required|numeric|min:0',
            'net_weight' => 'required|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'stone_pieces' => 'nullable|integer|min:0',
            'wastage_percentage' => 'nullable|numeric|min:0|max:100',
            'making_charge_per_gram' => 'nullable|numeric|min:0',
            'making_charge_fixed' => 'nullable|numeric|min:0',
            'other_charges' => 'nullable|numeric|min:0',
            'huid_number' => 'nullable|string|unique:products,huid_number,' . $product->id,
            'barcode' => 'nullable|string|unique:products,barcode,' . $product->id,
            'is_hallmarked' => 'boolean',
            'hallmark_number' => 'nullable|string',
            'hallmark_date' => 'nullable|date',
            'is_featured' => 'boolean',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
        ]);

        try {
            DB::beginTransaction();

            $product->update($validated);

            // Handle new image uploads
            if ($request->hasFile('images')) {
                $existingImagesCount = $product->images()->count();

                foreach ($request->file('images') as $index => $image) {
                    $path = $image->store('products', 'public');

                    ProductImage::create([
                        'product_id' => $product->id,
                        'image_path' => $path,
                        'alt_text' => $product->name,
                        'sort_order' => $existingImagesCount + $index,
                        'is_primary' => $existingImagesCount === 0 && $index === 0,
                    ]);
                }
            }

            DB::commit();

            return redirect()->route('admin.products.index')
                ->with('success', 'Product updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error updating product: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        try {
            DB::beginTransaction();

            // Delete product images from storage
            foreach ($product->images as $image) {
                Storage::disk('public')->delete($image->image_path);
                $image->delete();
            }

            // Delete inventory records
            $product->inventory()->delete();

            // Delete the product
            $product->delete();

            DB::commit();

            return redirect()->route('admin.products.index')
                ->with('success', 'Product deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting product: ' . $e->getMessage());
        }
    }

    /**
     * Delete a specific product image
     */
    public function deleteImage(ProductImage $image)
    {
        try {
            Storage::disk('public')->delete($image->image_path);
            $image->delete();

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Generate SKU for product
     */
    public function generateSku(Request $request)
    {
        $category = Category::find($request->category_id);
        $metal = Metal::find($request->metal_id);

        if (!$category || !$metal) {
            return response()->json(['sku' => '']);
        }

        $categoryCode = strtoupper(substr($category->name, 0, 3));
        $metalCode = strtoupper(substr($metal->name, 0, 2));
        $timestamp = now()->format('ymd');
        $random = str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);

        $sku = "{$categoryCode}{$metalCode}{$timestamp}{$random}";

        // Ensure uniqueness
        while (Product::where('sku', $sku)->exists()) {
            $random = str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
            $sku = "{$categoryCode}{$metalCode}{$timestamp}{$random}";
        }

        return response()->json(['sku' => $sku]);
    }

    /**
     * Generate barcode for product
     */
    public function generateBarcode()
    {
        do {
            $barcode = '8' . str_pad(rand(1, 99999999999), 11, '0', STR_PAD_LEFT);
        } while (Product::where('barcode', $barcode)->exists());

        return response()->json(['barcode' => $barcode]);
    }
}
