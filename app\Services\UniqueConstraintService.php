<?php

namespace App\Services;

use Illuminate\Database\QueryException;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class UniqueConstraintService
{
    /**
     * Execute a database operation with automatic unique constraint handling
     */
    public static function executeWithRetry(callable $operation, int $maxRetries = 3): mixed
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $maxRetries) {
            try {
                return $operation();
            } catch (QueryException|UniqueConstraintViolationException $e) {
                $lastException = $e;
                $attempt++;
                
                if (self::isUniqueConstraintViolation($e)) {
                    Log::warning("Unique constraint violation attempt {$attempt}", [
                        'error' => $e->getMessage(),
                        'attempt' => $attempt,
                        'max_retries' => $maxRetries
                    ]);

                    if ($attempt >= $maxRetries) {
                        break;
                    }

                    // Small delay to avoid race conditions
                    usleep(100000); // 100ms
                } else {
                    // If it's not a unique constraint violation, don't retry
                    throw $e;
                }
            }
        }

        // If we've exhausted retries, throw the last exception
        throw $lastException;
    }

    /**
     * Check if the exception is a unique constraint violation
     */
    public static function isUniqueConstraintViolation($exception): bool
    {
        if ($exception instanceof UniqueConstraintViolationException) {
            return true;
        }

        if ($exception instanceof QueryException) {
            $message = $exception->getMessage();
            return str_contains($message, 'Duplicate entry') ||
                   str_contains($message, 'UNIQUE constraint') ||
                   str_contains($message, 'Integrity constraint violation') ||
                   (str_contains($message, '1062')); // MySQL unique constraint error code
        }

        return false;
    }

    /**
     * Find and fix existing duplicate records in a table
     */
    public static function fixDuplicatesInTable(string $table, string $column, callable $generator = null): array
    {
        $results = [];
        
        // Find duplicates
        $duplicates = DB::select("
            SELECT {$column}, COUNT(*) as count
            FROM {$table}
            WHERE {$column} IS NOT NULL
            GROUP BY {$column}
            HAVING COUNT(*) > 1
        ");

        foreach ($duplicates as $duplicate) {
            $duplicateValue = $duplicate->{$column};
            $count = $duplicate->count;
            
            $results[] = [
                'table' => $table,
                'column' => $column,
                'value' => $duplicateValue,
                'count' => $count
            ];

            // If a generator is provided, fix the duplicates
            if ($generator) {
                $records = DB::table($table)
                    ->where($column, $duplicateValue)
                    ->orderBy('id')
                    ->get();

                // Keep the first record, update others
                foreach ($records->skip(1) as $record) {
                    $newValue = $generator($record);
                    DB::table($table)
                        ->where('id', $record->id)
                        ->update([$column => $newValue]);
                    
                    Log::info("Fixed duplicate in {$table}.{$column}", [
                        'record_id' => $record->id,
                        'old_value' => $duplicateValue,
                        'new_value' => $newValue
                    ]);
                }
            }
        }

        return $results;
    }

    /**
     * Generate a unique value for a field by appending a suffix
     */
    public static function generateUniqueValue(string $table, string $column, string $baseValue, int $maxLength = 255): string
    {
        $originalValue = $baseValue;
        $suffix = 1;
        
        // Ensure we don't exceed the column length
        $maxBaseLength = $maxLength - 10; // Reserve space for suffix
        if (strlen($baseValue) > $maxBaseLength) {
            $baseValue = substr($baseValue, 0, $maxBaseLength);
        }

        while (DB::table($table)->where($column, $baseValue)->exists()) {
            $baseValue = $originalValue . '_' . $suffix;
            
            // Ensure we don't exceed the column length
            if (strlen($baseValue) > $maxLength) {
                $baseValue = substr($originalValue, 0, $maxLength - strlen('_' . $suffix)) . '_' . $suffix;
            }
            
            $suffix++;
            
            // Prevent infinite loops
            if ($suffix > 1000) {
                $baseValue = $originalValue . '_' . time() . '_' . rand(100, 999);
                break;
            }
        }

        return $baseValue;
    }

    /**
     * Check if a value would cause a unique constraint violation
     */
    public static function wouldViolateConstraint(string $table, string $column, $value, $excludeId = null): bool
    {
        $query = DB::table($table)->where($column, $value);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * Get suggestions for resolving a unique constraint violation
     */
    public static function getSuggestions(string $table, string $column, $value): array
    {
        $suggestions = [];
        
        // Generate some alternative values
        for ($i = 1; $i <= 5; $i++) {
            $suggestion = $value . '_' . $i;
            if (!self::wouldViolateConstraint($table, $column, $suggestion)) {
                $suggestions[] = $suggestion;
            }
        }
        
        // Add timestamp-based suggestion
        $timestampSuggestion = $value . '_' . time();
        if (!self::wouldViolateConstraint($table, $column, $timestampSuggestion)) {
            $suggestions[] = $timestampSuggestion;
        }
        
        return array_slice($suggestions, 0, 3); // Return max 3 suggestions
    }

    /**
     * Validate unique fields before saving
     */
    public static function validateUniqueFields(array $data, string $table, array $uniqueFields, $excludeId = null): array
    {
        $violations = [];
        
        foreach ($uniqueFields as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                if (self::wouldViolateConstraint($table, $field, $data[$field], $excludeId)) {
                    $violations[$field] = [
                        'value' => $data[$field],
                        'message' => "The {$field} '{$data[$field]}' is already taken.",
                        'suggestions' => self::getSuggestions($table, $field, $data[$field])
                    ];
                }
            }
        }
        
        return $violations;
    }
}