<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\Product;
use App\Models\InvoiceItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SalesController extends Controller
{
    /**
     * Sales dashboard
     */
    public function dashboard(Request $request)
    {
        $dateRange = $request->get('date_range', '30'); // Default 30 days
        $startDate = now()->subDays($dateRange);

        // Sales Statistics
        $stats = [
            'total_sales' => Invoice::where('status', 'paid')
                                  ->where('created_at', '>=', $startDate)
                                  ->sum('total_amount'),
            'total_orders' => Invoice::where('created_at', '>=', $startDate)->count(),
            'average_order_value' => Invoice::where('status', 'paid')
                                          ->where('created_at', '>=', $startDate)
                                          ->avg('total_amount'),
            'pending_orders' => Invoice::where('status', 'pending')->count(),
            'completed_orders' => Invoice::where('status', 'paid')
                                        ->where('created_at', '>=', $startDate)
                                        ->count(),
            'cancelled_orders' => Invoice::where('status', 'cancelled')
                                        ->where('created_at', '>=', $startDate)
                                        ->count(),
        ];

        // Sales by day for chart
        $dailySales = $this->getDailySalesData($dateRange);

        // Top selling products
        $topProducts = $this->getTopSellingProducts($dateRange);

        // Sales by category
        $categoryStats = $this->getSalesByCategory($dateRange);

        // Recent orders
        $recentOrders = Invoice::with(['customer', 'items.product'])
                              ->orderByDesc('created_at')
                              ->limit(10)
                              ->get();

        // Sales performance by staff
        $staffPerformance = $this->getStaffPerformance($dateRange);

        return view('admin.sales.dashboard', compact(
            'stats', 'dailySales', 'topProducts', 'categoryStats', 
            'recentOrders', 'staffPerformance', 'dateRange'
        ));
    }

    /**
     * Sales reports
     */
    public function reports(Request $request)
    {
        $reportType = $request->get('type', 'daily');
        $startDate = $request->get('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        $data = [];

        switch ($reportType) {
            case 'daily':
                $data = $this->getDailyReport($startDate, $endDate);
                break;
            case 'monthly':
                $data = $this->getMonthlyReport($startDate, $endDate);
                break;
            case 'product':
                $data = $this->getProductReport($startDate, $endDate);
                break;
            case 'customer':
                $data = $this->getCustomerReport($startDate, $endDate);
                break;
            case 'category':
                $data = $this->getCategoryReport($startDate, $endDate);
                break;
        }

        return view('admin.sales.reports', compact(
            'data', 'reportType', 'startDate', 'endDate'
        ));
    }

    /**
     * Quick sale interface
     */
    public function quickSale()
    {
        $customers = Customer::where('is_active', true)
                           ->orderBy('first_name')
                           ->get();

        $products = Product::where('is_active', true)
                          ->with(['category', 'metal', 'inventory'])
                          ->orderBy('name')
                          ->get();

        return view('admin.sales.quick-sale', compact('customers', 'products'));
    }

    /**
     * Process quick sale
     */
    public function processQuickSale(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'payment_method' => 'required|string',
            'payment_status' => 'required|in:paid,pending,partial',
            'paid_amount' => 'required_if:payment_status,paid,partial|numeric|min:0',
        ]);

        DB::transaction(function () use ($request) {
            // Create invoice
            $invoice = Invoice::create([
                'invoice_number' => $this->generateInvoiceNumber(),
                'customer_id' => $request->customer_id,
                'location_id' => auth()->user()->location_id,
                'invoice_date' => now(),
                'due_date' => now()->addDays(30),
                'subtotal' => 0,
                'tax_amount' => 0,
                'total_amount' => 0,
                'status' => $request->payment_status,
                'payment_method' => $request->payment_method,
                'notes' => $request->notes,
                'created_by' => auth()->id(),
            ]);

            $subtotal = 0;

            // Add invoice items
            foreach ($request->items as $item) {
                $product = Product::find($item['product_id']);
                $lineTotal = $item['quantity'] * $item['unit_price'];
                
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_amount' => $lineTotal,
                    'tax_rate' => $product->hsnCode->gst_rate ?? 3,
                    'tax_amount' => $lineTotal * (($product->hsnCode->gst_rate ?? 3) / 100),
                ]);

                $subtotal += $lineTotal;

                // Update inventory
                $this->updateInventory($item['product_id'], $item['quantity']);
            }

            // Calculate totals
            $taxAmount = $subtotal * 0.03; // Assuming 3% GST
            $totalAmount = $subtotal + $taxAmount;

            $invoice->update([
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
            ]);

            // Record payment if paid
            if ($request->payment_status === 'paid' || $request->payment_status === 'partial') {
                $invoice->payments()->create([
                    'amount' => $request->paid_amount,
                    'payment_method' => $request->payment_method,
                    'payment_date' => now(),
                    'reference_number' => $request->reference_number,
                    'notes' => 'Quick sale payment',
                    'created_by' => auth()->id(),
                ]);
            }

            // Update customer stats
            $customer = Customer::find($request->customer_id);
            $customer->increment('total_purchase_amount', $totalAmount);
            $customer->update(['last_purchase_date' => now()]);
        });

        return redirect()->route('admin.sales.dashboard')
                        ->with('success', 'Quick sale completed successfully!');
    }

    /**
     * Sales analytics
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', 'month'); // day, week, month, year
        
        $analytics = [
            'sales_trend' => $this->getSalesTrend($period),
            'product_performance' => $this->getProductPerformance($period),
            'customer_segments' => $this->getCustomerSegmentAnalysis(),
            'seasonal_analysis' => $this->getSeasonalAnalysis(),
            'profit_margins' => $this->getProfitMarginAnalysis($period),
        ];

        return view('admin.sales.analytics', compact('analytics', 'period'));
    }

    /**
     * Get daily sales data for charts
     */
    private function getDailySalesData(int $days): array
    {
        $data = Invoice::selectRaw('DATE(created_at) as date, SUM(total_amount) as total, COUNT(*) as orders')
                      ->where('status', 'paid')
                      ->where('created_at', '>=', now()->subDays($days))
                      ->groupBy('date')
                      ->orderBy('date')
                      ->get();

        return [
            'labels' => $data->pluck('date')->map(function ($date) {
                return Carbon::parse($date)->format('M d');
            })->toArray(),
            'sales' => $data->pluck('total')->toArray(),
            'orders' => $data->pluck('orders')->toArray(),
        ];
    }

    /**
     * Get top selling products
     */
    private function getTopSellingProducts(int $days): array
    {
        return InvoiceItem::join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
                         ->join('products', 'invoice_items.product_id', '=', 'products.id')
                         ->where('invoices.status', 'paid')
                         ->where('invoices.created_at', '>=', now()->subDays($days))
                         ->selectRaw('products.name, SUM(invoice_items.quantity) as total_sold, SUM(invoice_items.total_amount) as total_revenue')
                         ->groupBy('products.id', 'products.name')
                         ->orderByDesc('total_sold')
                         ->limit(10)
                         ->get()
                         ->toArray();
    }

    /**
     * Get sales by category
     */
    private function getSalesByCategory(int $days): array
    {
        return InvoiceItem::join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
                         ->join('products', 'invoice_items.product_id', '=', 'products.id')
                         ->join('categories', 'products.category_id', '=', 'categories.id')
                         ->where('invoices.status', 'paid')
                         ->where('invoices.created_at', '>=', now()->subDays($days))
                         ->selectRaw('categories.name, SUM(invoice_items.total_amount) as total_sales, COUNT(*) as items_sold')
                         ->groupBy('categories.id', 'categories.name')
                         ->orderByDesc('total_sales')
                         ->get()
                         ->toArray();
    }

    /**
     * Get staff performance
     */
    private function getStaffPerformance(int $days): array
    {
        return Invoice::join('users', 'invoices.created_by', '=', 'users.id')
                     ->where('invoices.status', 'paid')
                     ->where('invoices.created_at', '>=', now()->subDays($days))
                     ->selectRaw('users.name, COUNT(*) as orders_count, SUM(invoices.total_amount) as total_sales')
                     ->groupBy('users.id', 'users.name')
                     ->orderByDesc('total_sales')
                     ->get()
                     ->toArray();
    }

    /**
     * Generate invoice number
     */
    private function generateInvoiceNumber(): string
    {
        $prefix = 'INV';
        $date = now()->format('Ymd');
        $lastInvoice = Invoice::whereDate('created_at', now())->latest()->first();
        $sequence = $lastInvoice ? (int)substr($lastInvoice->invoice_number, -4) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Update inventory after sale
     */
    private function updateInventory(int $productId, int $quantity): void
    {
        $inventory = \App\Models\InventoryItem::where('product_id', $productId)
                                            ->where('location_id', auth()->user()->location_id)
                                            ->first();

        if ($inventory) {
            $inventory->decrement('quantity', $quantity);
        }
    }

    // Additional private methods for reports and analytics would go here...
    private function getDailyReport($startDate, $endDate) { return []; }
    private function getMonthlyReport($startDate, $endDate) { return []; }
    private function getProductReport($startDate, $endDate) { return []; }
    private function getCustomerReport($startDate, $endDate) { return []; }
    private function getCategoryReport($startDate, $endDate) { return []; }
    private function getSalesTrend($period) { return []; }
    private function getProductPerformance($period) { return []; }
    private function getCustomerSegmentAnalysis() { return []; }
    private function getSeasonalAnalysis() { return []; }
    private function getProfitMarginAnalysis($period) { return []; }
}
