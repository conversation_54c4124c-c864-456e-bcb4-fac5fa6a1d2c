<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class HsnCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'description',
        'cgst_rate',
        'sgst_rate',
        'igst_rate',
        'is_active',
    ];

    protected $casts = [
        'cgst_rate' => 'decimal:2',
        'sgst_rate' => 'decimal:2',
        'igst_rate' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the products that use this HSN code.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'hsn_code_id');
    }

    /**
     * Get the categories that use this HSN code.
     */
    public function categories(): HasMany
    {
        return $this->hasMany(Category::class, 'hsn_code_id');
    }

    /**
     * Scope a query to only include active HSN codes.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by GST rate.
     */
    public function scopeByGstRate($query, $rate)
    {
        return $query->where('cgst_rate', $rate)
                    ->orWhere('sgst_rate', $rate)
                    ->orWhere('igst_rate', $rate);
    }

    /**
     * Get the total GST rate (CGST + SGST or IGST).
     */
    public function getTotalGstRateAttribute()
    {
        // For intrastate: CGST + SGST
        // For interstate: IGST
        return $this->igst_rate ?: ($this->cgst_rate + $this->sgst_rate);
    }

    /**
     * Get formatted HSN code with description.
     */
    public function getFormattedCodeAttribute()
    {
        return $this->code . ' - ' . $this->description;
    }
}
