<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use App\Traits\GeneratesUniqueFields;
use App\Traits\HandlesUniqueConstraints;

class SavingSchemePlan extends Model
{
    use HasFactory, SoftDeletes, GeneratesUniqueFields, HandlesUniqueConstraints;

    protected $fillable = [
        'plan_name',
        'plan_code',
        'description',
        'plan_type',
        'duration_months',
        'minimum_amount',
        'maximum_amount',
        'interest_rate',
        'bonus_percentage',
        'allow_partial_withdrawal',
        'allow_premature_closure',
        'premature_closure_penalty',
        'grace_period_days',
        'late_fee_amount',
        'is_active',
        'terms_conditions',
        'created_by',
    ];

    protected $casts = [
        'minimum_amount' => 'decimal:2',
        'maximum_amount' => 'decimal:2',
        'interest_rate' => 'decimal:2',
        'bonus_percentage' => 'decimal:2',
        'premature_closure_penalty' => 'decimal:2',
        'late_fee_amount' => 'decimal:2',
        'allow_partial_withdrawal' => 'boolean',
        'allow_premature_closure' => 'boolean',
        'is_active' => 'boolean',
        'terms_conditions' => 'array',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($plan) {
            if (!$plan->plan_code) {
                $plan->plan_code = static::generatePlanCode($plan);
            }
            if (!$plan->created_by) {
                $plan->created_by = auth()->id() ?: 1; // Default to user ID 1 if no auth
            }
        });
    }

    /**
     * Relationships
     */
    public function savingSchemes(): HasMany
    {
        return $this->hasMany(SavingScheme::class, 'plan_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('plan_type', $type);
    }

    /**
     * Accessors
     */
    public function getFormattedMinimumAmountAttribute(): string
    {
        return '₹' . number_format($this->minimum_amount, 2);
    }

    public function getFormattedMaximumAmountAttribute(): string
    {
        return $this->maximum_amount ? '₹' . number_format($this->maximum_amount, 2) : 'No Limit';
    }

    public function getFormattedInterestRateAttribute(): string
    {
        return $this->interest_rate . '% per annum';
    }

    public function getFormattedBonusPercentageAttribute(): string
    {
        return $this->bonus_percentage . '% on maturity';
    }

    public function getPlanTypeDisplayAttribute(): string
    {
        $types = [
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
            'yearly' => 'Yearly',
            'custom' => 'Custom',
        ];

        return $types[$this->plan_type] ?? ucfirst($this->plan_type);
    }

    public function getDurationDisplayAttribute(): string
    {
        if ($this->duration_months < 12) {
            return $this->duration_months . ' months';
        } else {
            $years = floor($this->duration_months / 12);
            $months = $this->duration_months % 12;

            $display = $years . ' year' . ($years > 1 ? 's' : '');
            if ($months > 0) {
                $display .= ' ' . $months . ' month' . ($months > 1 ? 's' : '');
            }

            return $display;
        }
    }

    /**
     * Business Logic Methods
     */
    public static function generatePlanCode($plan): string
    {
        $maxAttempts = 10;
        $attempt = 0;

        while ($attempt < $maxAttempts) {
            $typeCode = strtoupper(substr($plan->plan_type, 0, 3));
            $durationCode = str_pad($plan->duration_months, 2, '0', STR_PAD_LEFT);

            // Use microseconds for better uniqueness
            $microtime = microtime(true);
            $timestamp = date('ymdHis', $microtime);
            $microseconds = sprintf('%03d', ($microtime - floor($microtime)) * 1000);

            // Add random component
            $random = rand(100, 999);

            $code = "SP{$typeCode}{$durationCode}{$timestamp}{$microseconds}{$random}";

            // Check if this code already exists
            if (!static::where('plan_code', $code)->exists()) {
                return $code;
            }

            $attempt++;
            // Small delay to avoid rapid collisions
            usleep(1000); // 1ms
        }

        // Fallback: use UUID-based approach if all attempts failed
        $uuid = str_replace('-', '', \Illuminate\Support\Str::uuid());
        $typeCode = strtoupper(substr($plan->plan_type, 0, 3));
        $durationCode = str_pad($plan->duration_months, 2, '0', STR_PAD_LEFT);

        return "SP{$typeCode}{$durationCode}" . substr($uuid, 0, 12);
    }

    public function calculateMaturityAmount($principalAmount): array
    {
        $interestAmount = ($principalAmount * $this->interest_rate * $this->duration_months) / (12 * 100);
        $bonusAmount = ($principalAmount * $this->bonus_percentage) / 100;
        $totalAmount = $principalAmount + $interestAmount + $bonusAmount;

        return [
            'principal_amount' => round($principalAmount, 2),
            'interest_amount' => round($interestAmount, 2),
            'bonus_amount' => round($bonusAmount, 2),
            'total_amount' => round($totalAmount, 2),
        ];
    }

    public function getInstallmentFrequency(): int
    {
        return match($this->plan_type) {
            'monthly' => 1,
            'quarterly' => 3,
            'yearly' => 12,
            default => 1,
        };
    }

    public function getTotalInstallments(): int
    {
        return $this->duration_months / $this->getInstallmentFrequency();
    }

    public function activate(): self
    {
        $this->update(['is_active' => true]);
        return $this;
    }

    public function deactivate(): self
    {
        $this->update(['is_active' => false]);
        return $this;
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'plan_name' => $this->plan_name,
            'plan_code' => $this->plan_code,
            'plan_type' => $this->plan_type_display,
            'duration' => $this->duration_display,
            'minimum_amount' => $this->formatted_minimum_amount,
            'maximum_amount' => $this->formatted_maximum_amount,
            'interest_rate' => $this->formatted_interest_rate,
            'bonus_percentage' => $this->formatted_bonus_percentage,
            'total_installments' => $this->getTotalInstallments(),
            'installment_frequency' => $this->getInstallmentFrequency(),
            'is_active' => $this->is_active,
            'active_schemes_count' => $this->savingSchemes()->where('status', 'active')->count(),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
