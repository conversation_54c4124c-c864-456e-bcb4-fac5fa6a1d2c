<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'image_path' => $this->image_path,
            'sort_order' => $this->sort_order,
            'is_active' => $this->is_active,
            
            // SEO
            'seo_description' => $this->seo_description,
            'seo_keywords' => $this->seo_keywords,
            
            // Relationships
            'parent' => new CategoryResource($this->whenLoaded('parent')),
            'children' => CategoryResource::collection($this->whenLoaded('children')),
            'hsn_code' => new HsnCodeResource($this->whenLoaded('hsnCode')),
            
            // Counts
            'products_count' => $this->when(isset($this->products_count), $this->products_count),
            'children_count' => $this->when(isset($this->children_count), $this->children_count),
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
