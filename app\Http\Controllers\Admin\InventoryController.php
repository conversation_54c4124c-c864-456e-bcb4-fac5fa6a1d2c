<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\Location;
use App\Models\StockMovement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InventoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Inventory::with(['product.category', 'product.metal', 'location'])
            ->join('products', 'inventory.product_id', '=', 'products.id')
            ->select('inventory.*', 'products.name as product_name', 'products.sku')
            ->orderBy('inventory.updated_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('products.name', 'like', "%{$search}%")
                  ->orWhere('products.sku', 'like', "%{$search}%")
                  ->orWhere('products.barcode', 'like', "%{$search}%")
                  ->orWhere('products.huid_number', 'like', "%{$search}%");
            });
        }

        // Location filter
        if ($request->filled('location')) {
            $query->where('inventory.location_id', $request->location);
        }

        // Stock status filter
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low_stock':
                    $query->whereRaw('inventory.quantity_available <= inventory.minimum_stock_level');
                    break;
                case 'out_of_stock':
                    $query->where('inventory.quantity_available', 0);
                    break;
                case 'in_stock':
                    $query->where('inventory.quantity_available', '>', 0);
                    break;
            }
        }

        // Category filter
        if ($request->filled('category')) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('category_id', $request->category);
            });
        }

        $inventories = $query->paginate(20);
        $locations = Location::active()->get();
        $categories = \App\Models\Category::active()->get();

        // Get summary statistics
        $stats = [
            'total_products' => Inventory::count(),
            'low_stock_items' => Inventory::whereRaw('quantity_available <= minimum_stock_level')->count(),
            'out_of_stock_items' => Inventory::where('quantity_available', 0)->count(),
            'total_value' => Inventory::sum(DB::raw('quantity_available * cost_price')),
        ];

        return view('admin.inventory.index', compact('inventories', 'locations', 'categories', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $products = Product::whereDoesntHave('inventory')->get();
        $locations = Location::active()->get();

        return view('admin.inventory.create', compact('products', 'locations'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id|unique:inventories,product_id',
            'location_id' => 'required|exists:locations,id',
            'quantity_available' => 'required|integer|min:0',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'mrp' => 'nullable|numeric|min:0',
            'minimum_stock_level' => 'required|integer|min:0',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $validated['last_stock_update'] = now();
            $inventory = Inventory::create($validated);

            // Create initial stock movement record
            StockMovement::create([
                'inventory_id' => $inventory->id,
                'movement_type' => 'initial_stock',
                'quantity' => $validated['quantity_available'],
                'reference_type' => 'manual',
                'reference_id' => null,
                'notes' => 'Initial stock entry',
                'created_by' => auth()->id(),
            ]);

            DB::commit();

            return redirect()->route('admin.inventory.index')
                ->with('success', 'Inventory item created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Error creating inventory item: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Inventory $inventory)
    {
        $inventory->load(['product.category', 'product.metal', 'location', 'stockMovements.createdBy']);

        // Get stock movement history
        $movements = $inventory->stockMovements()
            ->with('createdBy')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.inventory.show', compact('inventory', 'movements'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Inventory $inventory)
    {
        $locations = Location::active()->get();
        return view('admin.inventory.edit', compact('inventory', 'locations'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Inventory $inventory)
    {
        $validated = $request->validate([
            'location_id' => 'required|exists:locations,id',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'mrp' => 'nullable|numeric|min:0',
            'minimum_stock_level' => 'required|integer|min:0',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $validated['last_stock_update'] = now();
            $inventory->update($validated);

            return redirect()->route('admin.inventory.show', $inventory)
                ->with('success', 'Inventory updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Error updating inventory: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Inventory $inventory)
    {
        try {
            DB::beginTransaction();

            // Delete stock movements
            $inventory->stockMovements()->delete();

            // Delete inventory
            $inventory->delete();

            DB::commit();

            return redirect()->route('admin.inventory.index')
                ->with('success', 'Inventory item deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting inventory: ' . $e->getMessage());
        }
    }

    /**
     * Adjust stock quantity
     */
    public function adjustStock(Request $request, Inventory $inventory)
    {
        $validated = $request->validate([
            'adjustment_type' => 'required|in:increase,decrease',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $oldQuantity = $inventory->quantity_available;

            if ($validated['adjustment_type'] === 'increase') {
                $newQuantity = $oldQuantity + $validated['quantity'];
                $movementType = 'stock_in';
            } else {
                $newQuantity = max(0, $oldQuantity - $validated['quantity']);
                $movementType = 'stock_out';
            }

            // Update inventory
            $inventory->update([
                'quantity_available' => $newQuantity,
                'last_stock_update' => now(),
            ]);

            // Create stock movement record
            StockMovement::create([
                'inventory_id' => $inventory->id,
                'movement_type' => $movementType,
                'quantity' => $validated['quantity'],
                'reference_type' => 'adjustment',
                'reference_id' => null,
                'notes' => $validated['reason'] . ($validated['notes'] ? ' - ' . $validated['notes'] : ''),
                'created_by' => auth()->id(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Stock adjusted successfully.',
                'new_quantity' => $newQuantity
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error adjusting stock: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get low stock alerts
     */
    public function lowStockAlerts()
    {
        $lowStockItems = Inventory::with(['product.category', 'location'])
            ->whereRaw('quantity_available <= minimum_stock_level')
            ->orderBy('quantity_available', 'asc')
            ->get();

        return view('admin.inventory.low-stock', compact('lowStockItems'));
    }

    /**
     * Stock movement history
     */
    public function stockMovements(Request $request)
    {
        $query = StockMovement::with(['inventory.product', 'createdBy'])
            ->orderBy('created_at', 'desc');

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Movement type filter
        if ($request->filled('movement_type')) {
            $query->where('movement_type', $request->movement_type);
        }

        $movements = $query->paginate(20);

        return view('admin.inventory.movements', compact('movements'));
    }

    /**
     * Export inventory data
     */
    public function export(Request $request)
    {
        // This would implement CSV/Excel export functionality
        return response()->json(['message' => 'Export functionality to be implemented']);
    }

    /**
     * Bulk stock update
     */
    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'updates' => 'required|array',
            'updates.*.inventory_id' => 'required|exists:inventories,id',
            'updates.*.quantity' => 'required|integer|min:0',
            'updates.*.cost_price' => 'nullable|numeric|min:0',
            'updates.*.selling_price' => 'nullable|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            foreach ($validated['updates'] as $update) {
                $inventory = Inventory::find($update['inventory_id']);
                $oldQuantity = $inventory->quantity_available;

                $updateData = [
                    'quantity_available' => $update['quantity'],
                    'last_stock_update' => now(),
                ];

                if (isset($update['cost_price'])) {
                    $updateData['cost_price'] = $update['cost_price'];
                }
                if (isset($update['selling_price'])) {
                    $updateData['selling_price'] = $update['selling_price'];
                }

                $inventory->update($updateData);

                // Create stock movement if quantity changed
                if ($oldQuantity != $update['quantity']) {
                    $movementType = $update['quantity'] > $oldQuantity ? 'stock_in' : 'stock_out';
                    $quantity = abs($update['quantity'] - $oldQuantity);

                    StockMovement::create([
                        'inventory_id' => $inventory->id,
                        'movement_type' => $movementType,
                        'quantity' => $quantity,
                        'reference_type' => 'bulk_update',
                        'reference_id' => null,
                        'notes' => 'Bulk stock update',
                        'created_by' => auth()->id(),
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Bulk update completed successfully.'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error in bulk update: ' . $e->getMessage()
            ]);
        }
    }
}
