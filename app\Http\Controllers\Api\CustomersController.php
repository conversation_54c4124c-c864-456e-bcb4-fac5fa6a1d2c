<?php

namespace App\Http\Controllers\Api;

use App\Models\Customer;
use App\Http\Resources\CustomerResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class CustomersController extends BaseApiController
{
    /**
     * Display a listing of customers
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Customer::with(['invoices', 'payments']);

            // Apply filters
            $allowedFilters = ['first_name', 'last_name', 'email', 'phone', 'customer_type'];
            $query = $this->applyFilters($query, $request, $allowedFilters);

            // Customer segment filter
            if ($request->has('segment')) {
                $segment = $request->get('segment');
                switch ($segment) {
                    case 'vip':
                        $query->where('total_purchase_amount', '>=', 100000);
                        break;
                    case 'loyal':
                        $query->whereBetween('total_purchase_amount', [50000, 99999]);
                        break;
                    case 'regular':
                        $query->whereBetween('total_purchase_amount', [20000, 49999]);
                        break;
                    case 'new':
                        $query->where('created_at', '>=', now()->subDays(30));
                        break;
                    case 'at_risk':
                        $query->whereHas('invoices', function ($q) {
                            $q->where('created_at', '<', now()->subDays(180));
                        })->whereDoesntHave('invoices', function ($q) {
                            $q->where('created_at', '>=', now()->subDays(180));
                        });
                        break;
                }
            }

            // Date range filter
            if ($request->has('created_from')) {
                $query->where('created_at', '>=', $request->created_from);
            }
            if ($request->has('created_to')) {
                $query->where('created_at', '<=', $request->created_to);
            }

            // Apply sorting
            $allowedSorts = ['first_name', 'last_name', 'email', 'total_purchase_amount', 'created_at'];
            $query = $this->applySorting($query, $request, $allowedSorts);

            $paginationParams = $this->getPaginationParams($request);
            $customers = $query->paginate($paginationParams['per_page']);

            $this->logApiActivity($request, 'index', 'customers');

            return $this->sendPaginatedResponse($customers, 'Customers retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Display the specified customer
     */
    public function show(Request $request, $id): JsonResponse
    {
        try {
            $customer = Customer::with([
                'invoices' => function ($query) {
                    $query->latest()->limit(10);
                },
                'payments' => function ($query) {
                    $query->latest()->limit(10);
                }
            ])->find($id);

            if (!$customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            // Add customer analytics
            $customerData = $customer->toArray();
            $customerData['analytics'] = [
                'total_orders' => $customer->invoices()->count(),
                'total_spent' => $customer->total_purchase_amount,
                'average_order_value' => $customer->invoices()->avg('total_amount') ?? 0,
                'last_purchase_date' => $customer->invoices()->latest()->first()?->created_at,
                'customer_since' => $customer->created_at->diffForHumans(),
                'loyalty_points' => $customer->loyalty_points ?? 0,
                'preferred_categories' => $this->getCustomerPreferredCategories($customer),
            ];

            $this->logApiActivity($request, 'show', 'customers', $id);

            return $this->sendResponse($customerData, 'Customer retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Store a newly created customer
     */
    public function store(Request $request): JsonResponse
    {
        try {
            if (!$this->checkPermission('create_customers')) {
                return $this->sendForbiddenResponse('Insufficient permissions to create customers');
            }

            $validatedData = $this->validateRequest($request, [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:customers,email',
                'phone' => 'required|string|max:20|unique:customers,phone',
                'date_of_birth' => 'nullable|date|before:today',
                'anniversary_date' => 'nullable|date',
                'address' => 'nullable|string',
                'city' => 'nullable|string|max:100',
                'state' => 'nullable|string|max:100',
                'postal_code' => 'nullable|string|max:20',
                'country' => 'nullable|string|max:100',
                'customer_type' => 'required|in:individual,business',
                'company_name' => 'nullable|string|max:255',
                'gst_number' => 'nullable|string|max:50',
                'notes' => 'nullable|string',
            ]);

            // Generate customer code
            $validatedData['customer_code'] = $this->generateCustomerCode();
            $validatedData['created_by'] = $this->getAuthenticatedUser()->id;

            $customer = Customer::create($validatedData);

            $this->logApiActivity($request, 'store', 'customers', $customer->id);

            return $this->sendResponse(
                new CustomerResource($customer),
                'Customer created successfully',
                201
            );
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Update the specified customer
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            if (!$this->checkPermission('update_customers')) {
                return $this->sendForbiddenResponse('Insufficient permissions to update customers');
            }

            $customer = Customer::find($id);
            if (!$customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            $validatedData = $this->validateRequest($request, [
                'first_name' => 'sometimes|required|string|max:255',
                'last_name' => 'sometimes|required|string|max:255',
                'email' => [
                    'nullable',
                    'email',
                    Rule::unique('customers', 'email')->ignore($id)
                ],
                'phone' => [
                    'sometimes',
                    'required',
                    'string',
                    'max:20',
                    Rule::unique('customers', 'phone')->ignore($id)
                ],
                'date_of_birth' => 'nullable|date|before:today',
                'anniversary_date' => 'nullable|date',
                'address' => 'nullable|string',
                'city' => 'nullable|string|max:100',
                'state' => 'nullable|string|max:100',
                'postal_code' => 'nullable|string|max:20',
                'country' => 'nullable|string|max:100',
                'customer_type' => 'sometimes|required|in:individual,business',
                'company_name' => 'nullable|string|max:255',
                'gst_number' => 'nullable|string|max:50',
                'notes' => 'nullable|string',
            ]);

            $customer->update($validatedData);

            $this->logApiActivity($request, 'update', 'customers', $id);

            return $this->sendResponse(
                new CustomerResource($customer),
                'Customer updated successfully'
            );
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Remove the specified customer
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            if (!$this->checkPermission('delete_customers')) {
                return $this->sendForbiddenResponse('Insufficient permissions to delete customers');
            }

            $customer = Customer::find($id);
            if (!$customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            // Check if customer has any invoices
            if ($customer->invoices()->exists()) {
                return $this->sendError('Cannot delete customer with existing invoices', [], 409);
            }

            $customer->delete();

            $this->logApiActivity($request, 'destroy', 'customers', $id);

            return $this->sendResponse(null, 'Customer deleted successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Search customers
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validatedData = $this->validateRequest($request, [
                'query' => 'required|string|min:2',
            ]);

            $searchTerm = $validatedData['query'];
            $query = Customer::where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'like', "%{$searchTerm}%")
                  ->orWhere('last_name', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%")
                  ->orWhere('phone', 'like', "%{$searchTerm}%")
                  ->orWhere('customer_code', 'like', "%{$searchTerm}%");
            });

            $paginationParams = $this->getPaginationParams($request);
            $customers = $query->paginate($paginationParams['per_page']);

            $this->logApiActivity($request, 'search', 'customers');

            return $this->sendPaginatedResponse($customers, 'Search results retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Get customer purchase history
     */
    public function purchaseHistory(Request $request, $id): JsonResponse
    {
        try {
            $customer = Customer::find($id);
            if (!$customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            $query = $customer->invoices()->with(['items.product', 'payments']);

            // Date range filter
            if ($request->has('from_date')) {
                $query->where('invoice_date', '>=', $request->from_date);
            }
            if ($request->has('to_date')) {
                $query->where('invoice_date', '<=', $request->to_date);
            }

            $paginationParams = $this->getPaginationParams($request);
            $invoices = $query->orderByDesc('invoice_date')->paginate($paginationParams['per_page']);

            $this->logApiActivity($request, 'purchase_history', 'customers', $id);

            return $this->sendPaginatedResponse($invoices, 'Purchase history retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    /**
     * Get customer analytics
     */
    public function analytics(Request $request, $id): JsonResponse
    {
        try {
            $customer = Customer::with(['invoices', 'payments'])->find($id);
            if (!$customer) {
                return $this->sendNotFoundResponse('Customer not found');
            }

            $analytics = [
                'customer_info' => [
                    'id' => $customer->id,
                    'name' => $customer->first_name . ' ' . $customer->last_name,
                    'customer_code' => $customer->customer_code,
                    'customer_since' => $customer->created_at->format('Y-m-d'),
                ],
                'purchase_summary' => [
                    'total_orders' => $customer->invoices()->count(),
                    'total_spent' => $customer->total_purchase_amount,
                    'average_order_value' => $customer->invoices()->avg('total_amount') ?? 0,
                    'last_purchase_date' => $customer->invoices()->latest()->first()?->invoice_date,
                ],
                'customer_segment' => $this->getCustomerSegment($customer),
                'preferred_categories' => $this->getCustomerPreferredCategories($customer),
                'payment_behavior' => $this->getCustomerPaymentBehavior($customer),
                'loyalty_info' => [
                    'points_balance' => $customer->loyalty_points ?? 0,
                    'tier' => $this->getCustomerTier($customer),
                    'next_tier_threshold' => $this->getNextTierThreshold($customer),
                ],
            ];

            $this->logApiActivity($request, 'analytics', 'customers', $id);

            return $this->sendResponse($analytics, 'Customer analytics retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleApiException($e);
        }
    }

    // Helper Methods
    private function generateCustomerCode(): string
    {
        $prefix = 'CUST';
        $lastCustomer = Customer::latest()->first();
        $number = $lastCustomer ? (int)substr($lastCustomer->customer_code, 4) + 1 : 1;
        
        return $prefix . str_pad($number, 6, '0', STR_PAD_LEFT);
    }

    private function getCustomerPreferredCategories(Customer $customer): array
    {
        return $customer->invoices()
                       ->join('invoice_items', 'invoices.id', '=', 'invoice_items.invoice_id')
                       ->join('products', 'invoice_items.product_id', '=', 'products.id')
                       ->join('categories', 'products.category_id', '=', 'categories.id')
                       ->selectRaw('categories.name, COUNT(*) as purchase_count, SUM(invoice_items.total_amount) as total_spent')
                       ->groupBy('categories.id', 'categories.name')
                       ->orderByDesc('total_spent')
                       ->limit(5)
                       ->get()
                       ->toArray();
    }

    private function getCustomerSegment(Customer $customer): string
    {
        $totalSpent = $customer->total_purchase_amount;
        
        if ($totalSpent >= 100000) return 'VIP';
        if ($totalSpent >= 50000) return 'Loyal';
        if ($totalSpent >= 20000) return 'Regular';
        if ($customer->created_at >= now()->subDays(30)) return 'New';
        
        return 'Occasional';
    }

    private function getCustomerPaymentBehavior(Customer $customer): array
    {
        $payments = $customer->payments();
        
        return [
            'preferred_payment_method' => $payments->selectRaw('payment_method, COUNT(*) as count')
                                                 ->groupBy('payment_method')
                                                 ->orderByDesc('count')
                                                 ->first()?->payment_method ?? 'cash',
            'average_payment_time' => 0, // Days between invoice and payment
            'payment_reliability' => 'Good', // Based on payment history
        ];
    }

    private function getCustomerTier(Customer $customer): string
    {
        $totalSpent = $customer->total_purchase_amount;
        
        if ($totalSpent >= 500000) return 'Platinum';
        if ($totalSpent >= 200000) return 'Gold';
        if ($totalSpent >= 50000) return 'Silver';
        
        return 'Bronze';
    }

    private function getNextTierThreshold(Customer $customer): int
    {
        $totalSpent = $customer->total_purchase_amount;
        
        if ($totalSpent < 50000) return 50000;
        if ($totalSpent < 200000) return 200000;
        if ($totalSpent < 500000) return 500000;
        
        return 1000000; // Next milestone
    }
}
