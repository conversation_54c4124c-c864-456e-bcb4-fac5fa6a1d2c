<?php

namespace App\Services;

use App\Models\PrinterConfiguration;
use App\Models\PrintJob;
use App\Models\PrintTemplate;
use App\Models\Invoice;
use App\Models\Estimate;
use App\Models\InventoryItem;
use Mike<PERSON>\Escpos\Printer;
use <PERSON>42\Escpos\PrintConnectors\FilePrintConnector;
use Mike42\Escpos\PrintConnectors\NetworkPrintConnector;
use Mike42\Escpos\PrintConnectors\WindowsPrintConnector;
use Mike42\Escpos\EscposImage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Picqer\Barcode\BarcodeGeneratorPNG;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ThermalPrintingService
{
    protected $barcodeGenerator;

    public function __construct()
    {
        $this->barcodeGenerator = new BarcodeGeneratorPNG();
    }

    /**
     * Print invoice
     */
    public function printInvoice($invoiceId, $printerId = null, $copies = 1)
    {
        $invoice = Invoice::with([
            'customer', 'items.product.category', 'items.product.metal',
            'location', 'createdBy'
        ])->findOrFail($invoiceId);

        $printer = $this->getPrinter($printerId, 'invoice');
        $template = $this->getTemplate('invoice');

        return $this->createPrintJob([
            'printer_configuration_id' => $printer->id,
            'print_type' => 'invoice',
            'related_model_type' => Invoice::class,
            'related_model_id' => $invoice->id,
            'related_reference_number' => $invoice->invoice_number,
            'copies' => $copies,
            'print_data' => $this->prepareInvoiceData($invoice),
            'template_name' => $template->name,
        ]);
    }

    /**
     * Print receipt
     */
    public function printReceipt($invoiceId, $printerId = null, $copies = 1)
    {
        $invoice = Invoice::with(['customer', 'items.product'])->findOrFail($invoiceId);
        $printer = $this->getPrinter($printerId, 'receipt');
        $template = $this->getTemplate('receipt');

        return $this->createPrintJob([
            'printer_configuration_id' => $printer->id,
            'print_type' => 'receipt',
            'related_model_type' => Invoice::class,
            'related_model_id' => $invoice->id,
            'related_reference_number' => $invoice->invoice_number,
            'copies' => $copies,
            'print_data' => $this->prepareReceiptData($invoice),
            'template_name' => $template->name,
        ]);
    }

    /**
     * Print estimate
     */
    public function printEstimate($estimateId, $printerId = null, $copies = 1)
    {
        $estimate = Estimate::with(['customer', 'items.product'])->findOrFail($estimateId);
        $printer = $this->getPrinter($printerId, 'estimate');
        $template = $this->getTemplate('estimate');

        return $this->createPrintJob([
            'printer_configuration_id' => $printer->id,
            'print_type' => 'estimate',
            'related_model_type' => Estimate::class,
            'related_model_id' => $estimate->id,
            'related_reference_number' => $estimate->estimate_number,
            'copies' => $copies,
            'print_data' => $this->prepareEstimateData($estimate),
            'template_name' => $template->name,
        ]);
    }

    /**
     * Print inventory tag
     */
    public function printInventoryTag($itemId, $printerId = null, $copies = 1)
    {
        $item = InventoryItem::with(['product.category', 'product.metal', 'location'])
                            ->findOrFail($itemId);
        
        $printer = $this->getPrinter($printerId, 'tag');
        $template = $this->getTemplate('tag');

        return $this->createPrintJob([
            'printer_configuration_id' => $printer->id,
            'print_type' => 'tag',
            'related_model_type' => InventoryItem::class,
            'related_model_id' => $item->id,
            'related_reference_number' => $item->unique_tag,
            'copies' => $copies,
            'print_data' => $this->prepareInventoryTagData($item),
            'template_name' => $template->name,
        ]);
    }

    /**
     * Print barcode label
     */
    public function printBarcodeLabel($itemId, $printerId = null, $copies = 1)
    {
        $item = InventoryItem::with(['product'])->findOrFail($itemId);
        $printer = $this->getPrinter($printerId, 'label');
        $template = $this->getTemplate('barcode');

        return $this->createPrintJob([
            'printer_configuration_id' => $printer->id,
            'print_type' => 'barcode',
            'related_model_type' => InventoryItem::class,
            'related_model_id' => $item->id,
            'related_reference_number' => $item->barcode,
            'copies' => $copies,
            'print_data' => $this->prepareBarcodeData($item),
            'template_name' => $template->name,
        ]);
    }

    /**
     * Print QR code label
     */
    public function printQRCodeLabel($itemId, $printerId = null, $copies = 1)
    {
        $item = InventoryItem::with(['product'])->findOrFail($itemId);
        $printer = $this->getPrinter($printerId, 'label');
        $template = $this->getTemplate('qr_code');

        return $this->createPrintJob([
            'printer_configuration_id' => $printer->id,
            'print_type' => 'qr_code',
            'related_model_type' => InventoryItem::class,
            'related_model_id' => $item->id,
            'related_reference_number' => $item->unique_tag,
            'copies' => $copies,
            'print_data' => $this->prepareQRCodeData($item),
            'template_name' => $template->name,
        ]);
    }

    /**
     * Create print job
     */
    public function createPrintJob($jobData)
    {
        $jobData['job_id'] = $this->generateJobId();
        $jobData['user_id'] = auth()->id();
        $jobData['queued_at'] = now();
        $jobData['status'] = 'pending';

        $printJob = PrintJob::create($jobData);

        // Process the print job
        $this->processPrintJob($printJob);

        return $printJob;
    }

    /**
     * Process print job
     */
    public function processPrintJob(PrintJob $printJob)
    {
        try {
            $printJob->update([
                'status' => 'processing',
                'started_at' => now(),
            ]);

            $startTime = microtime(true);

            // Generate print content
            $content = $this->generatePrintContent($printJob);
            $printJob->update(['raw_content' => $content]);

            // Send to printer
            $this->sendToPrinter($printJob, $content);

            $endTime = microtime(true);
            $processingTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

            $printJob->update([
                'status' => 'completed',
                'completed_at' => now(),
                'processing_time_ms' => round($processingTime),
            ]);

            // Update printer statistics
            $this->updatePrinterStatistics($printJob->printer_configuration_id, true);

            return true;

        } catch (\Exception $e) {
            $printJob->update([
                'status' => 'failed',
                'failed_at' => now(),
                'error_message' => $e->getMessage(),
                'retry_count' => $printJob->retry_count + 1,
            ]);

            // Update printer statistics
            $this->updatePrinterStatistics($printJob->printer_configuration_id, false);

            // Retry if within retry limit
            if ($printJob->retry_count < $printJob->max_retries) {
                $this->retryPrintJob($printJob);
            }

            throw $e;
        }
    }

    /**
     * Generate print content based on template
     */
    public function generatePrintContent(PrintJob $printJob)
    {
        $template = PrintTemplate::where('template_type', $printJob->print_type)
                                ->where('is_active', true)
                                ->first();

        if (!$template) {
            return $this->generateDefaultContent($printJob);
        }

        $data = json_decode($printJob->print_data, true);
        $content = $template->template_content;

        // Replace placeholders with actual data
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $value = $this->formatArrayForPrint($value);
            }
            $content = str_replace("{{" . $key . "}}", $value, $content);
        }

        return $content;
    }

    /**
     * Send content to printer
     */
    public function sendToPrinter(PrintJob $printJob, $content)
    {
        $printerConfig = $printJob->printerConfiguration;
        $connector = $this->createPrintConnector($printerConfig);
        
        if (!$connector) {
            throw new \Exception("Could not create print connector for printer: " . $printerConfig->name);
        }

        $printer = new Printer($connector);

        try {
            // Set print density if supported
            if ($printerConfig->print_density) {
                $printer->setPrintLeftMargin($printerConfig->print_density);
            }

            // Process content line by line
            $lines = explode("\n", $content);
            
            foreach ($lines as $line) {
                $this->processContentLine($printer, $line, $printerConfig);
            }

            // Auto cut if enabled
            if ($printerConfig->auto_cut) {
                $printer->feed($printerConfig->cut_lines);
                $printer->cut();
            }

            // Open cash drawer if enabled
            if ($printerConfig->supports_drawer && 
                ($printJob->print_type === 'invoice' || $printJob->print_type === 'receipt')) {
                $printer->pulse();
            }

        } finally {
            $printer->close();
        }
    }

    /**
     * Process individual content line
     */
    private function processContentLine($printer, $line, $printerConfig)
    {
        $line = trim($line);
        
        if (empty($line)) {
            $printer->feed();
            return;
        }

        // Handle special commands
        if (strpos($line, '[') === 0 && strpos($line, ']') !== false) {
            $this->processSpecialCommand($printer, $line, $printerConfig);
            return;
        }

        // Handle alignment
        if (strpos($line, '|CENTER|') !== false) {
            $printer->setJustification(Printer::JUSTIFY_CENTER);
            $line = str_replace('|CENTER|', '', $line);
        } elseif (strpos($line, '|RIGHT|') !== false) {
            $printer->setJustification(Printer::JUSTIFY_RIGHT);
            $line = str_replace('|RIGHT|', '', $line);
        } else {
            $printer->setJustification(Printer::JUSTIFY_LEFT);
        }

        // Handle text formatting
        if (strpos($line, '|BOLD|') !== false) {
            $printer->setEmphasis(true);
            $line = str_replace('|BOLD|', '', $line);
        }

        if (strpos($line, '|LARGE|') !== false) {
            $printer->setTextSize(2, 2);
            $line = str_replace('|LARGE|', '', $line);
        }

        if (strpos($line, '|UNDERLINE|') !== false) {
            $printer->setUnderline(true);
            $line = str_replace('|UNDERLINE|', '', $line);
        }

        // Print the line
        $printer->text($line . "\n");

        // Reset formatting
        $printer->setEmphasis(false);
        $printer->setTextSize(1, 1);
        $printer->setUnderline(false);
        $printer->setJustification(Printer::JUSTIFY_LEFT);
    }

    /**
     * Process special commands like barcodes, QR codes, etc.
     */
    private function processSpecialCommand($printer, $command, $printerConfig)
    {
        if (strpos($command, '[BARCODE:') === 0) {
            $barcode = substr($command, 9, -1);
            if ($printerConfig->supports_barcode) {
                $printer->setBarcodeHeight(50);
                $printer->setBarcodeWidth(2);
                $printer->barcode($barcode, Printer::BARCODE_CODE128);
            }
        } elseif (strpos($command, '[QRCODE:') === 0) {
            $qrData = substr($command, 8, -1);
            if ($printerConfig->supports_qr_code) {
                $this->printQRCode($printer, $qrData);
            }
        } elseif (strpos($command, '[IMAGE:') === 0) {
            $imagePath = substr($command, 7, -1);
            $this->printImage($printer, $imagePath);
        } elseif (strpos($command, '[SEPARATOR]') === 0) {
            $printer->text(str_repeat('-', $printerConfig->characters_per_line) . "\n");
        } elseif (strpos($command, '[FEED:') === 0) {
            $lines = (int) substr($command, 6, -1);
            $printer->feed($lines);
        }
    }

    /**
     * Print QR code
     */
    private function printQRCode($printer, $qrData)
    {
        try {
            // Generate QR code image
            $qrImage = QrCode::format('png')
                            ->size(150)
                            ->margin(1)
                            ->generate($qrData);

            // Save temporarily
            $tempPath = storage_path('app/temp/qr_' . Str::random(10) . '.png');
            file_put_contents($tempPath, $qrImage);

            // Print image
            $img = EscposImage::load($tempPath);
            $printer->bitImage($img);

            // Clean up
            unlink($tempPath);

        } catch (\Exception $e) {
            // Fallback to text if QR code fails
            $printer->text("QR: " . $qrData . "\n");
        }
    }

    /**
     * Print image
     */
    private function printImage($printer, $imagePath)
    {
        try {
            $fullPath = storage_path('app/public/' . $imagePath);
            if (file_exists($fullPath)) {
                $img = EscposImage::load($fullPath);
                $printer->bitImage($img);
            }
        } catch (\Exception $e) {
            // Ignore image errors
        }
    }

    /**
     * Get printer configuration
     */
    private function getPrinter($printerId, $printType)
    {
        if ($printerId) {
            return PrinterConfiguration::findOrFail($printerId);
        }

        // Get default printer for print type
        $defaultField = "is_default_{$printType}";
        $printer = PrinterConfiguration::where($defaultField, true)
                                     ->where('is_active', true)
                                     ->first();

        if (!$printer) {
            // Get any active printer
            $printer = PrinterConfiguration::where('is_active', true)->first();
        }

        if (!$printer) {
            throw new \Exception("No active printer found for {$printType}");
        }

        return $printer;
    }

    /**
     * Get print template
     */
    private function getTemplate($templateType)
    {
        $template = PrintTemplate::where('template_type', $templateType)
                                ->where('is_active', true)
                                ->where('is_default', true)
                                ->first();

        if (!$template) {
            $template = PrintTemplate::where('template_type', $templateType)
                                    ->where('is_active', true)
                                    ->first();
        }

        return $template;
    }

    /**
     * Create print connector based on configuration
     */
    private function createPrintConnector(PrinterConfiguration $config)
    {
        switch ($config->connection_type) {
            case 'network':
                return new NetworkPrintConnector($config->connection_string, $config->port ?? 9100);
                
            case 'usb':
            case 'file':
                return new FilePrintConnector($config->device_path ?: $config->connection_string);
                
            case 'windows':
                return new WindowsPrintConnector($config->connection_string);
                
            default:
                throw new \Exception("Unsupported connection type: " . $config->connection_type);
        }
    }

    /**
     * Generate unique job ID
     */
    private function generateJobId()
    {
        do {
            $jobId = 'PJ' . now()->format('ymdHis') . strtoupper(Str::random(4));
        } while (PrintJob::where('job_id', $jobId)->exists());

        return $jobId;
    }

    /**
     * Prepare invoice data for printing
     */
    private function prepareInvoiceData($invoice)
    {
        return [
            'company_name' => config('app.company_name', 'JewelSoft'),
            'company_address' => config('app.company_address', ''),
            'company_phone' => config('app.company_phone', ''),
            'company_email' => config('app.company_email', ''),
            'company_gstin' => config('app.company_gstin', ''),
            'invoice_number' => $invoice->invoice_number,
            'invoice_date' => $invoice->invoice_date->format('d/m/Y'),
            'due_date' => $invoice->due_date ? $invoice->due_date->format('d/m/Y') : '',
            'customer_name' => $invoice->customer->name,
            'customer_phone' => $invoice->customer->phone,
            'customer_email' => $invoice->customer->email,
            'customer_address' => $invoice->customer->getFullAddress(),
            'customer_gstin' => $invoice->customer->gstin,
            'items' => $invoice->items->map(function ($item) {
                return [
                    'product_name' => $item->product->name,
                    'hsn_code' => $item->product->hsn_code,
                    'quantity' => $item->quantity,
                    'unit_price' => number_format($item->unit_price, 2),
                    'total_price' => number_format($item->total_price, 2),
                    'tax_rate' => $item->tax_rate,
                    'tax_amount' => number_format($item->tax_amount, 2),
                ];
            })->toArray(),
            'subtotal' => number_format($invoice->subtotal, 2),
            'total_tax' => number_format($invoice->total_tax, 2),
            'total_amount' => number_format($invoice->total_amount, 2),
            'amount_in_words' => $invoice->amount_in_words,
            'payment_status' => $invoice->payment_status,
            'notes' => $invoice->notes,
            'terms_conditions' => $invoice->terms_conditions,
            'print_time' => now()->format('d/m/Y H:i:s'),
            'printed_by' => auth()->user()->name,
        ];
    }

    /**
     * Prepare receipt data for printing
     */
    private function prepareReceiptData($invoice)
    {
        return [
            'company_name' => config('app.company_name', 'JewelSoft'),
            'receipt_number' => $invoice->invoice_number,
            'date' => $invoice->invoice_date->format('d/m/Y H:i'),
            'customer_name' => $invoice->customer->name,
            'customer_phone' => $invoice->customer->phone,
            'items' => $invoice->items->map(function ($item) {
                return [
                    'name' => $item->product->name,
                    'qty' => $item->quantity,
                    'price' => number_format($item->unit_price, 2),
                    'total' => number_format($item->total_price, 2),
                ];
            })->toArray(),
            'subtotal' => number_format($invoice->subtotal, 2),
            'tax' => number_format($invoice->total_tax, 2),
            'total' => number_format($invoice->total_amount, 2),
            'payment_method' => $invoice->payment_method ?? 'Cash',
            'cashier' => auth()->user()->name,
            'thank_you_message' => 'Thank you for your purchase!',
        ];
    }

    /**
     * Prepare estimate data for printing
     */
    private function prepareEstimateData($estimate)
    {
        return [
            'company_name' => config('app.company_name', 'JewelSoft'),
            'estimate_number' => $estimate->estimate_number,
            'estimate_date' => $estimate->estimate_date->format('d/m/Y'),
            'valid_until' => $estimate->valid_until ? $estimate->valid_until->format('d/m/Y') : '',
            'customer_name' => $estimate->customer->name,
            'customer_phone' => $estimate->customer->phone,
            'items' => $estimate->items->map(function ($item) {
                return [
                    'product_name' => $item->product->name,
                    'quantity' => $item->quantity,
                    'unit_price' => number_format($item->unit_price, 2),
                    'total_price' => number_format($item->total_price, 2),
                ];
            })->toArray(),
            'subtotal' => number_format($estimate->subtotal, 2),
            'total_tax' => number_format($estimate->total_tax, 2),
            'total_amount' => number_format($estimate->total_amount, 2),
            'notes' => $estimate->notes,
            'terms_conditions' => $estimate->terms_conditions,
            'prepared_by' => auth()->user()->name,
        ];
    }

    /**
     * Prepare inventory tag data
     */
    private function prepareInventoryTagData($item)
    {
        return [
            'unique_tag' => $item->unique_tag,
            'product_name' => $item->product->name,
            'category' => $item->product->category->name,
            'metal' => $item->product->metal->name,
            'weight' => $item->actual_weight ? $item->actual_weight . 'g' : '',
            'purity' => $item->purity,
            'price' => '₹' . number_format($item->selling_price, 2),
            'barcode' => $item->barcode,
            'qr_data' => $item->qr_code,
            'huid' => $item->huid_number,
            'location' => $item->location->name,
            'created_date' => $item->created_at->format('d/m/Y'),
        ];
    }

    /**
     * Prepare barcode data
     */
    private function prepareBarcodeData($item)
    {
        return [
            'barcode' => $item->barcode,
            'product_name' => $item->product->name,
            'unique_tag' => $item->unique_tag,
            'price' => '₹' . number_format($item->selling_price, 2),
        ];
    }

    /**
     * Prepare QR code data
     */
    private function prepareQRCodeData($item)
    {
        return [
            'qr_data' => $item->qr_code,
            'unique_tag' => $item->unique_tag,
            'product_name' => $item->product->name,
            'price' => '₹' . number_format($item->selling_price, 2),
        ];
    }

    /**
     * Generate default content if no template found
     */
    private function generateDefaultContent(PrintJob $printJob)
    {
        switch ($printJob->print_type) {
            case 'invoice':
                return $this->generateDefaultInvoiceContent($printJob);
            case 'receipt':
                return $this->generateDefaultReceiptContent($printJob);
            case 'tag':
                return $this->generateDefaultTagContent($printJob);
            default:
                return "Print job: " . $printJob->job_id . "\nType: " . $printJob->print_type . "\n";
        }
    }

    /**
     * Generate default invoice content
     */
    private function generateDefaultInvoiceContent(PrintJob $printJob)
    {
        $data = json_decode($printJob->print_data, true);

        $content = "|CENTER||BOLD|{$data['company_name']}|BOLD|\n";
        $content .= "|CENTER|{$data['company_address']}\n";
        $content .= "|CENTER|Ph: {$data['company_phone']}\n";
        $content .= "|CENTER|GSTIN: {$data['company_gstin']}\n";
        $content .= "[SEPARATOR]\n";
        $content .= "|CENTER||BOLD|TAX INVOICE|BOLD|\n";
        $content .= "[SEPARATOR]\n";
        $content .= "Invoice No: {$data['invoice_number']}\n";
        $content .= "Date: {$data['invoice_date']}\n";
        $content .= "Customer: {$data['customer_name']}\n";
        $content .= "Phone: {$data['customer_phone']}\n";
        if (!empty($data['customer_gstin'])) {
            $content .= "GSTIN: {$data['customer_gstin']}\n";
        }
        $content .= "[SEPARATOR]\n";

        foreach ($data['items'] as $item) {
            $content .= "{$item['product_name']}\n";
            $content .= "HSN: {$item['hsn_code']} Qty: {$item['quantity']}\n";
            $content .= "|RIGHT|₹{$item['total_price']}\n";
        }

        $content .= "[SEPARATOR]\n";
        $content .= "|RIGHT|Subtotal: ₹{$data['subtotal']}\n";
        $content .= "|RIGHT|Tax: ₹{$data['total_tax']}\n";
        $content .= "|RIGHT||BOLD|Total: ₹{$data['total_amount']}|BOLD|\n";
        $content .= "[SEPARATOR]\n";
        $content .= "|CENTER|Thank you for your business!\n";
        $content .= "[FEED:3]\n";

        return $content;
    }

    /**
     * Generate default receipt content
     */
    private function generateDefaultReceiptContent(PrintJob $printJob)
    {
        $data = json_decode($printJob->print_data, true);

        $content = "|CENTER||BOLD|{$data['company_name']}|BOLD|\n";
        $content .= "|CENTER||BOLD|RECEIPT|BOLD|\n";
        $content .= "[SEPARATOR]\n";
        $content .= "Receipt: {$data['receipt_number']}\n";
        $content .= "Date: {$data['date']}\n";
        $content .= "Customer: {$data['customer_name']}\n";
        $content .= "Cashier: {$data['cashier']}\n";
        $content .= "[SEPARATOR]\n";

        foreach ($data['items'] as $item) {
            $content .= "{$item['name']}\n";
            $content .= "{$item['qty']} x ₹{$item['price']} = ₹{$item['total']}\n";
        }

        $content .= "[SEPARATOR]\n";
        $content .= "|RIGHT|Subtotal: ₹{$data['subtotal']}\n";
        $content .= "|RIGHT|Tax: ₹{$data['tax']}\n";
        $content .= "|RIGHT||BOLD|Total: ₹{$data['total']}|BOLD|\n";
        $content .= "[SEPARATOR]\n";
        $content .= "|CENTER|{$data['thank_you_message']}\n";
        $content .= "[FEED:2]\n";

        return $content;
    }

    /**
     * Generate default tag content
     */
    private function generateDefaultTagContent(PrintJob $printJob)
    {
        $data = json_decode($printJob->print_data, true);

        $content = "|CENTER||BOLD|{$data['product_name']}|BOLD|\n";
        $content .= "|CENTER|{$data['category']} - {$data['metal']}\n";
        $content .= "[SEPARATOR]\n";
        $content .= "Tag: {$data['unique_tag']}\n";
        if (!empty($data['weight'])) {
            $content .= "Weight: {$data['weight']}\n";
        }
        if (!empty($data['purity'])) {
            $content .= "Purity: {$data['purity']}\n";
        }
        $content .= "|CENTER||LARGE|{$data['price']}|LARGE|\n";

        if (!empty($data['barcode'])) {
            $content .= "[BARCODE:{$data['barcode']}]\n";
        }

        if (!empty($data['qr_data'])) {
            $content .= "[QRCODE:{$data['qr_data']}]\n";
        }

        $content .= "[FEED:2]\n";

        return $content;
    }

    /**
     * Format array data for printing
     */
    private function formatArrayForPrint($array)
    {
        if (empty($array)) {
            return '';
        }

        $formatted = '';
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $formatted .= $key . ": " . implode(', ', $value) . "\n";
            } else {
                $formatted .= $key . ": " . $value . "\n";
            }
        }

        return rtrim($formatted, "\n");
    }

    /**
     * Update printer statistics
     */
    private function updatePrinterStatistics($printerId, $success)
    {
        $printer = PrinterConfiguration::find($printerId);
        if ($printer) {
            $printer->increment('total_prints');
            if ($success) {
                $printer->increment('successful_prints');
                $printer->update(['last_used_at' => now(), 'is_online' => true]);
            } else {
                $printer->increment('failed_prints');
            }
        }
    }

    /**
     * Retry print job
     */
    private function retryPrintJob(PrintJob $printJob)
    {
        // Schedule retry after delay
        $delay = pow(2, $printJob->retry_count) * 60; // Exponential backoff in seconds

        // In a real application, you would use a queue system here
        // For now, we'll just update the status to pending for manual retry
        $printJob->update([
            'status' => 'pending',
            'queued_at' => now()->addSeconds($delay),
        ]);
    }

    /**
     * Get print job status
     */
    public function getPrintJobStatus($jobId)
    {
        return PrintJob::where('job_id', $jobId)->first();
    }

    /**
     * Cancel print job
     */
    public function cancelPrintJob($jobId)
    {
        $printJob = PrintJob::where('job_id', $jobId)
                           ->whereIn('status', ['pending', 'processing'])
                           ->first();

        if ($printJob) {
            $printJob->update([
                'status' => 'cancelled',
                'completed_at' => now(),
            ]);
            return true;
        }

        return false;
    }

    /**
     * Get printer status
     */
    public function getPrinterStatus($printerId)
    {
        $printer = PrinterConfiguration::find($printerId);
        if (!$printer) {
            return null;
        }

        try {
            // Try to connect to printer to check status
            $connector = $this->createPrintConnector($printer);
            $testPrinter = new Printer($connector);
            $testPrinter->close();

            $printer->update([
                'is_online' => true,
                'last_checked_at' => now(),
                'last_error' => null,
            ]);

            return [
                'online' => true,
                'last_checked' => now(),
                'error' => null,
            ];

        } catch (\Exception $e) {
            $printer->update([
                'is_online' => false,
                'last_checked_at' => now(),
                'last_error' => $e->getMessage(),
            ]);

            return [
                'online' => false,
                'last_checked' => now(),
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Test printer connection
     */
    public function testPrinter($printerId)
    {
        $printer = PrinterConfiguration::find($printerId);
        if (!$printer) {
            throw new \Exception("Printer not found");
        }

        try {
            $connector = $this->createPrintConnector($printer);
            $testPrinter = new Printer($connector);

            // Send test print
            $testPrinter->text("Printer Test\n");
            $testPrinter->text("Printer: {$printer->name}\n");
            $testPrinter->text("Date: " . now()->format('Y-m-d H:i:s') . "\n");
            $testPrinter->text("Status: OK\n");
            $testPrinter->feed(3);

            if ($printer->auto_cut) {
                $testPrinter->cut();
            }

            $testPrinter->close();

            $printer->update([
                'is_online' => true,
                'last_used_at' => now(),
                'last_error' => null,
            ]);

            return true;

        } catch (\Exception $e) {
            $printer->update([
                'is_online' => false,
                'last_error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get print queue
     */
    public function getPrintQueue($printerId = null, $status = null)
    {
        $query = PrintJob::with(['printerConfiguration', 'user']);

        if ($printerId) {
            $query->where('printer_configuration_id', $printerId);
        }

        if ($status) {
            $query->where('status', $status);
        }

        return $query->orderBy('queued_at', 'desc')->get();
    }

    /**
     * Clear completed jobs
     */
    public function clearCompletedJobs($olderThanDays = 7)
    {
        return PrintJob::whereIn('status', ['completed', 'cancelled'])
                      ->where('completed_at', '<', now()->subDays($olderThanDays))
                      ->delete();
    }

    /**
     * Get printing statistics
     */
    public function getPrintingStatistics($printerId = null, $days = 30)
    {
        $query = PrintJob::where('created_at', '>=', now()->subDays($days));

        if ($printerId) {
            $query->where('printer_configuration_id', $printerId);
        }

        $jobs = $query->get();

        return [
            'total_jobs' => $jobs->count(),
            'completed_jobs' => $jobs->where('status', 'completed')->count(),
            'failed_jobs' => $jobs->where('status', 'failed')->count(),
            'pending_jobs' => $jobs->where('status', 'pending')->count(),
            'success_rate' => $jobs->count() > 0 ?
                round(($jobs->where('status', 'completed')->count() / $jobs->count()) * 100, 2) : 0,
            'average_processing_time' => $jobs->where('status', 'completed')
                                             ->avg('processing_time_ms'),
            'jobs_by_type' => $jobs->groupBy('print_type')->map->count(),
            'jobs_by_day' => $jobs->groupBy(function ($job) {
                return $job->created_at->format('Y-m-d');
            })->map->count(),
        ];
    }
}
