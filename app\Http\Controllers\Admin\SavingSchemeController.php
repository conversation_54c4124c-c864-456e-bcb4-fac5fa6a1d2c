<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SavingScheme;
use App\Models\SavingSchemePlan;
use App\Models\SchemePayment;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SavingSchemeController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_saving_schemes')->only(['index', 'show']);
        $this->middleware('permission:manage_saving_schemes')->only(['create', 'store', 'edit', 'update', 'destroy']);
        $this->middleware('permission:process_scheme_payments')->only(['makePayment', 'processMaturity']);
    }

    /**
     * Display a listing of saving schemes
     */
    public function index(Request $request)
    {
        $query = SavingScheme::with(['customer', 'plan', 'payments']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('scheme_number', 'like', "%{$search}%")
                  ->orWhere('scheme_name', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($cq) use ($search) {
                      $cq->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by plan
        if ($request->filled('plan_id')) {
            $query->where('plan_id', $request->plan_id);
        }

        // Filter by customer
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('start_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('start_date', '<=', $request->date_to);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $schemes = $query->paginate(20);

        // Get filter options
        $plans = SavingSchemePlan::active()->orderBy('plan_name')->get(['id', 'plan_name']);
        $customers = Customer::orderBy('first_name')->get(['id', 'first_name', 'last_name']);

        $statuses = [
            'active' => 'Active',
            'matured' => 'Matured',
            'closed' => 'Closed',
            'suspended' => 'Suspended',
            'defaulted' => 'Defaulted',
        ];

        // Get summary statistics
        $stats = $this->getSchemeStats($request);

        return view('admin.saving-schemes.index', compact('schemes', 'plans', 'customers', 'statuses', 'stats'));
    }

    /**
     * Show the form for creating a new saving scheme
     */
    public function create()
    {
        $plans = SavingSchemePlan::active()->orderBy('plan_name')->get();
        $customers = Customer::orderBy('first_name')->get();

        return view('admin.saving-schemes.create', compact('plans', 'customers'));
    }

    /**
     * Store a newly created saving scheme
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'plan_id' => 'required|exists:saving_scheme_plans,id',
            'scheme_name' => 'nullable|string|max:100',
            'start_date' => 'required|date',
            'installment_amount' => 'required|numeric|min:1',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            $plan = SavingSchemePlan::findOrFail($validated['plan_id']);

            // Validate installment amount
            if ($validated['installment_amount'] < $plan->minimum_amount) {
                throw new \Exception("Minimum installment amount is {$plan->formatted_minimum_amount}");
            }

            if ($plan->maximum_amount && $validated['installment_amount'] > $plan->maximum_amount) {
                throw new \Exception("Maximum installment amount is {$plan->formatted_maximum_amount}");
            }

            // Calculate dates and amounts
            $startDate = Carbon::parse($validated['start_date']);
            $maturityDate = $startDate->copy()->addMonths($plan->duration_months);
            $totalTargetAmount = $validated['installment_amount'] * $plan->getTotalInstallments();

            $scheme = SavingScheme::create([
                'customer_id' => $validated['customer_id'],
                'plan_id' => $validated['plan_id'],
                'scheme_name' => $validated['scheme_name'],
                'start_date' => $startDate,
                'maturity_date' => $maturityDate,
                'installment_amount' => $validated['installment_amount'],
                'total_target_amount' => $totalTargetAmount,
                'next_due_date' => $startDate->copy()->addMonths($plan->getInstallmentFrequency()),
                'notes' => $validated['notes'],
                'status' => 'active',
            ]);

            DB::commit();

            return redirect()->route('admin.saving-schemes.show', $scheme)
                           ->with('success', 'Saving scheme created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                        ->with('error', 'Failed to create saving scheme: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified saving scheme
     */
    public function show(SavingScheme $savingScheme)
    {
        $savingScheme->load([
            'customer',
            'plan',
            'payments' => function ($query) {
                $query->orderBy('installment_number', 'desc');
            },
            'transactions' => function ($query) {
                $query->orderBy('transaction_date', 'desc');
            },
            'maturity'
        ]);

        // Calculate payment schedule
        $paymentSchedule = $this->generatePaymentSchedule($savingScheme);

        return view('admin.saving-schemes.show', compact('savingScheme', 'paymentSchedule'));
    }

    /**
     * Show the form for editing the saving scheme
     */
    public function edit(SavingScheme $savingScheme)
    {
        if (!in_array($savingScheme->status, ['active', 'suspended'])) {
            return redirect()->route('admin.saving-schemes.show', $savingScheme)
                           ->with('error', 'This scheme cannot be edited.');
        }

        $plans = SavingSchemePlan::active()->orderBy('plan_name')->get();
        $customers = Customer::orderBy('first_name')->get();

        return view('admin.saving-schemes.edit', compact('savingScheme', 'plans', 'customers'));
    }

    /**
     * Update the specified saving scheme
     */
    public function update(Request $request, SavingScheme $savingScheme)
    {
        if (!in_array($savingScheme->status, ['active', 'suspended'])) {
            return redirect()->route('admin.saving-schemes.show', $savingScheme)
                           ->with('error', 'This scheme cannot be edited.');
        }

        $validated = $request->validate([
            'scheme_name' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $savingScheme->update($validated);

            return redirect()->route('admin.saving-schemes.show', $savingScheme)
                           ->with('success', 'Saving scheme updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Failed to update saving scheme: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified saving scheme
     */
    public function destroy(SavingScheme $savingScheme)
    {
        if (!$savingScheme->can_close) {
            return back()->with('error', 'This scheme cannot be deleted.');
        }

        try {
            $savingScheme->delete();

            return redirect()->route('admin.saving-schemes.index')
                           ->with('success', 'Saving scheme deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete saving scheme: ' . $e->getMessage());
        }
    }

    /**
     * Make payment for a scheme
     */
    public function makePayment(Request $request, SavingScheme $savingScheme)
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|in:cash,card,upi,bank_transfer,cheque',
            'payment_reference' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $payment = $savingScheme->makePayment(
                $validated['amount'],
                $validated['payment_method'],
                $validated['payment_reference'],
                $validated['notes']
            );

            return response()->json([
                'success' => true,
                'message' => 'Payment recorded successfully.',
                'payment' => $payment->getSummary(),
                'scheme' => $savingScheme->fresh()->getSummary(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Helper methods
     */
    protected function getSchemeStats($request)
    {
        $query = SavingScheme::query();

        // Apply same filters as main query
        if ($request->filled('plan_id')) {
            $query->where('plan_id', $request->plan_id);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('start_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('start_date', '<=', $request->date_to);
        }

        return [
            'total_schemes' => $query->count(),
            'active_schemes' => $query->where('status', 'active')->count(),
            'matured_schemes' => $query->where('status', 'matured')->count(),
            'total_value' => $query->sum('current_value'),
            'total_target' => $query->sum('total_target_amount'),
            'overdue_schemes' => $query->overdue()->count(),
        ];
    }

    protected function generatePaymentSchedule($scheme)
    {
        $schedule = [];
        $currentDate = Carbon::parse($scheme->start_date);
        $frequency = $scheme->plan->getInstallmentFrequency();
        $totalInstallments = $scheme->plan->getTotalInstallments();

        for ($i = 1; $i <= $totalInstallments; $i++) {
            $dueDate = $currentDate->copy()->addMonths($frequency * ($i - 1));

            // Check if payment exists for this installment
            $payment = $scheme->payments()->where('installment_number', $i)->first();

            $schedule[] = [
                'installment_number' => $i,
                'due_date' => $dueDate,
                'amount' => $scheme->installment_amount,
                'status' => $payment ? $payment->status : 'pending',
                'payment' => $payment,
                'is_overdue' => !$payment && $dueDate->isPast(),
            ];
        }

        return $schedule;
    }
}
