<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CustomerInteraction;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Http\Request;

class CustomerInteractionController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_customer_interactions')->only(['index', 'show']);
        $this->middleware('permission:manage_customer_interactions')->only(['create', 'store', 'edit', 'update', 'destroy']);
    }

    /**
     * Display a listing of customer interactions
     */
    public function index(Request $request)
    {
        $query = CustomerInteraction::with(['customer', 'createdBy', 'assignedTo']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($cq) use ($search) {
                      $cq->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by interaction type
        if ($request->filled('interaction_type')) {
            $query->where('interaction_type', $request->interaction_type);
        }

        // Filter by channel
        if ($request->filled('channel')) {
            $query->where('channel', $request->channel);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter by assigned user
        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        // Filter by customer
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('interaction_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('interaction_date', '<=', $request->date_to);
        }

        // Filter by follow-up requirements
        if ($request->filled('follow_up_filter')) {
            if ($request->follow_up_filter === 'required') {
                $query->where('follow_up_required', true);
            } elseif ($request->follow_up_filter === 'overdue') {
                $query->overdueFollowUp();
            }
        }

        // Sort
        $sortBy = $request->get('sort_by', 'interaction_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $interactions = $query->paginate(20);

        // Get filter options
        $customers = Customer::orderBy('first_name')->get(['id', 'first_name', 'last_name']);
        $users = User::orderBy('name')->get(['id', 'name']);

        $interactionTypes = [
            'call' => 'Phone Call',
            'email' => 'Email',
            'sms' => 'SMS',
            'whatsapp' => 'WhatsApp',
            'visit' => 'Store Visit',
            'inquiry' => 'Product Inquiry',
            'complaint' => 'Complaint',
            'feedback' => 'Feedback',
            'follow_up' => 'Follow Up',
            'appointment' => 'Appointment',
            'consultation' => 'Consultation',
            'service' => 'Service Request',
            'other' => 'Other',
        ];

        $channels = [
            'phone' => 'Phone',
            'email' => 'Email',
            'sms' => 'SMS',
            'whatsapp' => 'WhatsApp',
            'in_person' => 'In Person',
            'website' => 'Website',
            'social_media' => 'Social Media',
            'referral' => 'Referral',
            'walk_in' => 'Walk In',
        ];

        $statuses = [
            'pending' => 'Pending',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'follow_up_scheduled' => 'Follow-up Scheduled',
        ];

        $priorities = [
            'low' => 'Low',
            'normal' => 'Normal',
            'high' => 'High',
            'urgent' => 'Urgent',
        ];

        return view('admin.customer-interactions.index', compact(
            'interactions',
            'customers',
            'users',
            'interactionTypes',
            'channels',
            'statuses',
            'priorities'
        ));
    }

    /**
     * Show the form for creating a new interaction
     */
    public function create(Request $request)
    {
        $customerId = $request->get('customer_id');
        $customer = $customerId ? Customer::findOrFail($customerId) : null;

        $customers = Customer::orderBy('first_name')->get(['id', 'first_name', 'last_name']);
        $users = User::orderBy('name')->get(['id', 'name']);

        return view('admin.customer-interactions.create', compact('customer', 'customers', 'users'));
    }

    /**
     * Store a newly created interaction
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'interaction_type' => 'required|string',
            'channel' => 'required|string',
            'subject' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => 'required|in:low,normal,high,urgent',
            'follow_up_required' => 'boolean',
            'follow_up_date' => 'nullable|date|after:today',
            'assigned_to' => 'nullable|exists:users,id',
            'duration_minutes' => 'nullable|integer|min:1',
            'metadata' => 'nullable|array',
        ]);

        try {
            $interaction = CustomerInteraction::create([
                'customer_id' => $validated['customer_id'],
                'interaction_type' => $validated['interaction_type'],
                'channel' => $validated['channel'],
                'subject' => $validated['subject'],
                'description' => $validated['description'],
                'priority' => $validated['priority'],
                'follow_up_required' => $validated['follow_up_required'] ?? false,
                'follow_up_date' => $validated['follow_up_date'] ?? null,
                'assigned_to' => $validated['assigned_to'] ?? null,
                'duration_minutes' => $validated['duration_minutes'] ?? null,
                'metadata' => $validated['metadata'] ?? [],
                'status' => $validated['assigned_to'] ? 'in_progress' : 'pending',
                'interaction_date' => now(),
            ]);

            return redirect()->route('admin.customer-interactions.show', $interaction)
                           ->with('success', 'Customer interaction created successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Failed to create interaction: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified interaction
     */
    public function show(CustomerInteraction $customerInteraction)
    {
        $customerInteraction->load(['customer', 'createdBy', 'assignedTo']);

        return view('admin.customer-interactions.show', compact('customerInteraction'));
    }

    /**
     * Show the form for editing the interaction
     */
    public function edit(CustomerInteraction $customerInteraction)
    {
        $customerInteraction->load(['customer']);

        $customers = Customer::orderBy('first_name')->get(['id', 'first_name', 'last_name']);
        $users = User::orderBy('name')->get(['id', 'name']);

        return view('admin.customer-interactions.edit', compact('customerInteraction', 'customers', 'users'));
    }

    /**
     * Update the specified interaction
     */
    public function update(Request $request, CustomerInteraction $customerInteraction)
    {
        $validated = $request->validate([
            'interaction_type' => 'required|string',
            'channel' => 'required|string',
            'subject' => 'required|string|max:255',
            'description' => 'nullable|string',
            'outcome' => 'nullable|string',
            'priority' => 'required|in:low,normal,high,urgent',
            'status' => 'required|in:pending,in_progress,completed,cancelled,follow_up_scheduled',
            'follow_up_required' => 'boolean',
            'follow_up_date' => 'nullable|date|after:today',
            'assigned_to' => 'nullable|exists:users,id',
            'duration_minutes' => 'nullable|integer|min:1',
            'metadata' => 'nullable|array',
        ]);

        try {
            $customerInteraction->update($validated);

            if ($validated['status'] === 'completed') {
                $customerInteraction->markCompleted($validated['outcome'] ?? null);
            }

            return redirect()->route('admin.customer-interactions.show', $customerInteraction)
                           ->with('success', 'Customer interaction updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'Failed to update interaction: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified interaction
     */
    public function destroy(CustomerInteraction $customerInteraction)
    {
        try {
            $customerInteraction->delete();

            return redirect()->route('admin.customer-interactions.index')
                           ->with('success', 'Customer interaction deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete interaction: ' . $e->getMessage());
        }
    }

    /**
     * Mark interaction as completed
     */
    public function markCompleted(Request $request, CustomerInteraction $customerInteraction)
    {
        $validated = $request->validate([
            'outcome' => 'nullable|string|max:500',
        ]);

        try {
            $customerInteraction->markCompleted($validated['outcome'] ?? null);

            return response()->json([
                'success' => true,
                'message' => 'Interaction marked as completed.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Schedule follow-up for interaction
     */
    public function scheduleFollowUp(Request $request, CustomerInteraction $customerInteraction)
    {
        $validated = $request->validate([
            'follow_up_date' => 'required|date|after:today',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $customerInteraction->scheduleFollowUp(
                new \DateTime($validated['follow_up_date']),
                $validated['notes'] ?? null
            );

            return response()->json([
                'success' => true,
                'message' => 'Follow-up scheduled successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Assign interaction to user
     */
    public function assign(Request $request, CustomerInteraction $customerInteraction)
    {
        $validated = $request->validate([
            'assigned_to' => 'required|exists:users,id',
        ]);

        try {
            $user = User::findOrFail($validated['assigned_to']);
            $customerInteraction->assignTo($user);

            return response()->json([
                'success' => true,
                'message' => 'Interaction assigned successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Add note to interaction
     */
    public function addNote(Request $request, CustomerInteraction $customerInteraction)
    {
        $validated = $request->validate([
            'note' => 'required|string|max:1000',
        ]);

        try {
            $customerInteraction->addNote($validated['note']);

            return response()->json([
                'success' => true,
                'message' => 'Note added successfully.',
                'interaction' => $customerInteraction->getSummary(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get interaction statistics
     */
    public function getStatistics(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = now()->subDays($days);

        $stats = [
            'total_interactions' => CustomerInteraction::where('created_at', '>=', $startDate)->count(),
            'pending_interactions' => CustomerInteraction::pending()->where('created_at', '>=', $startDate)->count(),
            'completed_interactions' => CustomerInteraction::completed()->where('created_at', '>=', $startDate)->count(),
            'overdue_follow_ups' => CustomerInteraction::overdueFollowUp()->count(),
            'interactions_by_type' => CustomerInteraction::where('created_at', '>=', $startDate)
                                                      ->groupBy('interaction_type')
                                                      ->selectRaw('interaction_type, count(*) as count')
                                                      ->pluck('count', 'interaction_type'),
            'interactions_by_channel' => CustomerInteraction::where('created_at', '>=', $startDate)
                                                           ->groupBy('channel')
                                                           ->selectRaw('channel, count(*) as count')
                                                           ->pluck('count', 'channel'),
            'average_resolution_time' => CustomerInteraction::completed()
                                                           ->where('created_at', '>=', $startDate)
                                                           ->avg('duration_minutes'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Export interactions to CSV
     */
    public function export(Request $request)
    {
        $query = CustomerInteraction::with(['customer', 'createdBy', 'assignedTo']);

        // Apply same filters as index
        if ($request->filled('interaction_type')) {
            $query->where('interaction_type', $request->interaction_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('interaction_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('interaction_date', '<=', $request->date_to);
        }

        $interactions = $query->get();

        $filename = 'customer_interactions_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($interactions) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'Customer', 'Type', 'Channel', 'Subject', 'Status', 'Priority',
                'Interaction Date', 'Duration (min)', 'Follow-up Required', 'Follow-up Date',
                'Created By', 'Assigned To', 'Outcome'
            ]);

            // CSV data
            foreach ($interactions as $interaction) {
                fputcsv($file, [
                    $interaction->id,
                    $interaction->customer->full_name,
                    $interaction->interaction_type_display,
                    $interaction->channel_display,
                    $interaction->subject,
                    $interaction->status_display,
                    $interaction->priority_display,
                    $interaction->interaction_date->format('Y-m-d H:i:s'),
                    $interaction->duration_minutes,
                    $interaction->follow_up_required ? 'Yes' : 'No',
                    $interaction->follow_up_date?->format('Y-m-d H:i:s'),
                    $interaction->createdBy->name,
                    $interaction->assignedTo?->name,
                    $interaction->outcome,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
