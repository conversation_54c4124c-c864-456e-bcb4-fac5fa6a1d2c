<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SavingSchemePlan;
use App\Models\SavingScheme;
use App\Models\SchemePayment;
use App\Models\SchemeTransaction;
use App\Models\SchemeMaturity;
use App\Models\SchemeNotification;
use Illuminate\Support\Facades\DB;

class FixUniqueConstraints extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'saving-schemes:fix-constraints {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix unique constraint violations in saving scheme tables';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        $this->info('Checking for unique constraint violations in saving scheme tables...');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $this->fixSavingSchemePlans($dryRun);
        $this->fixSavingSchemes($dryRun);
        $this->fixSchemePayments($dryRun);
        $this->fixSchemeTransactions($dryRun);
        $this->fixSchemeMaturity($dryRun);
        $this->fixSchemeNotifications($dryRun);

        $this->info('Unique constraint check completed!');
    }

    private function fixSavingSchemePlans($dryRun)
    {
        $this->info('Checking saving_scheme_plans table...');

        // Find duplicate plan_codes
        $duplicates = DB::select("
            SELECT plan_code, COUNT(*) as count
            FROM saving_scheme_plans
            WHERE plan_code IS NOT NULL
            GROUP BY plan_code
            HAVING COUNT(*) > 1
        ");

        if (empty($duplicates)) {
            $this->info('✅ No duplicate plan_codes found');
            return;
        }

        foreach ($duplicates as $duplicate) {
            $this->warn("Found {$duplicate->count} records with plan_code: {$duplicate->plan_code}");

            if (!$dryRun) {
                // Keep the first record, update others
                $records = SavingSchemePlan::where('plan_code', $duplicate->plan_code)
                                         ->orderBy('id')
                                         ->get();

                foreach ($records->skip(1) as $record) {
                    $newCode = SavingSchemePlan::generatePlanCode($record);
                    $record->update(['plan_code' => $newCode]);
                    $this->info("Updated record ID {$record->id} with new plan_code: {$newCode}");
                }
            }
        }
    }

    private function fixSavingSchemes($dryRun)
    {
        $this->info('Checking saving_schemes table...');

        $duplicates = DB::select("
            SELECT scheme_number, COUNT(*) as count
            FROM saving_schemes
            WHERE scheme_number IS NOT NULL
            GROUP BY scheme_number
            HAVING COUNT(*) > 1
        ");

        if (empty($duplicates)) {
            $this->info('✅ No duplicate scheme_numbers found');
            return;
        }

        foreach ($duplicates as $duplicate) {
            $this->warn("Found {$duplicate->count} records with scheme_number: {$duplicate->scheme_number}");

            if (!$dryRun) {
                $records = SavingScheme::where('scheme_number', $duplicate->scheme_number)
                                     ->orderBy('id')
                                     ->get();

                foreach ($records->skip(1) as $record) {
                    $newNumber = SavingScheme::generateSchemeNumber();
                    $record->update(['scheme_number' => $newNumber]);
                    $this->info("Updated record ID {$record->id} with new scheme_number: {$newNumber}");
                }
            }
        }
    }

    private function fixSchemePayments($dryRun)
    {
        $this->info('Checking scheme_payments table...');

        $duplicates = DB::select("
            SELECT payment_number, COUNT(*) as count
            FROM scheme_payments
            WHERE payment_number IS NOT NULL
            GROUP BY payment_number
            HAVING COUNT(*) > 1
        ");

        if (empty($duplicates)) {
            $this->info('✅ No duplicate payment_numbers found');
            return;
        }

        foreach ($duplicates as $duplicate) {
            $this->warn("Found {$duplicate->count} records with payment_number: {$duplicate->payment_number}");

            if (!$dryRun) {
                $records = SchemePayment::where('payment_number', $duplicate->payment_number)
                                      ->orderBy('id')
                                      ->get();

                foreach ($records->skip(1) as $record) {
                    $newNumber = SchemePayment::generatePaymentNumber();
                    $record->update(['payment_number' => $newNumber]);
                    $this->info("Updated record ID {$record->id} with new payment_number: {$newNumber}");
                }
            }
        }
    }

    private function fixSchemeTransactions($dryRun)
    {
        $this->info('Checking scheme_transactions table...');

        $duplicates = DB::select("
            SELECT transaction_number, COUNT(*) as count
            FROM scheme_transactions
            WHERE transaction_number IS NOT NULL
            GROUP BY transaction_number
            HAVING COUNT(*) > 1
        ");

        if (empty($duplicates)) {
            $this->info('✅ No duplicate transaction_numbers found');
            return;
        }

        foreach ($duplicates as $duplicate) {
            $this->warn("Found {$duplicate->count} records with transaction_number: {$duplicate->transaction_number}");

            if (!$dryRun) {
                $records = SchemeTransaction::where('transaction_number', $duplicate->transaction_number)
                                          ->orderBy('id')
                                          ->get();

                foreach ($records->skip(1) as $record) {
                    $newNumber = SchemeTransaction::generateTransactionNumber();
                    $record->update(['transaction_number' => $newNumber]);
                    $this->info("Updated record ID {$record->id} with new transaction_number: {$newNumber}");
                }
            }
        }
    }

    private function fixSchemeMaturity($dryRun)
    {
        $this->info('Checking scheme_maturities table...');
        // Add similar logic for maturity numbers if needed
        $this->info('✅ No unique constraints to check in scheme_maturities');
    }

    private function fixSchemeNotifications($dryRun)
    {
        $this->info('Checking scheme_notifications table...');
        // Add similar logic for notification references if needed
        $this->info('✅ No unique constraints to check in scheme_notifications');
    }
}
