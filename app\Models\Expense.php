<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Expense extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'expense_number',
        'expense_date',
        'category_id',
        'supplier_id',
        'description',
        'amount',
        'tax_amount',
        'total_amount',
        'payment_method',
        'payment_reference',
        'receipt_number',
        'receipt_path',
        'status',
        'approved_by',
        'approved_at',
        'paid_at',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'expense_date' => 'date',
        'amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($expense) {
            if (!$expense->expense_number) {
                $expense->expense_number = static::generateExpenseNumber();
            }
            $expense->created_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ExpenseCategory::class, 'category_id');
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function journalEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class, 'reference_id')
                   ->where('reference_type', get_class($this));
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('expense_date', [$startDate, $endDate]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('expense_date', now()->month)
                    ->whereYear('expense_date', now()->year);
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute(): string
    {
        $statuses = [
            'pending' => 'Pending Approval',
            'approved' => 'Approved',
            'paid' => 'Paid',
            'rejected' => 'Rejected',
            'cancelled' => 'Cancelled',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute(): string
    {
        $colors = [
            'pending' => 'yellow',
            'approved' => 'blue',
            'paid' => 'green',
            'rejected' => 'red',
            'cancelled' => 'gray',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getFormattedAmountAttribute(): string
    {
        return '₹' . number_format($this->total_amount, 2);
    }

    public function getCanApproveAttribute(): bool
    {
        return $this->status === 'pending';
    }

    public function getCanPayAttribute(): bool
    {
        return $this->status === 'approved';
    }

    public function getCanEditAttribute(): bool
    {
        return in_array($this->status, ['pending']);
    }

    public function getCanDeleteAttribute(): bool
    {
        return in_array($this->status, ['pending', 'rejected']);
    }

    public function getReceiptUrlAttribute(): ?string
    {
        return $this->receipt_path ? asset('storage/' . $this->receipt_path) : null;
    }

    /**
     * Business Logic Methods
     */
    public static function generateExpenseNumber(): string
    {
        $prefix = 'EXP';
        $year = now()->format('Y');
        $month = now()->format('m');

        $lastExpense = static::whereYear('created_at', now()->year)
                            ->whereMonth('created_at', now()->month)
                            ->orderBy('id', 'desc')
                            ->first();

        $sequence = $lastExpense ? (int) substr($lastExpense->expense_number, -4) + 1 : 1;

        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function approve($approverId = null): self
    {
        if (!$this->can_approve) {
            throw new \Exception('Expense cannot be approved');
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $approverId ?: auth()->id(),
            'approved_at' => now(),
        ]);

        return $this;
    }

    public function reject($reason = null): self
    {
        if (!$this->can_approve) {
            throw new \Exception('Expense cannot be rejected');
        }

        $this->update([
            'status' => 'rejected',
            'notes' => $reason ? ($this->notes . "\n\nRejected: " . $reason) : $this->notes,
        ]);

        return $this;
    }

    public function markAsPaid($paymentMethod = null, $paymentReference = null): self
    {
        if (!$this->can_pay) {
            throw new \Exception('Expense cannot be marked as paid');
        }

        $this->update([
            'status' => 'paid',
            'payment_method' => $paymentMethod ?: $this->payment_method,
            'payment_reference' => $paymentReference ?: $this->payment_reference,
            'paid_at' => now(),
        ]);

        // Create journal entry
        $this->createJournalEntry();

        return $this;
    }

    public function cancel($reason = null): self
    {
        if ($this->status === 'paid') {
            throw new \Exception('Cannot cancel paid expense');
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? ($this->notes . "\n\nCancelled: " . $reason) : $this->notes,
        ]);

        return $this;
    }

    public function uploadReceipt($file): self
    {
        $path = $file->store('expenses/receipts', 'public');

        $this->update(['receipt_path' => $path]);

        return $this;
    }

    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'expense_number' => $this->expense_number,
            'expense_date' => $this->expense_date->format('Y-m-d'),
            'category' => $this->category->name,
            'supplier' => $this->supplier?->name,
            'description' => $this->description,
            'amount' => $this->amount,
            'tax_amount' => $this->tax_amount,
            'total_amount' => $this->total_amount,
            'formatted_amount' => $this->formatted_amount,
            'status' => $this->status_display,
            'payment_method' => $this->payment_method,
            'receipt_number' => $this->receipt_number,
            'receipt_url' => $this->receipt_url,
            'approved_by' => $this->approvedBy?->name,
            'approved_at' => $this->approved_at?->format('Y-m-d H:i:s'),
            'paid_at' => $this->paid_at?->format('Y-m-d H:i:s'),
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
