<?php

namespace App\Traits;

use Illuminate\Support\Str;

trait GeneratesUniqueFields
{
    /**
     * Generate a unique field value with collision detection
     */
    public static function generateUniqueField(string $field, callable $generator, int $maxAttempts = 100): string
    {
        $attempts = 0;

        do {
            $value = $generator($attempts);
            $attempts++;

            if ($attempts > $maxAttempts) {
                throw new \Exception("Could not generate unique {$field} after {$maxAttempts} attempts");
            }

            // Add a small random delay to prevent race conditions
            if ($attempts > 1) {
                usleep(rand(1000, 5000)); // 1-5 milliseconds
            }

        } while (static::where($field, $value)->exists());

        return $value;
    }

    /**
     * Handle unique constraint violations with retry logic
     */
    public static function createWithRetry(array $attributes, int $maxAttempts = 5): ?self
    {
        $attempts = 0;

        while ($attempts < $maxAttempts) {
            try {
                return static::create($attributes);
            } catch (\Illuminate\Database\QueryException $e) {
                $attempts++;

                // Check if it's a unique constraint violation
                if (str_contains($e->getMessage(), 'Duplicate entry') ||
                    str_contains($e->getMessage(), 'Integrity constraint violation')) {

                    if ($attempts >= $maxAttempts) {
                        throw new \Exception("Failed to create record after {$maxAttempts} attempts due to unique constraint violations");
                    }

                    // Wait a bit before retrying
                    usleep(rand(10000, 50000)); // 10-50 milliseconds

                    // Force regeneration of unique fields
                    static::forceRegenerateUniqueFields($attributes);

                    continue;
                } else {
                    // If it's not a unique constraint violation, re-throw the exception
                    throw $e;
                }
            }
        }

        return null;
    }

    /**
     * Force regeneration of unique fields
     */
    protected static function forceRegenerateUniqueFields(array &$attributes): void
    {
        // This method can be overridden in each model to handle specific unique fields
        // For now, we'll clear any auto-generated fields to force regeneration
        $uniqueFields = ['plan_code', 'scheme_number', 'payment_number', 'transaction_number', 'maturity_number', 'reference_number'];

        foreach ($uniqueFields as $field) {
            if (array_key_exists($field, $attributes)) {
                unset($attributes[$field]);
            }
        }
    }

    /**
     * Generate unique plan code
     */
    public static function generateUniquePlanCode($plan): string
    {
        return static::generateUniqueField('plan_code', function ($attempt) use ($plan) {
            $typeCode = strtoupper(substr($plan->plan_type, 0, 3));
            $durationCode = str_pad($plan->duration_months, 2, '0', STR_PAD_LEFT);
            $timestamp = now()->format('ymdHis');
            $microseconds = substr(microtime(), 2, 6);
            $random = rand(1000, 9999);
            $attemptSuffix = $attempt > 0 ? str_pad($attempt, 3, '0', STR_PAD_LEFT) : '';
            
            return "SP{$typeCode}{$durationCode}{$timestamp}{$microseconds}{$random}{$attemptSuffix}";
        });
    }

    /**
     * Generate unique scheme number
     */
    public static function generateUniqueSchemeNumber(): string
    {
        return static::generateUniqueField('scheme_number', function ($attempt) {
            $prefix = 'SS';
            $year = now()->format('Y');
            $month = now()->format('m');
            $day = now()->format('d');
            $time = now()->format('His');
            $microseconds = substr(microtime(), 2, 6);
            $random = rand(1000, 9999);
            $attemptSuffix = $attempt > 0 ? str_pad($attempt, 3, '0', STR_PAD_LEFT) : '';
            
            return "{$prefix}{$year}{$month}{$day}{$time}{$microseconds}{$random}{$attemptSuffix}";
        });
    }

    /**
     * Generate unique payment number
     */
    public static function generateUniquePaymentNumber(): string
    {
        return static::generateUniqueField('payment_number', function ($attempt) {
            $prefix = 'SP';
            $timestamp = now()->format('ymdHis');
            $microseconds = substr(microtime(), 2, 6);
            $random = rand(10000, 99999);
            $attemptSuffix = $attempt > 0 ? str_pad($attempt, 3, '0', STR_PAD_LEFT) : '';
            
            return "{$prefix}{$timestamp}{$microseconds}{$random}{$attemptSuffix}";
        });
    }

    /**
     * Generate unique transaction number
     */
    public static function generateUniqueTransactionNumber(): string
    {
        return static::generateUniqueField('transaction_number', function ($attempt) {
            $prefix = 'ST';
            $timestamp = now()->format('ymdHis');
            $microseconds = substr(microtime(), 2, 6);
            $random = rand(10000, 99999);
            $attemptSuffix = $attempt > 0 ? str_pad($attempt, 3, '0', STR_PAD_LEFT) : '';
            
            return "{$prefix}{$timestamp}{$microseconds}{$random}{$attemptSuffix}";
        });
    }

    /**
     * Generate unique maturity number
     */
    public static function generateUniqueMaturityNumber(): string
    {
        return static::generateUniqueField('maturity_number', function ($attempt) {
            $prefix = 'SM';
            $timestamp = now()->format('ymdHis');
            $microseconds = substr(microtime(), 2, 6);
            $random = rand(10000, 99999);
            $attemptSuffix = $attempt > 0 ? str_pad($attempt, 3, '0', STR_PAD_LEFT) : '';
            
            return "{$prefix}{$timestamp}{$microseconds}{$random}{$attemptSuffix}";
        });
    }

    /**
     * Generate unique notification reference
     */
    public static function generateUniqueNotificationReference(): string
    {
        return static::generateUniqueField('reference_number', function ($attempt) {
            $prefix = 'SN';
            $timestamp = now()->format('ymdHis');
            $microseconds = substr(microtime(), 2, 6);
            $random = rand(10000, 99999);
            $attemptSuffix = $attempt > 0 ? str_pad($attempt, 3, '0', STR_PAD_LEFT) : '';
            
            return "{$prefix}{$timestamp}{$microseconds}{$random}{$attemptSuffix}";
        });
    }
}
