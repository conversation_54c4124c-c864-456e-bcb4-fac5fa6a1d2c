<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class DefaultUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+91-9876543210',
            'employee_id' => 'EMP001',
            'location_id' => 1, // Main Store
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $superAdmin->assignRole('Super Admin');

        // Create Store Manager
        $manager = User::create([
            'name' => 'Store Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+91-9876543211',
            'employee_id' => 'EMP002',
            'location_id' => 1,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $manager->assignRole('Store Manager');

        // Create Sales Staff
        $salesStaff = User::create([
            'name' => 'Sales Executive',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+91-9876543212',
            'employee_id' => 'EMP003',
            'location_id' => 1,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $salesStaff->assignRole('Sales Staff');

        // Create Cashier
        $cashier = User::create([
            'name' => 'Cashier',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+91-9876543213',
            'employee_id' => 'EMP004',
            'location_id' => 1,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $cashier->assignRole('Cashier');

        // Create Inventory Manager
        $inventoryManager = User::create([
            'name' => 'Inventory Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+91-**********',
            'employee_id' => 'EMP005',
            'location_id' => 1,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $inventoryManager->assignRole('Inventory Manager');

        // Create Accountant
        $accountant = User::create([
            'name' => 'Accountant',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+91-**********',
            'employee_id' => 'EMP006',
            'location_id' => 1,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $accountant->assignRole('Accountant');

        // Create Repair Technician
        $repairTech = User::create([
            'name' => 'Repair Technician',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+91-**********',
            'employee_id' => 'EMP007',
            'location_id' => 1,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $repairTech->assignRole('Repair Technician');
    }
}
