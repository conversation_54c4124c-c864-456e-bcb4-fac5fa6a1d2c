<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->route('user');
        
        // Users can update their own profile with limited fields
        if ($this->user()->id === $user->id) {
            return true;
        }
        
        // Otherwise, need manage_users permission
        return $this->user()->can('manage_users');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->route('user');
        $isOwnProfile = $this->user()->id === $user->id;
        
        $rules = [
            'name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20', 'regex:/^[\+]?[0-9\s\-\(\)]+$/'],
            'profile_photo' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ];

        // Only allow these fields if user has manage_users permission
        if (!$isOwnProfile && $this->user()->can('manage_users')) {
            $rules = array_merge($rules, [
                'employee_id' => ['nullable', 'string', 'max:50', Rule::unique('users')->ignore($user->id)],
                'location_id' => ['nullable', 'exists:locations,id'],
                'role' => ['required', 'exists:roles,name'],
                'is_active' => ['boolean'],
            ]);
        }

        // Password rules (optional for updates)
        if ($this->filled('password')) {
            $rules['password'] = ['confirmed', Password::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->symbols()
                ->uncompromised()];
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'The name may only contain letters and spaces.',
            'phone.regex' => 'The phone number format is invalid.',
            'employee_id.unique' => 'This employee ID is already taken.',
            'password.uncompromised' => 'The given password has appeared in a data leak. Please choose a different password.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'employee_id' => 'employee ID',
            'location_id' => 'location',
            'is_active' => 'status',
            'profile_photo' => 'profile photo',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure is_active is boolean if provided
        if ($this->has('is_active')) {
            $this->merge([
                'is_active' => $this->boolean('is_active'),
            ]);
        }
    }
}
