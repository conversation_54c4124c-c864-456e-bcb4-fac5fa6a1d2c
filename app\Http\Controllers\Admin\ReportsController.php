<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Estimate;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Inventory;
use App\Models\InventoryMovement;
use App\Models\Location;
use App\Services\ReportService;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ReportsController extends Controller
{
    protected $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    /**
     * Main reports dashboard
     */
    public function index()
    {
        $dateRange = [
            'start' => now()->startOfMonth(),
            'end' => now()->endOfMonth()
        ];

        // Get dashboard summary data
        $summary = $this->reportService->getDashboardSummary($dateRange);

        return view('admin.reports.index', compact('summary', 'dateRange'));
    }

    /**
     * Sales reports
     */
    public function sales(Request $request)
    {
        $dateFrom = $request->date_from ?? now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->endOfMonth()->format('Y-m-d');
        $locationId = $request->location_id;
        $groupBy = $request->group_by ?? 'day';

        $salesData = $this->reportService->getSalesReport([
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'location_id' => $locationId,
            'group_by' => $groupBy
        ]);

        $locations = Location::active()->get();

        return view('admin.reports.sales', compact('salesData', 'locations', 'dateFrom', 'dateTo', 'locationId', 'groupBy'));
    }

    /**
     * Inventory reports
     */
    public function inventory(Request $request)
    {
        $locationId = $request->location_id;
        $categoryId = $request->category_id;
        $lowStock = $request->low_stock;

        $inventoryData = $this->reportService->getInventoryReport([
            'location_id' => $locationId,
            'category_id' => $categoryId,
            'low_stock' => $lowStock
        ]);

        $locations = Location::active()->get();
        $categories = \App\Models\Category::active()->get();

        return view('admin.reports.inventory', compact('inventoryData', 'locations', 'categories', 'locationId', 'categoryId', 'lowStock'));
    }

    /**
     * Customer analytics
     */
    public function customers(Request $request)
    {
        $dateFrom = $request->date_from ?? now()->subMonths(6)->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');
        $segment = $request->segment;

        $customerData = $this->reportService->getCustomerAnalytics([
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'segment' => $segment
        ]);

        return view('admin.reports.customers', compact('customerData', 'dateFrom', 'dateTo', 'segment'));
    }

    /**
     * GST reports
     */
    public function gst(Request $request)
    {
        $dateFrom = $request->date_from ?? now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->endOfMonth()->format('Y-m-d');
        $locationId = $request->location_id;

        $gstData = $this->reportService->getGSTReport([
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'location_id' => $locationId
        ]);

        $locations = Location::active()->get();

        return view('admin.reports.gst', compact('gstData', 'locations', 'dateFrom', 'dateTo', 'locationId'));
    }

    /**
     * Profit analysis
     */
    public function profit(Request $request)
    {
        $dateFrom = $request->date_from ?? now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->endOfMonth()->format('Y-m-d');
        $locationId = $request->location_id;
        $categoryId = $request->category_id;

        $profitData = $this->reportService->getProfitAnalysis([
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'location_id' => $locationId,
            'category_id' => $categoryId
        ]);

        $locations = Location::active()->get();
        $categories = \App\Models\Category::active()->get();

        return view('admin.reports.profit', compact('profitData', 'locations', 'categories', 'dateFrom', 'dateTo', 'locationId', 'categoryId'));
    }

    /**
     * Estimate analytics
     */
    public function estimates(Request $request)
    {
        $dateFrom = $request->date_from ?? now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->endOfMonth()->format('Y-m-d');
        $locationId = $request->location_id;

        $estimateData = $this->reportService->getEstimateAnalytics([
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'location_id' => $locationId
        ]);

        $locations = Location::active()->get();

        return view('admin.reports.estimates', compact('estimateData', 'locations', 'dateFrom', 'dateTo', 'locationId'));
    }

    /**
     * Product performance
     */
    public function products(Request $request)
    {
        $dateFrom = $request->date_from ?? now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->endOfMonth()->format('Y-m-d');
        $locationId = $request->location_id;
        $categoryId = $request->category_id;
        $sortBy = $request->sort_by ?? 'revenue';

        $productData = $this->reportService->getProductPerformance([
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'location_id' => $locationId,
            'category_id' => $categoryId,
            'sort_by' => $sortBy
        ]);

        $locations = Location::active()->get();
        $categories = \App\Models\Category::active()->get();

        return view('admin.reports.products', compact('productData', 'locations', 'categories', 'dateFrom', 'dateTo', 'locationId', 'categoryId', 'sortBy'));
    }

    /**
     * Export sales report
     */
    public function exportSales(Request $request)
    {
        $filters = $request->only(['date_from', 'date_to', 'location_id', 'group_by']);

        return $this->reportService->exportSalesReport($filters);
    }

    /**
     * Export inventory report
     */
    public function exportInventory(Request $request)
    {
        $filters = $request->only(['location_id', 'category_id', 'low_stock']);

        return $this->reportService->exportInventoryReport($filters);
    }

    /**
     * Export GST report
     */
    public function exportGST(Request $request)
    {
        $filters = $request->only(['date_from', 'date_to', 'location_id']);

        return $this->reportService->exportGSTReport($filters);
    }

    /**
     * Get chart data for AJAX requests
     */
    public function getChartData(Request $request)
    {
        $type = $request->type;
        $filters = $request->only(['date_from', 'date_to', 'location_id', 'category_id', 'group_by']);

        switch ($type) {
            case 'sales_trend':
                $data = $this->reportService->getSalesTrendData($filters);
                break;
            case 'category_sales':
                $data = $this->reportService->getCategorySalesData($filters);
                break;
            case 'customer_segments':
                $data = $this->reportService->getCustomerSegmentData($filters);
                break;
            case 'inventory_value':
                $data = $this->reportService->getInventoryValueData($filters);
                break;
            case 'profit_trend':
                $data = $this->reportService->getProfitTrendData($filters);
                break;
            default:
                $data = [];
        }

        return response()->json($data);
    }

    /**
     * Get real-time dashboard data
     */
    public function getDashboardData(Request $request)
    {
        $period = $request->period ?? 'today';

        $data = $this->reportService->getRealTimeDashboardData($period);

        return response()->json($data);
    }
}
