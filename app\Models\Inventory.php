<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Inventory extends Model
{
    protected $table = 'inventory';

    protected $fillable = [
        'product_id',
        'location_id',
        'quantity_available',
        'quantity_reserved',
        'cost_price',
        'selling_price',
        'mrp',
        'minimum_stock_level',
        'maximum_stock_level',
        'reorder_point',
        'last_stock_update',
        'notes',
    ];

    protected $casts = [
        'quantity_available' => 'integer',
        'quantity_reserved' => 'integer',
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'mrp' => 'decimal:2',
        'minimum_stock_level' => 'integer',
        'maximum_stock_level' => 'integer',
        'reorder_point' => 'integer',
        'last_stock_update' => 'datetime',
    ];

    // Relationships
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    // Scopes
    public function scopeLowStock($query)
    {
        return $query->whereRaw('quantity_available <= minimum_stock_level');
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('quantity_available', 0);
    }

    public function scopeInStock($query)
    {
        return $query->where('quantity_available', '>', 0);
    }

    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    // Accessors
    public function getIsLowStockAttribute()
    {
        return $this->quantity_available <= $this->minimum_stock_level;
    }

    public function getIsOutOfStockAttribute()
    {
        return $this->quantity_available == 0;
    }

    public function getStockStatusAttribute()
    {
        if ($this->is_out_of_stock) {
            return 'out_of_stock';
        } elseif ($this->is_low_stock) {
            return 'low_stock';
        } else {
            return 'in_stock';
        }
    }

    public function getStockStatusDisplayAttribute()
    {
        $statuses = [
            'out_of_stock' => 'Out of Stock',
            'low_stock' => 'Low Stock',
            'in_stock' => 'In Stock',
        ];

        return $statuses[$this->stock_status] ?? 'Unknown';
    }

    public function getStockValueAttribute()
    {
        return $this->quantity_available * $this->cost_price;
    }

    public function getAvailableQuantityAttribute()
    {
        return max(0, $this->quantity_available - $this->quantity_reserved);
    }

    // Methods
    public function adjustStock($quantity, $type = 'adjustment', $notes = null, $userId = null)
    {
        $oldQuantity = $this->quantity_available;
        $this->quantity_available = max(0, $this->quantity_available + $quantity);
        $this->last_stock_update = now();
        $this->save();

        // Create stock movement record
        $this->stockMovements()->create([
            'movement_type' => $quantity > 0 ? 'stock_in' : 'stock_out',
            'quantity' => abs($quantity),
            'reference_type' => $type,
            'notes' => $notes,
            'created_by' => $userId ?? auth()->id(),
        ]);

        return $this;
    }

    public function reserveStock($quantity)
    {
        if ($this->available_quantity >= $quantity) {
            $this->quantity_reserved += $quantity;
            $this->save();
            return true;
        }
        return false;
    }

    public function releaseReservedStock($quantity)
    {
        $this->quantity_reserved = max(0, $this->quantity_reserved - $quantity);
        $this->save();
        return $this;
    }
}
