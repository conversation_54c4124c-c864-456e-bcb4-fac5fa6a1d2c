<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class CustomerNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'type',
        'title',
        'message',
        'related_type',
        'related_id',
        'data',
        'sent_at',
        'read_at',
        'delivery_status',
        'delivery_method',
        'delivery_response',
    ];

    protected $casts = [
        'data' => 'array',
        'sent_at' => 'datetime',
        'read_at' => 'datetime',
        'delivery_response' => 'array',
    ];

    /**
     * Relationships
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function related(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scopes
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeSent($query)
    {
        return $query->whereNotNull('sent_at');
    }

    public function scopePending($query)
    {
        return $query->whereNull('sent_at');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Accessors
     */
    public function getIsReadAttribute()
    {
        return !is_null($this->read_at);
    }

    public function getIsSentAttribute()
    {
        return !is_null($this->sent_at);
    }

    public function getDeliveryStatusDisplayAttribute()
    {
        $statuses = [
            'pending' => 'Pending',
            'sent' => 'Sent',
            'delivered' => 'Delivered',
            'failed' => 'Failed',
            'read' => 'Read',
        ];

        return $statuses[$this->delivery_status] ?? ucfirst($this->delivery_status);
    }

    public function getDeliveryStatusColorAttribute()
    {
        $colors = [
            'pending' => 'yellow',
            'sent' => 'blue',
            'delivered' => 'green',
            'failed' => 'red',
            'read' => 'purple',
        ];

        return $colors[$this->delivery_status] ?? 'gray';
    }

    public function getDeliveryMethodDisplayAttribute()
    {
        $methods = [
            'sms' => 'SMS',
            'email' => 'Email',
            'whatsapp' => 'WhatsApp',
            'push' => 'Push Notification',
            'in_app' => 'In-App',
        ];

        return $methods[$this->delivery_method] ?? ucfirst($this->delivery_method);
    }

    /**
     * Business Logic Methods
     */
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->update([
                'read_at' => now(),
                'delivery_status' => 'read',
            ]);
        }

        return $this;
    }

    public function markAsSent($deliveryMethod = null, $response = null)
    {
        $this->update([
            'sent_at' => now(),
            'delivery_status' => 'sent',
            'delivery_method' => $deliveryMethod,
            'delivery_response' => $response,
        ]);

        return $this;
    }

    public function markAsDelivered($response = null)
    {
        $this->update([
            'delivery_status' => 'delivered',
            'delivery_response' => array_merge($this->delivery_response ?: [], $response ?: []),
        ]);

        return $this;
    }

    public function markAsFailed($error = null)
    {
        $this->update([
            'delivery_status' => 'failed',
            'delivery_response' => array_merge($this->delivery_response ?: [], ['error' => $error]),
        ]);

        return $this;
    }

    public function getSummary()
    {
        return [
            'id' => $this->id,
            'customer_name' => $this->customer->name,
            'type' => $this->type,
            'title' => $this->title,
            'message' => $this->message,
            'delivery_status' => $this->delivery_status_display,
            'delivery_method' => $this->delivery_method_display,
            'is_read' => $this->is_read,
            'is_sent' => $this->is_sent,
            'sent_at' => $this->sent_at?->format('Y-m-d H:i:s'),
            'read_at' => $this->read_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
