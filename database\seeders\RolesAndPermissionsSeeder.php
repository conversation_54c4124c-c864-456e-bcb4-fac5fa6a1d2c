<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Role;
use <PERSON>tie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create Permissions
        $permissions = [
            // Dashboard
            'view_dashboard',
            
            // Products
            'view_products', 'create_products', 'edit_products', 'delete_products', 'manage_products',
            
            // Categories
            'view_categories', 'manage_categories',
            
            // Customers
            'view_customers', 'create_customers', 'edit_customers', 'delete_customers', 'manage_customers',
            
            // Inventory
            'view_inventory', 'manage_inventory', 'adjust_stock', 'manage_inventory_items',
            
            // Sales & Invoices
            'view_invoices', 'create_invoices', 'edit_invoices', 'delete_invoices',
            'process_sales', 'refund_sales', 'manage_sales', 'view_sales',
            
            // Estimates
            'view_estimates', 'create_estimates', 'edit_estimates', 'delete_estimates',
            'convert_estimates', 'manage_estimates',
            
            // Metal Rates
            'view_metal_rates', 'edit_metal_rates', 'manage_metals',
            
            // Old Gold
            'view_old_gold', 'manage_old_gold', 'process_old_gold',
            
            // Repair Services
            'view_repair_services', 'manage_repair_services', 'update_repair_status',
            
            // Schemes
            'view_saving_schemes', 'manage_saving_schemes', 'process_scheme_payments',
            
            // Reports
            'view_reports', 'export_reports', 'view_financial_reports',
            
            // CRM
            'view_crm', 'manage_customer_interactions', 'manage_loyalty_program',
            'manage_marketing_campaigns',
            
            // Financial
            'manage_chart_of_accounts', 'manage_journal_entries', 'view_financial_analytics',
            
            // POS
            'access_pos', 'manage_pos_sessions',
            
            // Users & Roles
            'manage_users', 'manage_roles', 'assign_permissions',
            
            // System
            'system_settings', 'manage_locations', 'view_audit_logs',
            'manage_backups', 'manage_integrations',
            
            // HSN & Stones
            'view_hsn', 'manage_hsn', 'view_stones', 'manage_stones',
            'manage_making_charges',
            
            // Suppliers
            'view_suppliers', 'manage_suppliers', 'manage_purchase_orders',
            
            // Expenses
            'view_expenses', 'manage_expenses', 'approve_expenses',

            // Analytics & Business Intelligence
            'view_analytics', 'view_business_intelligence', 'export_analytics', 'view_reports',

            // Security Management
            'view_security_dashboard', 'manage_security_settings', 'view_security_logs',
            'manage_ip_blocking', 'manage_user_access', 'security_management',

            // System Administration
            'system_administration', 'view_system_logs', 'manage_system_health',
            'manage_database', 'manage_cache', 'manage_queues',

            // Barcode Management
            'generate_barcodes', 'print_barcodes', 'manage_barcode_settings',

            // Testing & Quality Assurance
            'view_testing_dashboard', 'manage_test_cases', 'run_tests',
            'view_test_results', 'manage_bug_reports',

            // Dashboard Access
            'view_dashboard_analytics', 'view_inventory_dashboard', 'view_metal_rate_dashboard',

            // Purchase Orders & Goods Receipt
            'view_purchase_orders', 'create_purchase_orders', 'edit_purchase_orders', 'delete_purchase_orders',
            'view_goods_receipt', 'create_goods_receipt', 'edit_goods_receipt', 'delete_goods_receipt',

            // Supplier Management (if missing)
            'create_suppliers', 'edit_suppliers', 'delete_suppliers',

            // Repair Services (if missing)
            'create_repair_services', 'edit_repair_services', 'delete_repair_services',

            // Saving Scheme Plans
            'view_saving_scheme_plans', 'manage_saving_scheme_plans',

            // POS Sessions
            'view_pos_sessions',

            // Customer Interactions
            'view_customer_interactions',

            // Additional Security Permissions
            'manage_security_policies', 'view_security_audit',

            // Additional Testing Permissions
            'run_performance_tests', 'run_security_tests', 'view_code_quality',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create Roles with Permissions
        $roles = [
            'SuperAdmin' => $permissions, // All permissions
            
            'Manager' => [
                'view_dashboard', 'view_products', 'create_products', 'edit_products',
                'view_categories', 'manage_categories', 'view_customers', 'create_customers',
                'edit_customers', 'view_inventory', 'manage_inventory', 'adjust_stock',
                'view_invoices', 'create_invoices', 'edit_invoices', 'process_sales',
                'view_estimates', 'create_estimates', 'edit_estimates', 'convert_estimates',
                'view_metal_rates', 'edit_metal_rates', 'view_old_gold', 'manage_old_gold',
                'view_repair_services', 'manage_repair_services', 'update_repair_status',
                'create_repair_services', 'edit_repair_services', 'delete_repair_services',
                'view_saving_schemes', 'manage_saving_schemes', 'process_scheme_payments',
                'view_saving_scheme_plans', 'manage_saving_scheme_plans',
                'view_reports', 'export_reports', 'view_crm', 'manage_customer_interactions',
                'view_customer_interactions', 'access_pos', 'manage_pos_sessions', 'view_pos_sessions',
                'view_suppliers', 'manage_suppliers', 'create_suppliers', 'edit_suppliers', 'delete_suppliers',
                'view_purchase_orders', 'create_purchase_orders', 'edit_purchase_orders', 'delete_purchase_orders',
                'view_goods_receipt', 'create_goods_receipt', 'edit_goods_receipt', 'delete_goods_receipt',
                'view_expenses', 'manage_expenses', 'approve_expenses', 'view_hsn', 'manage_hsn',
                'view_stones', 'manage_stones', 'manage_making_charges',
                'view_analytics', 'view_business_intelligence', 'export_analytics',
                'view_dashboard_analytics', 'view_inventory_dashboard', 'view_metal_rate_dashboard',
                'generate_barcodes', 'print_barcodes', 'manage_barcode_settings',
            ],
            
            'Cashier' => [
                'view_dashboard', 'view_products', 'view_customers', 'create_customers',
                'edit_customers', 'view_inventory', 'view_invoices', 'create_invoices',
                'process_sales', 'view_estimates', 'create_estimates', 'view_metal_rates',
                'view_old_gold', 'manage_old_gold', 'access_pos', 'manage_pos_sessions',
                'view_pos_sessions', 'view_saving_schemes', 'process_scheme_payments',
                'view_dashboard_analytics', 'generate_barcodes', 'print_barcodes',
            ],
            
            'SalesStaff' => [
                'view_dashboard', 'view_products', 'view_customers', 'create_customers',
                'edit_customers', 'view_inventory', 'view_invoices', 'create_invoices',
                'process_sales', 'view_estimates', 'create_estimates', 'convert_estimates',
                'view_metal_rates', 'view_old_gold', 'manage_old_gold', 'view_crm',
                'manage_customer_interactions', 'view_customer_interactions', 'access_pos',
                'manage_pos_sessions', 'view_pos_sessions', 'view_saving_schemes',
                'process_scheme_payments', 'view_dashboard_analytics',
                'generate_barcodes', 'print_barcodes',
            ],
            
            'Accountant' => [
                'view_dashboard', 'view_invoices', 'view_estimates', 'view_reports',
                'export_reports', 'view_financial_reports', 'manage_chart_of_accounts',
                'manage_journal_entries', 'view_financial_analytics', 'view_expenses',
                'manage_expenses', 'approve_expenses', 'view_suppliers', 'manage_suppliers',
                'create_suppliers', 'edit_suppliers', 'delete_suppliers',
                'view_purchase_orders', 'create_purchase_orders', 'edit_purchase_orders', 'delete_purchase_orders',
                'view_goods_receipt', 'create_goods_receipt', 'edit_goods_receipt', 'delete_goods_receipt',
                'view_dashboard_analytics', 'view_analytics', 'export_analytics',
            ],
            
            'Customer' => [
                'view_products', 'view_categories', 'view_metal_rates',
            ],
        ];

        foreach ($roles as $roleName => $rolePermissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            $role->syncPermissions($rolePermissions);
        }

        // Update existing admin user with SuperAdmin role
        $adminUser = User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            $adminUser->assignRole('SuperAdmin');
        }

        // Create additional test users if they don't exist
        $testUsers = [
            [
                'name' => 'Store Manager',
                'email' => '<EMAIL>',
                'role' => 'Manager'
            ],
            [
                'name' => 'Cashier User',
                'email' => '<EMAIL>',
                'role' => 'Cashier'
            ],
            [
                'name' => 'Sales Staff',
                'email' => '<EMAIL>',
                'role' => 'SalesStaff'
            ],
            [
                'name' => 'Accountant User',
                'email' => '<EMAIL>',
                'role' => 'Accountant'
            ],
        ];

        foreach ($testUsers as $userData) {
            $user = User::firstOrCreate([
                'email' => $userData['email']
            ], [
                'name' => $userData['name'],
                'password' => Hash::make('password123'),
                'location_id' => 1, // Assuming main location has ID 1
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $user->assignRole($userData['role']);
        }

        $this->command->info('Roles and permissions seeder completed successfully!');
        $this->command->info('Roles created: SuperAdmin, Manager, Cashier, SalesStaff, Accountant, Customer');
        $this->command->info('Test users created with password: password123');
    }
}
