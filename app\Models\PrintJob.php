<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class PrintJob extends Model
{
    use HasFactory;

    protected $fillable = [
        'job_id',
        'printer_configuration_id',
        'user_id',
        'print_type',
        'document_type',
        'template_name',
        'related_model_type',
        'related_model_id',
        'related_reference_number',
        'print_data',
        'raw_content',
        'copies',
        'print_settings',
        'paper_width',
        'auto_cut',
        'print_density',
        'status',
        'error_message',
        'retry_count',
        'max_retries',
        'queued_at',
        'started_at',
        'completed_at',
        'failed_at',
        'processing_time_ms',
        'print_verified',
        'verification_notes',
        'verified_by',
        'verified_at',
        'preview_file_path',
        'backup_file_path',
        'file_size_bytes',
        'print_analytics',
        'cost_estimate',
        'pages_printed',
    ];

    protected $casts = [
        'print_settings' => 'array',
        'auto_cut' => 'boolean',
        'queued_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'failed_at' => 'datetime',
        'print_verified' => 'boolean',
        'verified_at' => 'datetime',
        'print_analytics' => 'array',
        'cost_estimate' => 'decimal:4',
    ];

    /**
     * Relationships
     */
    public function printerConfiguration(): BelongsTo
    {
        return $this->belongsTo(PrinterConfiguration::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Get the related model (polymorphic relationship)
     */
    public function relatedModel()
    {
        if ($this->related_model_type && $this->related_model_id) {
            return $this->related_model_type::find($this->related_model_id);
        }
        return null;
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeByPrintType($query, $printType)
    {
        return $query->where('print_type', $printType);
    }

    public function scopeByPrinter($query, $printerId)
    {
        return $query->where('printer_configuration_id', $printerId);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    /**
     * Accessors
     */
    public function getPrintTypeDisplayAttribute()
    {
        $types = [
            'invoice' => 'Invoice',
            'receipt' => 'Receipt',
            'estimate' => 'Estimate',
            'tag' => 'Inventory Tag',
            'label' => 'Label',
            'barcode' => 'Barcode',
            'qr_code' => 'QR Code',
            'report' => 'Report',
            'custom' => 'Custom',
        ];

        return $types[$this->print_type] ?? ucfirst(str_replace('_', ' ', $this->print_type));
    }

    public function getStatusDisplayAttribute()
    {
        return ucfirst($this->status);
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'yellow',
            'processing' => 'blue',
            'completed' => 'green',
            'failed' => 'red',
            'cancelled' => 'gray',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getDurationDisplayAttribute()
    {
        if ($this->status === 'completed' && $this->processing_time_ms) {
            if ($this->processing_time_ms < 1000) {
                return $this->processing_time_ms . 'ms';
            } else {
                return round($this->processing_time_ms / 1000, 2) . 's';
            }
        }

        if ($this->started_at) {
            $endTime = $this->completed_at ?: $this->failed_at ?: now();
            return $this->started_at->diffInSeconds($endTime) . 's';
        }

        return 'N/A';
    }

    public function getWaitTimeDisplayAttribute()
    {
        if ($this->started_at && $this->queued_at) {
            return $this->queued_at->diffInSeconds($this->started_at) . 's';
        }

        if ($this->queued_at && $this->status === 'pending') {
            return $this->queued_at->diffInSeconds(now()) . 's';
        }

        return 'N/A';
    }

    public function getIsOverdueAttribute()
    {
        if ($this->status !== 'pending') {
            return false;
        }

        // Consider job overdue if pending for more than 5 minutes
        return $this->queued_at && $this->queued_at < now()->subMinutes(5);
    }

    public function getCanRetryAttribute()
    {
        return $this->status === 'failed' && $this->retry_count < $this->max_retries;
    }

    public function getCanCancelAttribute()
    {
        return in_array($this->status, ['pending', 'processing']);
    }

    public function getFileSizeDisplayAttribute()
    {
        if (!$this->file_size_bytes) {
            return 'N/A';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = $this->file_size_bytes;
        $factor = floor((strlen($bytes) - 1) / 3);

        return sprintf("%.2f", $bytes / pow(1024, $factor)) . ' ' . $units[$factor];
    }

    public function getRelatedModelDisplayAttribute()
    {
        if (!$this->related_model_type || !$this->related_model_id) {
            return 'N/A';
        }

        $model = $this->relatedModel();
        if (!$model) {
            return 'Deleted';
        }

        switch ($this->related_model_type) {
            case 'App\Models\Invoice':
                return 'Invoice #' . $model->invoice_number;
            case 'App\Models\Estimate':
                return 'Estimate #' . $model->estimate_number;
            case 'App\Models\InventoryItem':
                return 'Item #' . $model->unique_tag;
            default:
                return class_basename($this->related_model_type) . ' #' . $this->related_model_id;
        }
    }

    /**
     * Mark job as started
     */
    public function markAsStarted()
    {
        $this->update([
            'status' => 'processing',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark job as completed
     */
    public function markAsCompleted($processingTimeMs = null)
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'processing_time_ms' => $processingTimeMs,
        ]);

        // Update printer statistics
        $this->printerConfiguration->recordPrintJob(true);
    }

    /**
     * Mark job as failed
     */
    public function markAsFailed($errorMessage = null)
    {
        $this->update([
            'status' => 'failed',
            'failed_at' => now(),
            'error_message' => $errorMessage,
            'retry_count' => $this->retry_count + 1,
        ]);

        // Update printer statistics
        $this->printerConfiguration->recordPrintJob(false);
    }

    /**
     * Mark job as cancelled
     */
    public function markAsCancelled()
    {
        $this->update([
            'status' => 'cancelled',
            'completed_at' => now(),
        ]);
    }

    /**
     * Retry the job
     */
    public function retry()
    {
        if (!$this->can_retry) {
            return false;
        }

        $this->update([
            'status' => 'pending',
            'queued_at' => now(),
            'started_at' => null,
            'completed_at' => null,
            'failed_at' => null,
            'error_message' => null,
        ]);

        return true;
    }

    /**
     * Verify print job
     */
    public function verify($notes = null)
    {
        $this->update([
            'print_verified' => true,
            'verification_notes' => $notes,
            'verified_by' => auth()->id(),
            'verified_at' => now(),
        ]);
    }

    /**
     * Get job summary
     */
    public function getSummary()
    {
        return [
            'job_id' => $this->job_id,
            'print_type' => $this->print_type_display,
            'status' => $this->status_display,
            'copies' => $this->copies,
            'printer' => $this->printerConfiguration->name,
            'user' => $this->user->name,
            'related_document' => $this->related_model_display,
            'queued_at' => $this->queued_at?->format('Y-m-d H:i:s'),
            'completed_at' => $this->completed_at?->format('Y-m-d H:i:s'),
            'duration' => $this->duration_display,
            'wait_time' => $this->wait_time_display,
            'error_message' => $this->error_message,
            'is_overdue' => $this->is_overdue,
            'can_retry' => $this->can_retry,
            'can_cancel' => $this->can_cancel,
            'verified' => $this->print_verified,
        ];
    }

    /**
     * Get analytics data
     */
    public function getAnalytics()
    {
        return [
            'processing_time_ms' => $this->processing_time_ms,
            'wait_time_seconds' => $this->queued_at && $this->started_at ?
                $this->queued_at->diffInSeconds($this->started_at) : null,
            'total_time_seconds' => $this->queued_at && $this->completed_at ?
                $this->queued_at->diffInSeconds($this->completed_at) : null,
            'retry_count' => $this->retry_count,
            'file_size_bytes' => $this->file_size_bytes,
            'pages_printed' => $this->pages_printed,
            'cost_estimate' => $this->cost_estimate,
            'print_analytics' => $this->print_analytics,
        ];
    }
}
