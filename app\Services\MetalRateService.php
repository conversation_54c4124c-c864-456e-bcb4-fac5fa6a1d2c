<?php

namespace App\Services;

use App\Models\MetalRate;
use App\Models\Metal;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class MetalRateService
{
    /**
     * Get current rates for all metals
     */
    public function getCurrentRates()
    {
        return Cache::remember('metal_rates_current', 300, function () {
            $rates = [];
            
            $metals = Metal::active()->get();
            
            foreach ($metals as $metal) {
                $latestRate = MetalRate::where('metal_id', $metal->id)
                    ->where('effective_date', '<=', now())
                    ->orderBy('effective_date', 'desc')
                    ->first();
                
                $rates[$metal->id] = [
                    'metal' => $metal,
                    'rate' => $latestRate ? $latestRate->rate_per_gram : 0,
                    'last_updated' => $latestRate ? $latestRate->effective_date : null,
                    'change_percentage' => $this->calculateChangePercentage($metal->id),
                ];
            }
            
            return $rates;
        });
    }

    /**
     * Get current rate for specific metal
     */
    public function getCurrentRate($metalId)
    {
        $rates = $this->getCurrentRates();
        
        return $rates[$metalId]['rate'] ?? 0;
    }

    /**
     * Update metal rate
     */
    public function updateRate($metalId, $rate, $effectiveDate = null, $source = 'manual')
    {
        $effectiveDate = $effectiveDate ?? now();
        
        $metalRate = MetalRate::create([
            'metal_id' => $metalId,
            'rate_per_gram' => $rate,
            'effective_date' => $effectiveDate,
            'source' => $source,
            'created_by' => auth()->id(),
        ]);

        // Clear cache
        Cache::forget('metal_rates_current');
        Cache::forget("metal_rate_history_{$metalId}");

        return $metalRate;
    }

    /**
     * Get rate history for a metal
     */
    public function getRateHistory($metalId, $days = 30)
    {
        return Cache::remember("metal_rate_history_{$metalId}_{$days}", 3600, function () use ($metalId, $days) {
            return MetalRate::where('metal_id', $metalId)
                ->where('effective_date', '>=', now()->subDays($days))
                ->orderBy('effective_date', 'desc')
                ->get();
        });
    }

    /**
     * Calculate change percentage from previous rate
     */
    public function calculateChangePercentage($metalId)
    {
        $rates = MetalRate::where('metal_id', $metalId)
            ->where('effective_date', '<=', now())
            ->orderBy('effective_date', 'desc')
            ->limit(2)
            ->get();

        if ($rates->count() < 2) {
            return 0;
        }

        $currentRate = $rates->first()->rate_per_gram;
        $previousRate = $rates->last()->rate_per_gram;

        if ($previousRate == 0) {
            return 0;
        }

        return (($currentRate - $previousRate) / $previousRate) * 100;
    }

    /**
     * Fetch rates from external API (example implementation)
     */
    public function fetchExternalRates()
    {
        try {
            // Example API call - replace with actual metal rates API
            $response = Http::timeout(10)->get('https://api.metals.live/v1/spot');
            
            if ($response->successful()) {
                $data = $response->json();
                
                // Process and update rates based on API response
                // This is a placeholder implementation
                $this->processExternalRates($data);
                
                return true;
            }
        } catch (\Exception $e) {
            \Log::error('Failed to fetch external metal rates: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Process external rates data
     */
    private function processExternalRates($data)
    {
        // Map external API data to our metals
        $metalMapping = [
            'gold' => 'Gold',
            'silver' => 'Silver',
            'platinum' => 'Platinum',
        ];

        foreach ($metalMapping as $apiKey => $metalName) {
            if (isset($data[$apiKey])) {
                $metal = Metal::where('name', $metalName)->first();
                
                if ($metal) {
                    $rate = $data[$apiKey]['price'] ?? 0;
                    
                    // Convert from USD per ounce to INR per gram (example conversion)
                    $ratePerGram = $this->convertToInrPerGram($rate);
                    
                    $this->updateRate($metal->id, $ratePerGram, now(), 'api');
                }
            }
        }
    }

    /**
     * Convert USD per ounce to INR per gram
     */
    private function convertToInrPerGram($usdPerOunce)
    {
        $usdToInr = 83; // Current exchange rate (should be fetched from API)
        $gramsPerOunce = 31.1035;
        
        return ($usdPerOunce * $usdToInr) / $gramsPerOunce;
    }

    /**
     * Get rate for specific date
     */
    public function getRateForDate($metalId, $date)
    {
        $rate = MetalRate::where('metal_id', $metalId)
            ->where('effective_date', '<=', $date)
            ->orderBy('effective_date', 'desc')
            ->first();

        return $rate ? $rate->rate_per_gram : 0;
    }

    /**
     * Calculate price for product based on current rates
     */
    public function calculateProductPrice(Product $product, $makingChargesPerGram = null, $stoneCharges = null)
    {
        $metalRate = $this->getCurrentRate($product->metal_id);
        $weight = $product->net_weight ?? $product->gross_weight ?? 0;
        
        $metalValue = $weight * $metalRate;
        
        $makingCharges = 0;
        if ($makingChargesPerGram) {
            $makingCharges = $weight * $makingChargesPerGram;
        } elseif ($product->making_charges_per_gram) {
            $makingCharges = $weight * $product->making_charges_per_gram;
        }
        
        $stoneCharges = $stoneCharges ?? $product->stone_charges ?? 0;
        
        return [
            'metal_rate' => $metalRate,
            'metal_value' => $metalValue,
            'making_charges' => $makingCharges,
            'stone_charges' => $stoneCharges,
            'total_price' => $metalValue + $makingCharges + $stoneCharges,
        ];
    }

    /**
     * Get rate trends for dashboard
     */
    public function getRateTrends($days = 7)
    {
        $trends = [];
        $metals = Metal::active()->get();
        
        foreach ($metals as $metal) {
            $rates = $this->getRateHistory($metal->id, $days);
            
            $trends[$metal->id] = [
                'metal' => $metal,
                'current_rate' => $this->getCurrentRate($metal->id),
                'change_percentage' => $this->calculateChangePercentage($metal->id),
                'history' => $rates->map(function ($rate) {
                    return [
                        'date' => $rate->effective_date->format('Y-m-d'),
                        'rate' => $rate->rate_per_gram,
                    ];
                }),
            ];
        }
        
        return $trends;
    }

    /**
     * Update all rates from external source
     */
    public function updateAllRatesFromExternal()
    {
        $success = $this->fetchExternalRates();
        
        if ($success) {
            // Clear all rate caches
            Cache::forget('metal_rates_current');
            
            $metals = Metal::active()->get();
            foreach ($metals as $metal) {
                Cache::forget("metal_rate_history_{$metal->id}");
            }
        }
        
        return $success;
    }

    /**
     * Get rate alerts (for rates that changed significantly)
     */
    public function getRateAlerts($threshold = 5)
    {
        $alerts = [];
        $rates = $this->getCurrentRates();
        
        foreach ($rates as $metalId => $rateData) {
            $changePercentage = abs($rateData['change_percentage']);
            
            if ($changePercentage >= $threshold) {
                $alerts[] = [
                    'metal' => $rateData['metal'],
                    'current_rate' => $rateData['rate'],
                    'change_percentage' => $rateData['change_percentage'],
                    'alert_type' => $rateData['change_percentage'] > 0 ? 'increase' : 'decrease',
                ];
            }
        }
        
        return $alerts;
    }

    /**
     * Schedule automatic rate updates
     */
    public function scheduleRateUpdate()
    {
        // This would be called by a scheduled job
        return $this->updateAllRatesFromExternal();
    }

    /**
     * Get rate comparison between dates
     */
    public function getRateComparison($metalId, $startDate, $endDate)
    {
        $startRate = $this->getRateForDate($metalId, $startDate);
        $endRate = $this->getRateForDate($metalId, $endDate);

        $change = $endRate - $startRate;
        $changePercentage = $startRate > 0 ? ($change / $startRate) * 100 : 0;

        return [
            'start_rate' => $startRate,
            'end_rate' => $endRate,
            'change' => $change,
            'change_percentage' => $changePercentage,
        ];
    }

    /**
     * Calculate making charges based on metal weight and rate
     */
    public function calculateMakingCharges($metalId, $weight, $makingChargePercentage = null, $fixedMakingCharge = null)
    {
        $currentRate = $this->getCurrentRate($metalId);
        $metalValue = $weight * $currentRate;

        $makingCharges = 0;

        if ($makingChargePercentage) {
            $makingCharges += $metalValue * ($makingChargePercentage / 100);
        }

        if ($fixedMakingCharge) {
            $makingCharges += $fixedMakingCharge;
        }

        return [
            'metal_value' => $metalValue,
            'making_charges' => $makingCharges,
            'total_value' => $metalValue + $makingCharges,
            'rate_used' => $currentRate,
            'calculated_at' => now(),
        ];
    }

    /**
     * Calculate wastage charges
     */
    public function calculateWastageCharges($metalId, $grossWeight, $netWeight, $wastagePercentage = null)
    {
        $currentRate = $this->getCurrentRate($metalId);

        // Calculate wastage weight
        $wastageWeight = $wastagePercentage ?
            ($netWeight * $wastagePercentage / 100) :
            ($grossWeight - $netWeight);

        $wastageValue = $wastageWeight * $currentRate;

        return [
            'gross_weight' => $grossWeight,
            'net_weight' => $netWeight,
            'wastage_weight' => $wastageWeight,
            'wastage_percentage' => $wastagePercentage ?? (($wastageWeight / $netWeight) * 100),
            'wastage_value' => $wastageValue,
            'rate_used' => $currentRate,
            'calculated_at' => now(),
        ];
    }

    /**
     * Get rate volatility analysis
     */
    public function getRateVolatility($metalId, $days = 30)
    {
        $rates = $this->getRateHistory($metalId, $days);

        if ($rates->count() < 2) {
            return [
                'volatility' => 0,
                'standard_deviation' => 0,
                'min_rate' => 0,
                'max_rate' => 0,
                'avg_rate' => 0,
            ];
        }

        $rateValues = $rates->pluck('rate_per_gram')->toArray();
        $avgRate = array_sum($rateValues) / count($rateValues);

        // Calculate standard deviation
        $variance = array_sum(array_map(function($rate) use ($avgRate) {
            return pow($rate - $avgRate, 2);
        }, $rateValues)) / count($rateValues);

        $standardDeviation = sqrt($variance);
        $volatility = ($standardDeviation / $avgRate) * 100;

        return [
            'volatility' => round($volatility, 2),
            'standard_deviation' => round($standardDeviation, 2),
            'min_rate' => min($rateValues),
            'max_rate' => max($rateValues),
            'avg_rate' => round($avgRate, 2),
            'rate_range' => max($rateValues) - min($rateValues),
        ];
    }

    /**
     * Get making charge recommendations based on metal and market conditions
     */
    public function getMakingChargeRecommendations($metalId, $productType = null)
    {
        $currentRate = $this->getCurrentRate($metalId);
        $volatility = $this->getRateVolatility($metalId, 30);
        $metal = Metal::find($metalId);

        // Base making charge percentages by metal type
        $baseCharges = [
            'Gold' => ['min' => 8, 'max' => 15, 'recommended' => 12],
            'Silver' => ['min' => 15, 'max' => 25, 'recommended' => 20],
            'Platinum' => ['min' => 10, 'max' => 18, 'recommended' => 14],
        ];

        $metalName = $metal->name ?? 'Gold';
        $charges = $baseCharges[$metalName] ?? $baseCharges['Gold'];

        // Adjust for volatility
        if ($volatility['volatility'] > 10) {
            $charges['recommended'] += 2; // Add buffer for high volatility
        }

        // Adjust for product type
        $productTypeAdjustments = [
            'ring' => 0,
            'necklace' => 2,
            'bracelet' => 1,
            'earrings' => 1,
            'pendant' => 1,
            'chain' => 3,
        ];

        if ($productType && isset($productTypeAdjustments[$productType])) {
            $charges['recommended'] += $productTypeAdjustments[$productType];
        }

        return [
            'metal' => $metal,
            'current_rate' => $currentRate,
            'volatility' => $volatility['volatility'],
            'making_charge_percentage' => [
                'min' => $charges['min'],
                'max' => $charges['max'],
                'recommended' => min($charges['max'], $charges['recommended']),
            ],
            'market_conditions' => $this->getMarketConditions($volatility['volatility']),
            'recommendations' => $this->generateMakingChargeRecommendations($charges, $volatility['volatility']),
        ];
    }

    /**
     * Get market conditions assessment
     */
    private function getMarketConditions($volatility)
    {
        if ($volatility > 15) {
            return ['status' => 'highly_volatile', 'description' => 'Market is highly volatile, consider higher making charges'];
        } elseif ($volatility > 10) {
            return ['status' => 'volatile', 'description' => 'Market is volatile, add volatility buffer'];
        } elseif ($volatility > 5) {
            return ['status' => 'moderate', 'description' => 'Market conditions are moderate'];
        } else {
            return ['status' => 'stable', 'description' => 'Market is stable, standard charges apply'];
        }
    }

    /**
     * Generate making charge recommendations
     */
    private function generateMakingChargeRecommendations($charges, $volatility)
    {
        $recommendations = [];

        if ($volatility > 10) {
            $recommendations[] = 'Consider adding 2-3% volatility buffer to making charges';
        }

        if ($charges['recommended'] > 15) {
            $recommendations[] = 'High making charges may affect competitiveness';
        }

        if ($charges['recommended'] < 8) {
            $recommendations[] = 'Consider increasing making charges to ensure profitability';
        }

        $recommendations[] = 'Review making charges weekly during volatile periods';
        $recommendations[] = 'Lock metal rates for estimates during high volatility';

        return $recommendations;
    }

    /**
     * Get weekly average rate for a metal
     */
    public function getWeeklyAverage($metalId)
    {
        return Cache::remember("metal_weekly_average_{$metalId}", 3600, function () use ($metalId) {
            return MetalRate::where('metal_id', $metalId)
                ->where('effective_date', '>=', now()->subDays(7))
                ->avg('rate_per_gram') ?? 0;
        });
    }

    /**
     * Get monthly average rate for a metal
     */
    public function getMonthlyAverage($metalId)
    {
        return Cache::remember("metal_monthly_average_{$metalId}", 3600, function () use ($metalId) {
            return MetalRate::where('metal_id', $metalId)
                ->where('effective_date', '>=', now()->subDays(30))
                ->avg('rate_per_gram') ?? 0;
        });
    }

    /**
     * Calculate volatility for a metal over specified days
     */
    public function calculateVolatility($metalId, $days = 7)
    {
        return Cache::remember("metal_volatility_{$metalId}_{$days}", 1800, function () use ($metalId, $days) {
            $rates = MetalRate::where('metal_id', $metalId)
                ->where('effective_date', '>=', now()->subDays($days))
                ->orderBy('effective_date', 'asc')
                ->pluck('rate_per_gram')
                ->toArray();

            if (count($rates) < 2) {
                return 0;
            }

            // Calculate daily percentage changes
            $changes = [];
            for ($i = 1; $i < count($rates); $i++) {
                if ($rates[$i - 1] > 0) {
                    $changes[] = (($rates[$i] - $rates[$i - 1]) / $rates[$i - 1]) * 100;
                }
            }

            if (empty($changes)) {
                return 0;
            }

            // Calculate standard deviation of changes
            $mean = array_sum($changes) / count($changes);
            $variance = array_sum(array_map(function($x) use ($mean) {
                return pow($x - $mean, 2);
            }, $changes)) / count($changes);

            return sqrt($variance);
        });
    }

    /**
     * Get advanced rate alerts for dashboard
     */
    public function getAdvancedRateAlerts()
    {
        $alerts = [];

        $metals = Metal::active()->get();

        foreach ($metals as $metal) {
            $currentRate = $this->getCurrentRate($metal->id);
            $weeklyAverage = $this->getWeeklyAverage($metal->id);
            $monthlyAverage = $this->getMonthlyAverage($metal->id);

            // Check for significant deviations from averages
            if ($currentRate && $weeklyAverage) {
                $weeklyDeviation = (($currentRate - $weeklyAverage) / $weeklyAverage) * 100;

                if (abs($weeklyDeviation) > 8) {
                    $alerts[] = [
                        'metal' => $metal->name,
                        'type' => 'weekly_deviation',
                        'current_rate' => $currentRate,
                        'weekly_average' => $weeklyAverage,
                        'deviation_percentage' => $weeklyDeviation,
                        'severity' => abs($weeklyDeviation) > 15 ? 'critical' : 'high',
                        'message' => "Rate deviates " . abs(round($weeklyDeviation, 2)) . "% from weekly average",
                    ];
                }
            }

            if ($currentRate && $monthlyAverage) {
                $monthlyDeviation = (($currentRate - $monthlyAverage) / $monthlyAverage) * 100;

                if (abs($monthlyDeviation) > 12) {
                    $alerts[] = [
                        'metal' => $metal->name,
                        'type' => 'monthly_deviation',
                        'current_rate' => $currentRate,
                        'monthly_average' => $monthlyAverage,
                        'deviation_percentage' => $monthlyDeviation,
                        'severity' => abs($monthlyDeviation) > 20 ? 'critical' : 'high',
                        'message' => "Rate deviates " . abs(round($monthlyDeviation, 2)) . "% from monthly average",
                    ];
                }
            }

            // Check for volatility alerts
            $volatility = $this->calculateVolatility($metal->id, 7); // 7-day volatility
            if ($volatility > 15) {
                $alerts[] = [
                    'metal' => $metal->name,
                    'type' => 'high_volatility',
                    'volatility' => $volatility,
                    'severity' => $volatility > 25 ? 'critical' : 'medium',
                    'message' => "High volatility detected: " . round($volatility, 2) . "%",
                ];
            }
        }

        return $alerts;
    }

    /**
     * Get metal rate comparison across all metals
     */
    public function getMetalRateComparison($days = 7)
    {
        $comparison = [];
        $metals = Metal::active()->get();

        foreach ($metals as $metal) {
            $currentRate = $this->getCurrentRate($metal->id);
            $previousRate = $this->getRateForDate($metal->id, now()->subDays($days));

            $change = $currentRate - $previousRate;
            $changePercentage = $previousRate > 0 ? (($change / $previousRate) * 100) : 0;

            $comparison[] = [
                'metal' => $metal,
                'current_rate' => $currentRate,
                'previous_rate' => $previousRate,
                'change' => $change,
                'change_percentage' => round($changePercentage, 2),
                'trend' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'stable'),
                'last_updated' => MetalRate::where('metal_id', $metal->id)
                    ->latest('effective_date')
                    ->first()?->effective_date,
            ];
        }

        // Sort by absolute change percentage (most volatile first)
        usort($comparison, function ($a, $b) {
            return abs($b['change_percentage']) <=> abs($a['change_percentage']);
        });

        return $comparison;
    }

    /**
     * Get inventory rate impact analysis
     */
    public function getInventoryRateImpact()
    {
        $impact = [
            'total_inventory_value' => 0,
            'total_rate_impact' => 0,
            'metals_impact' => [],
            'most_affected_metal' => null,
            'inventory_change_percentage' => 0,
        ];

        $metals = Metal::active()->get();

        foreach ($metals as $metal) {
            $currentRate = $this->getCurrentRate($metal->id);
            $previousRate = $this->getRateForDate($metal->id, now()->subDays(1));

            // Get inventory items for this metal
            $inventoryValue = 0;
            $inventoryCount = 0;

            // Check if we have inventory items table and products
            if (class_exists('App\Models\InventoryItem')) {
                $inventoryItems = \App\Models\InventoryItem::whereHas('product', function ($query) use ($metal) {
                    $query->where('metal_id', $metal->id);
                })->get();

                foreach ($inventoryItems as $item) {
                    $weight = $item->net_metal_weight ?? $item->actual_weight ?? 0;
                    $inventoryValue += $weight * $currentRate;
                    $inventoryCount++;
                }
            } else {
                // Fallback to products table
                $products = \App\Models\Product::where('metal_id', $metal->id)->get();

                foreach ($products as $product) {
                    $weight = $product->net_weight ?? $product->gross_weight ?? 0;
                    $inventoryValue += $weight * $currentRate;
                    $inventoryCount++;
                }
            }

            $rateChange = $currentRate - $previousRate;
            $rateImpact = $inventoryValue * ($rateChange / ($previousRate ?: 1));

            $metalImpact = [
                'metal' => $metal,
                'inventory_value' => $inventoryValue,
                'inventory_count' => $inventoryCount,
                'rate_change' => $rateChange,
                'rate_impact' => $rateImpact,
                'impact_percentage' => $previousRate > 0 ? (($rateChange / $previousRate) * 100) : 0,
            ];

            $impact['metals_impact'][] = $metalImpact;
            $impact['total_inventory_value'] += $inventoryValue;
            $impact['total_rate_impact'] += $rateImpact;
        }

        // Find most affected metal
        if (!empty($impact['metals_impact'])) {
            $mostAffected = collect($impact['metals_impact'])
                ->sortByDesc(function ($item) {
                    return abs($item['rate_impact']);
                })
                ->first();

            $impact['most_affected_metal'] = $mostAffected;
        }

        // Calculate overall inventory change percentage
        if ($impact['total_inventory_value'] > 0) {
            $impact['inventory_change_percentage'] = round(
                ($impact['total_rate_impact'] / $impact['total_inventory_value']) * 100,
                2
            );
        }

        return $impact;
    }
}
