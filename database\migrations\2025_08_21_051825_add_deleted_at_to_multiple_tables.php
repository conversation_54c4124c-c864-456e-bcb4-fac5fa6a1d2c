<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add deleted_at column to tables that use SoftDeletes but are missing the column

        if (Schema::hasTable('purchase_orders') && !Schema::hasColumn('purchase_orders', 'deleted_at')) {
            Schema::table('purchase_orders', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (Schema::hasTable('repair_services') && !Schema::hasColumn('repair_services', 'deleted_at')) {
            Schema::table('repair_services', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (Schema::hasTable('saving_schemes') && !Schema::hasColumn('saving_schemes', 'deleted_at')) {
            Schema::table('saving_schemes', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (Schema::hasTable('scheme_payments') && !Schema::hasColumn('scheme_payments', 'deleted_at')) {
            Schema::table('scheme_payments', function (Blueprint $table) {
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('purchase_orders', 'deleted_at')) {
            Schema::table('purchase_orders', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }

        if (Schema::hasColumn('repair_services', 'deleted_at')) {
            Schema::table('repair_services', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }

        if (Schema::hasColumn('saving_schemes', 'deleted_at')) {
            Schema::table('saving_schemes', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }

        if (Schema::hasColumn('scheme_payments', 'deleted_at')) {
            Schema::table('scheme_payments', function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }
    }
};
