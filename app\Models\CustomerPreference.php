<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'preferred_metals',
        'avoided_metals',
        'preferred_purity',
        'preferred_categories',
        'preferred_product_types',
        'avoided_categories',
        'preferred_weight_min',
        'preferred_weight_max',
        'preferred_styles',
        'preferred_occasions',
        'preferred_colors',
        'preferred_stone_types',
        'budget_min',
        'budget_max',
        'budget_flexibility',
        'preferred_shopping_times',
        'preferred_contact_methods',
        'prefers_appointments',
        'likes_new_arrivals_notifications',
        'likes_discount_notifications',
        'likes_event_reminders',
        'purchase_frequency',
        'seasonal_preferences',
        'prefers_custom_designs',
        'prefers_ready_made',
        'preferred_language',
        'marketing_emails_consent',
        'promotional_sms_consent',
        'whatsapp_notifications_consent',
        'allergies',
        'special_requirements',
        'notes',
        'last_updated_at',
        'updated_by',
    ];

    protected $casts = [
        'preferred_metals' => 'array',
        'avoided_metals' => 'array',
        'preferred_categories' => 'array',
        'preferred_product_types' => 'array',
        'avoided_categories' => 'array',
        'preferred_weight_min' => 'decimal:3',
        'preferred_weight_max' => 'decimal:3',
        'preferred_styles' => 'array',
        'preferred_occasions' => 'array',
        'preferred_colors' => 'array',
        'preferred_stone_types' => 'array',
        'budget_min' => 'decimal:2',
        'budget_max' => 'decimal:2',
        'preferred_shopping_times' => 'array',
        'preferred_contact_methods' => 'array',
        'prefers_appointments' => 'boolean',
        'likes_new_arrivals_notifications' => 'boolean',
        'likes_discount_notifications' => 'boolean',
        'likes_event_reminders' => 'boolean',
        'seasonal_preferences' => 'array',
        'prefers_custom_designs' => 'boolean',
        'prefers_ready_made' => 'boolean',
        'marketing_emails_consent' => 'boolean',
        'promotional_sms_consent' => 'boolean',
        'whatsapp_notifications_consent' => 'boolean',
        'last_updated_at' => 'datetime',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($preference) {
            $preference->last_updated_at = now();
            $preference->updated_by = auth()->id();
        });
    }

    /**
     * Relationships
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Accessors
     */
    public function getBudgetRangeDisplayAttribute()
    {
        if (!$this->budget_min && !$this->budget_max) {
            return 'Not specified';
        }

        if ($this->budget_min && $this->budget_max) {
            return "₹" . number_format($this->budget_min) . " - ₹" . number_format($this->budget_max);
        }

        if ($this->budget_min) {
            return "Above ₹" . number_format($this->budget_min);
        }

        return "Below ₹" . number_format($this->budget_max);
    }

    public function getWeightRangeDisplayAttribute()
    {
        if (!$this->preferred_weight_min && !$this->preferred_weight_max) {
            return 'Not specified';
        }

        if ($this->preferred_weight_min && $this->preferred_weight_max) {
            return $this->preferred_weight_min . "g - " . $this->preferred_weight_max . "g";
        }

        if ($this->preferred_weight_min) {
            return "Above " . $this->preferred_weight_min . "g";
        }

        return "Below " . $this->preferred_weight_max . "g";
    }

    public function getPreferredMetalsDisplayAttribute()
    {
        if (!$this->preferred_metals) {
            return 'Not specified';
        }

        $metals = Metal::whereIn('id', $this->preferred_metals)->pluck('name')->toArray();
        return implode(', ', $metals);
    }

    public function getPreferredCategoriesDisplayAttribute()
    {
        if (!$this->preferred_categories) {
            return 'Not specified';
        }

        $categories = Category::whereIn('id', $this->preferred_categories)->pluck('name')->toArray();
        return implode(', ', $categories);
    }

    public function getContactMethodsDisplayAttribute()
    {
        if (!$this->preferred_contact_methods) {
            return 'Any method';
        }

        $methods = [
            'phone' => 'Phone',
            'email' => 'Email',
            'sms' => 'SMS',
            'whatsapp' => 'WhatsApp',
        ];

        $preferred = array_intersect_key($methods, array_flip($this->preferred_contact_methods));
        return implode(', ', $preferred);
    }

    /**
     * Check if customer prefers a specific metal
     */
    public function prefersMetal($metalId)
    {
        return $this->preferred_metals && in_array($metalId, $this->preferred_metals);
    }

    /**
     * Check if customer avoids a specific metal
     */
    public function avoidsMetal($metalId)
    {
        return $this->avoided_metals && in_array($metalId, $this->avoided_metals);
    }

    /**
     * Check if customer prefers a specific category
     */
    public function prefersCategory($categoryId)
    {
        return $this->preferred_categories && in_array($categoryId, $this->preferred_categories);
    }

    /**
     * Check if price is within customer's budget
     */
    public function isWithinBudget($price)
    {
        if ($this->budget_min && $price < $this->budget_min) {
            return false;
        }

        if ($this->budget_max && $price > $this->budget_max) {
            return false;
        }

        return true;
    }

    /**
     * Check if weight is within customer's preference
     */
    public function isPreferredWeight($weight)
    {
        if ($this->preferred_weight_min && $weight < $this->preferred_weight_min) {
            return false;
        }

        if ($this->preferred_weight_max && $weight > $this->preferred_weight_max) {
            return false;
        }

        return true;
    }

    /**
     * Get marketing consent status
     */
    public function hasMarketingConsent($channel = null)
    {
        if (!$channel) {
            return $this->marketing_emails_consent ||
                   $this->promotional_sms_consent ||
                   $this->whatsapp_notifications_consent;
        }

        switch ($channel) {
            case 'email':
                return $this->marketing_emails_consent;
            case 'sms':
                return $this->promotional_sms_consent;
            case 'whatsapp':
                return $this->whatsapp_notifications_consent;
            default:
                return false;
        }
    }

    /**
     * Update preferences from array
     */
    public function updatePreferences(array $preferences)
    {
        $this->fill($preferences);
        $this->save();

        return $this;
    }

    /**
     * Get preference score for a product
     */
    public function getProductPreferenceScore($product)
    {
        $score = 0;
        $maxScore = 100;

        // Metal preference (25 points)
        if ($this->prefersMetal($product->metal_id)) {
            $score += 25;
        } elseif ($this->avoidsMetal($product->metal_id)) {
            $score -= 15;
        }

        // Category preference (25 points)
        if ($this->prefersCategory($product->category_id)) {
            $score += 25;
        }

        // Budget preference (25 points)
        if ($this->isWithinBudget($product->selling_price)) {
            $score += 25;
        } elseif ($product->selling_price > ($this->budget_max ?? PHP_INT_MAX)) {
            $score -= 10;
        }

        // Weight preference (15 points)
        if ($product->weight && $this->isPreferredWeight($product->weight)) {
            $score += 15;
        }

        // Style preference (10 points)
        if ($this->preferred_styles && $product->style &&
            in_array($product->style, $this->preferred_styles)) {
            $score += 10;
        }

        return max(0, min($maxScore, $score));
    }
}
