<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GoodsReceiptNoteItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'goods_receipt_note_id',
        'purchase_order_item_id',
        'product_id',
        'quantity_received',
        'unit_price',
        'total_price',
        'quality_rating',
        'notes',
    ];

    protected $casts = [
        'quantity_received' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'quality_rating' => 'decimal:1',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_price = $item->quantity_received * $item->unit_price;
        });
    }

    /**
     * Relationships
     */
    public function goodsReceiptNote(): BelongsTo
    {
        return $this->belongsTo(GoodsReceiptNote::class);
    }

    public function purchaseOrderItem(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrderItem::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Accessors
     */
    public function getQualityRatingDisplayAttribute()
    {
        if (!$this->quality_rating) {
            return 'Not Rated';
        }

        $ratings = [
            1 => 'Poor',
            2 => 'Fair',
            3 => 'Good',
            4 => 'Very Good',
            5 => 'Excellent',
        ];

        return $ratings[$this->quality_rating] ?? 'Unknown';
    }

    public function getQualityRatingColorAttribute()
    {
        if (!$this->quality_rating) {
            return 'gray';
        }

        $colors = [
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'blue',
            5 => 'green',
        ];

        return $colors[$this->quality_rating] ?? 'gray';
    }

    /**
     * Business Logic Methods
     */
    public function updateQualityRating($rating, $notes = null)
    {
        $this->update([
            'quality_rating' => $rating,
            'notes' => $notes ? ($this->notes . "\n\nQuality Update: " . $notes) : $this->notes,
        ]);

        // Update overall GRN quality rating
        $avgRating = $this->goodsReceiptNote->items()->avg('quality_rating');
        $this->goodsReceiptNote->update(['quality_rating' => $avgRating]);

        return $this;
    }

    public function getSummary()
    {
        return [
            'id' => $this->id,
            'product_name' => $this->product->name,
            'product_code' => $this->product->product_code,
            'quantity_received' => $this->quantity_received,
            'unit_price' => $this->unit_price,
            'total_price' => $this->total_price,
            'quality_rating' => $this->quality_rating_display,
            'notes' => $this->notes,
            'grn_number' => $this->goodsReceiptNote->grn_number,
            'received_date' => $this->goodsReceiptNote->received_date->format('Y-m-d'),
        ];
    }
}
