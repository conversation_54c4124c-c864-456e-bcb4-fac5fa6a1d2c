<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_segment_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_segment_id')->constrained()->onDelete('cascade');
            
            // Assignment details
            $table->boolean('is_primary')->default(false); // Primary segment for customer
            $table->enum('assignment_type', ['automatic', 'manual'])->default('automatic');
            $table->text('assignment_reason')->nullable(); // Why customer was assigned to this segment
            $table->decimal('segment_score', 8, 2)->nullable(); // How well customer fits this segment
            
            // Validity
            $table->date('assigned_date');
            $table->date('valid_until')->nullable(); // For temporary assignments
            $table->boolean('is_active')->default(true);
            
            // Audit fields
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            // Unique constraint - customer can only be in a segment once
            $table->unique(['customer_id', 'customer_segment_id'], 'customer_segment_unique');
            
            // Indexes
            $table->index(['customer_id', 'is_primary']);
            $table->index(['customer_segment_id', 'is_active']);
            $table->index(['assignment_type']);
            $table->index(['assigned_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_segment_assignments');
    }
};
