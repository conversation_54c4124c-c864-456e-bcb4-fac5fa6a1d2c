<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Location;
use Carbon\Carbon;

class InvoiceNumberService
{
    /**
     * Generate unique invoice number
     */
    public function generateInvoiceNumber($locationId = null)
    {
        $location = $locationId ? Location::find($locationId) : null;
        $locationCode = $location ? strtoupper(substr($location->name, 0, 3)) : 'DEF';
        
        $year = now()->format('Y');
        $month = now()->format('m');
        
        // Get the last invoice number for this location and month
        $lastInvoice = Invoice::where('invoice_number', 'like', "{$locationCode}{$year}{$month}%")
            ->orderBy('invoice_number', 'desc')
            ->first();

        if ($lastInvoice) {
            // Extract sequence number and increment
            $lastSequence = intval(substr($lastInvoice->invoice_number, -4));
            $sequence = $lastSequence + 1;
        } else {
            $sequence = 1;
        }

        return $locationCode . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generate estimate number
     */
    public function generateEstimateNumber($locationId = null)
    {
        $location = $locationId ? Location::find($locationId) : null;
        $locationCode = $location ? strtoupper(substr($location->name, 0, 3)) : 'DEF';
        
        $year = now()->format('Y');
        $month = now()->format('m');
        
        // This would check estimates table when implemented
        $sequence = 1;

        return 'EST' . $locationCode . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generate receipt number
     */
    public function generateReceiptNumber($locationId = null)
    {
        $location = $locationId ? Location::find($locationId) : null;
        $locationCode = $location ? strtoupper(substr($location->name, 0, 3)) : 'DEF';
        
        $year = now()->format('Y');
        $month = now()->format('m');
        
        // This would check receipts/payments table when implemented
        $sequence = 1;

        return 'RCP' . $locationCode . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Validate invoice number format
     */
    public function validateInvoiceNumber($invoiceNumber)
    {
        // Expected format: LOC202501001 (3 char location + year + month + 4 digit sequence)
        $pattern = '/^[A-Z]{3}[0-9]{4}[0-9]{2}[0-9]{4}$/';
        
        return preg_match($pattern, $invoiceNumber);
    }

    /**
     * Parse invoice number components
     */
    public function parseInvoiceNumber($invoiceNumber)
    {
        if (!$this->validateInvoiceNumber($invoiceNumber)) {
            return null;
        }

        return [
            'location_code' => substr($invoiceNumber, 0, 3),
            'year' => substr($invoiceNumber, 3, 4),
            'month' => substr($invoiceNumber, 7, 2),
            'sequence' => substr($invoiceNumber, 9, 4),
        ];
    }

    /**
     * Get next invoice number preview
     */
    public function getNextInvoiceNumberPreview($locationId = null)
    {
        return $this->generateInvoiceNumber($locationId);
    }

    /**
     * Check if invoice number exists
     */
    public function invoiceNumberExists($invoiceNumber)
    {
        return Invoice::where('invoice_number', $invoiceNumber)->exists();
    }

    /**
     * Generate custom invoice number with prefix
     */
    public function generateCustomInvoiceNumber($prefix, $locationId = null)
    {
        $location = $locationId ? Location::find($locationId) : null;
        $locationCode = $location ? strtoupper(substr($location->name, 0, 3)) : 'DEF';
        
        $year = now()->format('y'); // 2-digit year
        $month = now()->format('m');
        
        // Get the last invoice with this prefix
        $lastInvoice = Invoice::where('invoice_number', 'like', "{$prefix}%")
            ->orderBy('invoice_number', 'desc')
            ->first();

        if ($lastInvoice) {
            $lastSequence = intval(substr($lastInvoice->invoice_number, -4));
            $sequence = $lastSequence + 1;
        } else {
            $sequence = 1;
        }

        return $prefix . $locationCode . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get invoice statistics for a period
     */
    public function getInvoiceStats($startDate = null, $endDate = null, $locationId = null)
    {
        $query = Invoice::query();

        if ($startDate) {
            $query->whereDate('invoice_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->whereDate('invoice_date', '<=', $endDate);
        }

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        return [
            'total_invoices' => $query->count(),
            'total_amount' => $query->sum('total_amount'),
            'total_gst' => $query->sum('total_gst'),
            'paid_invoices' => $query->where('payment_status', 'paid')->count(),
            'pending_invoices' => $query->where('payment_status', 'pending')->count(),
            'overdue_invoices' => $query->where('payment_status', 'overdue')->count(),
        ];
    }

    /**
     * Format invoice number for display
     */
    public function formatInvoiceNumber($invoiceNumber)
    {
        if (!$this->validateInvoiceNumber($invoiceNumber)) {
            return $invoiceNumber;
        }

        $parts = $this->parseInvoiceNumber($invoiceNumber);
        
        return $parts['location_code'] . '/' . $parts['year'] . '/' . $parts['month'] . '/' . $parts['sequence'];
    }
}
