<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;
use App\Services\UniqueConstraintService;

class UniqueWithSuggestions implements ValidationRule
{
    protected string $table;
    protected string $column;
    protected $ignoreId;
    protected array $suggestions = [];

    public function __construct(string $table, string $column = null, $ignoreId = null)
    {
        $this->table = $table;
        $this->column = $column;
        $this->ignoreId = $ignoreId;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $column = $this->column ?: $attribute;
        
        // Check if the value already exists
        $query = DB::table($this->table)->where($column, $value);
        
        if ($this->ignoreId) {
            $query->where('id', '!=', $this->ignoreId);
        }
        
        if ($query->exists()) {
            // Generate suggestions
            $this->suggestions = UniqueConstraintService::getSuggestions($this->table, $column, $value);
            
            $message = "The {$attribute} '{$value}' is already taken.";
            
            if (!empty($this->suggestions)) {
                $message .= " Suggestions: " . implode(', ', $this->suggestions);
            }
            
            $fail($message);
        }
    }

    /**
     * Get the suggestions generated during validation
     */
    public function getSuggestions(): array
    {
        return $this->suggestions;
    }

    /**
     * Create a rule instance that ignores a specific ID
     */
    public static function ignore(string $table, string $column = null, $ignoreId = null): self
    {
        return new static($table, $column, $ignoreId);
    }
}