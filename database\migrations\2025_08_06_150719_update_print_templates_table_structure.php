<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('print_templates', function (Blueprint $table) {
            // Check if columns exist before adding them
            if (!Schema::hasColumn('print_templates', 'code')) {
                $table->string('code', 50)->unique()->after('name');
            }
            if (!Schema::hasColumn('print_templates', 'template_type')) {
                $table->enum('template_type', [
                    'invoice', 'receipt', 'estimate', 'tag', 'label',
                    'barcode', 'qr_code', 'report', 'custom'
                ])->after('code');
            }
            if (!Schema::hasColumn('print_templates', 'description')) {
                $table->text('description')->nullable()->after('template_type');
            }
            if (!Schema::hasColumn('print_templates', 'template_content')) {
                $table->longText('template_content')->after('description');
            }
            if (!Schema::hasColumn('print_templates', 'template_variables')) {
                $table->json('template_variables')->nullable()->after('template_content');
            }
            if (!Schema::hasColumn('print_templates', 'required_fields')) {
                $table->json('required_fields')->nullable()->after('template_variables');
            }
            if (!Schema::hasColumn('print_templates', 'paper_width_mm')) {
                $table->integer('paper_width_mm')->default(80)->after('required_fields');
            }
            if (!Schema::hasColumn('print_templates', 'paper_height_mm')) {
                $table->integer('paper_height_mm')->nullable()->after('paper_width_mm');
            }
            if (!Schema::hasColumn('print_templates', 'is_system_template')) {
                $table->boolean('is_system_template')->default(false)->after('paper_height_mm');
            }
            if (!Schema::hasColumn('print_templates', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('is_system_template');
            }
            if (!Schema::hasColumn('print_templates', 'is_default')) {
                $table->boolean('is_default')->default(false)->after('is_active');
            }
            if (!Schema::hasColumn('print_templates', 'version')) {
                $table->string('version', 20)->default('1.0')->after('is_default');
            }
            if (!Schema::hasColumn('print_templates', 'usage_count')) {
                $table->integer('usage_count')->default(0)->after('version');
            }
            if (!Schema::hasColumn('print_templates', 'last_used_at')) {
                $table->timestamp('last_used_at')->nullable()->after('usage_count');
            }
            if (!Schema::hasColumn('print_templates', 'created_by')) {
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade')->after('last_used_at');
            }
            if (!Schema::hasColumn('print_templates', 'updated_by')) {
                $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null')->after('created_by');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('print_templates', function (Blueprint $table) {
            $columnsToRemove = [
                'code', 'template_type', 'description', 'template_content',
                'template_variables', 'required_fields', 'paper_width_mm',
                'paper_height_mm', 'is_system_template', 'is_active',
                'is_default', 'version', 'usage_count', 'last_used_at',
                'created_by', 'updated_by'
            ];

            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('print_templates', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
