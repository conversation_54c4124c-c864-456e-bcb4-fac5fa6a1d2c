<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class BaseApiController extends Controller
{
    /**
     * Success response method
     */
    protected function sendResponse($result, $message = 'Success', $code = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $result,
            'timestamp' => now()->toISOString(),
        ];

        return response()->json($response, $code);
    }

    /**
     * Error response method
     */
    protected function sendError($error, $errorMessages = [], $code = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $error,
            'timestamp' => now()->toISOString(),
        ];

        if (!empty($errorMessages)) {
            $response['errors'] = $errorMessages;
        }

        return response()->json($response, $code);
    }

    /**
     * Validation error response
     */
    protected function sendValidationError($validator): JsonResponse
    {
        return $this->sendError('Validation Error', $validator->errors(), 422);
    }

    /**
     * Paginated response method
     */
    protected function sendPaginatedResponse($data, $message = 'Success'): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data->items(),
            'pagination' => [
                'current_page' => $data->currentPage(),
                'last_page' => $data->lastPage(),
                'per_page' => $data->perPage(),
                'total' => $data->total(),
                'from' => $data->firstItem(),
                'to' => $data->lastItem(),
                'has_more_pages' => $data->hasMorePages(),
            ],
            'timestamp' => now()->toISOString(),
        ];

        return response()->json($response, 200);
    }

    /**
     * Not found response
     */
    protected function sendNotFoundResponse($message = 'Resource not found'): JsonResponse
    {
        return $this->sendError($message, [], 404);
    }

    /**
     * Unauthorized response
     */
    protected function sendUnauthorizedResponse($message = 'Unauthorized'): JsonResponse
    {
        return $this->sendError($message, [], 401);
    }

    /**
     * Forbidden response
     */
    protected function sendForbiddenResponse($message = 'Forbidden'): JsonResponse
    {
        return $this->sendError($message, [], 403);
    }

    /**
     * Server error response
     */
    protected function sendServerErrorResponse($message = 'Internal Server Error'): JsonResponse
    {
        return $this->sendError($message, [], 500);
    }

    /**
     * Validate request data
     */
    protected function validateRequest(Request $request, array $rules, array $messages = []): array
    {
        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Get authenticated user
     */
    protected function getAuthenticatedUser()
    {
        return auth('sanctum')->user();
    }

    /**
     * Check if user has permission
     */
    protected function checkPermission(string $permission): bool
    {
        $user = $this->getAuthenticatedUser();
        return $user && $user->can($permission);
    }

    /**
     * Rate limit response
     */
    protected function sendRateLimitResponse($message = 'Too Many Requests'): JsonResponse
    {
        return $this->sendError($message, [], 429);
    }

    /**
     * Log API activity
     */
    protected function logApiActivity(Request $request, $action, $resource = null, $resourceId = null)
    {
        try {
            \App\Models\ApiLog::create([
                'user_id' => auth('sanctum')->id(),
                'endpoint' => $request->path(),
                'method' => $request->method(),
                'action' => $action,
                'resource' => $resource,
                'resource_id' => $resourceId,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'request_data' => $this->sanitizeRequestData($request->all()),
                'response_code' => null, // Will be updated in middleware
            ]);
        } catch (\Exception $e) {
            // Log the error but don't fail the request
            \Log::error('Failed to log API activity: ' . $e->getMessage());
        }
    }

    /**
     * Sanitize request data for logging
     */
    private function sanitizeRequestData(array $data): array
    {
        $sensitiveFields = ['password', 'password_confirmation', 'token', 'secret', 'key'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }

    /**
     * Transform resource for API response
     */
    protected function transformResource($resource, $transformer = null)
    {
        if ($transformer && class_exists($transformer)) {
            return new $transformer($resource);
        }

        return $resource;
    }

    /**
     * Transform collection for API response
     */
    protected function transformCollection($collection, $transformer = null)
    {
        if ($transformer && class_exists($transformer)) {
            return $transformer::collection($collection);
        }

        return $collection;
    }

    /**
     * Handle API exceptions
     */
    protected function handleApiException(\Exception $e): JsonResponse
    {
        \Log::error('API Exception: ' . $e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
        ]);

        if ($e instanceof ValidationException) {
            return $this->sendValidationError($e->validator);
        }

        if ($e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
            return $this->sendNotFoundResponse('Resource not found');
        }

        if ($e instanceof \Illuminate\Auth\AuthenticationException) {
            return $this->sendUnauthorizedResponse('Authentication required');
        }

        if ($e instanceof \Illuminate\Auth\Access\AuthorizationException) {
            return $this->sendForbiddenResponse('Access denied');
        }

        // For production, don't expose internal errors
        if (app()->environment('production')) {
            return $this->sendServerErrorResponse('An error occurred while processing your request');
        }

        return $this->sendServerErrorResponse($e->getMessage());
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, Request $request, array $allowedFilters = [])
    {
        foreach ($allowedFilters as $filter) {
            if ($request->has($filter)) {
                $value = $request->get($filter);
                
                if ($value !== null && $value !== '') {
                    $query->where($filter, 'like', "%{$value}%");
                }
            }
        }

        return $query;
    }

    /**
     * Apply sorting to query
     */
    protected function applySorting($query, Request $request, array $allowedSorts = [])
    {
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder === 'asc' ? 'asc' : 'desc');
        }

        return $query;
    }

    /**
     * Get pagination parameters
     */
    protected function getPaginationParams(Request $request): array
    {
        return [
            'per_page' => min($request->get('per_page', 15), 100), // Max 100 items per page
            'page' => $request->get('page', 1),
        ];
    }

    /**
     * Build API metadata
     */
    protected function buildMetadata(Request $request): array
    {
        return [
            'api_version' => config('app.api_version', '1.0'),
            'request_id' => $request->header('X-Request-ID', uniqid()),
            'timestamp' => now()->toISOString(),
            'timezone' => config('app.timezone'),
        ];
    }

    /**
     * Cache API response
     */
    protected function cacheResponse(string $key, $data, int $ttl = 300)
    {
        try {
            cache()->put($key, $data, $ttl);
        } catch (\Exception $e) {
            \Log::warning('Failed to cache API response: ' . $e->getMessage());
        }
    }

    /**
     * Get cached response
     */
    protected function getCachedResponse(string $key)
    {
        try {
            return cache()->get($key);
        } catch (\Exception $e) {
            \Log::warning('Failed to get cached API response: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate cache key
     */
    protected function generateCacheKey(Request $request, string $prefix = ''): string
    {
        $key = $prefix . ':' . $request->path() . ':' . md5(serialize($request->query()));
        
        if ($user = $this->getAuthenticatedUser()) {
            $key .= ':user:' . $user->id;
        }

        return $key;
    }
}
