<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{

    protected $fillable = [
        'customer_code',
        'first_name',
        'last_name',
        'phone',
        'email',
        'alternate_phone',
        'date_of_birth',
        'anniversary_date',
        'gender',
        'customer_type',
        'customer_segment',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'pan_number',
        'aadhar_number',
        'gstin',
        'company_name',
        'designation',
        'credit_limit',
        'credit_days',
        'discount_percentage',
        'notes',
        'is_active',
        'kyc_status',
        'kyc_verified_at',
        'kyc_verified_by',
        'kyc_notes',
        'last_purchase_date',
        'total_purchase_amount',
        'loyalty_points',
        'referral_code',
        'referred_by',
        'preferred_contact_method',
        'marketing_consent',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'anniversary_date' => 'date',
        'credit_limit' => 'decimal:2',
        'is_active' => 'boolean',
        'last_purchase_date' => 'datetime',
        'total_purchase_amount' => 'decimal:2',
    ];

    // Relationships
    public function addresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class);
    }

    public function documents(): HasMany
    {
        return $this->hasMany(CustomerDocument::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function estimates(): HasMany
    {
        return $this->hasMany(Estimate::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function oldGoldTransactions(): HasMany
    {
        return $this->hasMany(OldGoldTransaction::class);
    }

    public function savingSchemes(): HasMany
    {
        return $this->hasMany(SavingScheme::class);
    }

    public function repairServices(): HasMany
    {
        return $this->hasMany(RepairService::class);
    }

    public function serviceRequests(): HasMany
    {
        return $this->hasMany(ServiceRequest::class);
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(CustomerNotification::class);
    }

    public function preferences(): HasOne
    {
        return $this->hasOne(CustomerPreference::class);
    }

    public function analytics(): HasOne
    {
        return $this->hasOne(CustomerAnalytics::class);
    }

    public function interactions(): HasMany
    {
        return $this->hasMany(CustomerInteraction::class);
    }

    public function loyaltyPoints(): HasMany
    {
        return $this->hasMany(LoyaltyPoint::class);
    }

    public function loyaltyRedemptions(): HasMany
    {
        return $this->hasMany(LoyaltyRedemption::class);
    }

    public function campaigns(): BelongsToMany
    {
        return $this->belongsToMany(MarketingCampaign::class, 'campaign_customers')
                   ->withPivot(['sent_at', 'opened_at', 'clicked_at', 'status'])
                   ->withTimestamps();
    }

    public function posTransactions(): HasMany
    {
        return $this->hasMany(POSTransaction::class);
    }

    // Accessors
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function getFormattedPhoneAttribute(): string
    {
        return '+91-' . $this->phone;
    }

    public function getDefaultAddressAttribute()
    {
        return $this->addresses()->where('is_default', true)->first();
    }

    public function getOutstandingAmountAttribute(): float
    {
        return $this->invoices()
            ->whereIn('status', ['sent', 'partially_paid', 'overdue'])
            ->sum('balance_amount');
    }

    public function getIsVipAttribute(): bool
    {
        return $this->customer_segment === 'vip';
    }

    public function getTotalLoyaltyPointsAttribute(): int
    {
        return $this->loyaltyPoints()->where('status', 'active')->sum('points');
    }

    public function getLifetimeValueAttribute(): float
    {
        return $this->analytics?->customer_lifetime_value ?? $this->total_purchase_amount;
    }

    public function getLastInteractionAttribute()
    {
        return $this->interactions()->latest()->first();
    }

    public function getDaysSinceLastPurchaseAttribute(): int
    {
        if (!$this->last_purchase_date) {
            return 0;
        }
        return $this->last_purchase_date->diffInDays(now());
    }

    public function getCustomerStatusAttribute(): string
    {
        $daysSinceLastPurchase = $this->days_since_last_purchase;

        if ($daysSinceLastPurchase <= 30) {
            return 'active';
        } elseif ($daysSinceLastPurchase <= 90) {
            return 'at_risk';
        } elseif ($daysSinceLastPurchase <= 180) {
            return 'dormant';
        } else {
            return 'lost';
        }
    }

    public function getPreferredContactMethodAttribute(): string
    {
        $preferences = $this->preferences?->preferred_contact_methods ?? ['phone'];
        return is_array($preferences) ? $preferences[0] : 'phone';
    }

    // Business logic methods
    public function updatePurchaseStats(float $amount): void
    {
        $this->update([
            'last_purchase_date' => now(),
            'total_purchase_amount' => $this->total_purchase_amount + $amount,
        ]);

        // Auto-upgrade to VIP if total purchases exceed threshold
        if ($this->total_purchase_amount >= 500000 && $this->customer_segment !== 'vip') {
            $this->update(['customer_segment' => 'vip']);
        }

        // Award loyalty points (1 point per ₹100 spent)
        $this->awardLoyaltyPoints($amount);
    }

    public function awardLoyaltyPoints(float $amount, string $reason = 'Purchase', $referenceId = null): LoyaltyPoint
    {
        $pointsEarned = floor($amount / 100); // 1 point per ₹100

        return $this->loyaltyPoints()->create([
            'points' => $pointsEarned,
            'transaction_type' => 'earned',
            'reason' => $reason,
            'reference_type' => $referenceId ? get_class($referenceId) : null,
            'reference_id' => $referenceId?->id,
            'status' => 'active',
            'expires_at' => now()->addYear(), // Points expire in 1 year
        ]);
    }

    public function redeemLoyaltyPoints(int $points, string $reason = 'Redemption', $referenceId = null): LoyaltyRedemption
    {
        $availablePoints = $this->total_loyalty_points;

        if ($points > $availablePoints) {
            throw new \Exception('Insufficient loyalty points');
        }

        // Create redemption record
        $redemption = $this->loyaltyRedemptions()->create([
            'points_redeemed' => $points,
            'reason' => $reason,
            'reference_type' => $referenceId ? get_class($referenceId) : null,
            'reference_id' => $referenceId?->id,
        ]);

        // Deduct points (mark oldest points as redeemed)
        $pointsToRedeem = $points;
        $loyaltyPoints = $this->loyaltyPoints()
                             ->where('status', 'active')
                             ->where('expires_at', '>', now())
                             ->orderBy('created_at')
                             ->get();

        foreach ($loyaltyPoints as $loyaltyPoint) {
            if ($pointsToRedeem <= 0) break;

            $pointsToDeduct = min($pointsToRedeem, $loyaltyPoint->points);

            if ($pointsToDeduct == $loyaltyPoint->points) {
                $loyaltyPoint->update(['status' => 'redeemed']);
            } else {
                // Split the points
                $loyaltyPoint->update(['points' => $loyaltyPoint->points - $pointsToDeduct]);

                $this->loyaltyPoints()->create([
                    'points' => $pointsToDeduct,
                    'transaction_type' => 'redeemed',
                    'reason' => 'Partial redemption',
                    'status' => 'redeemed',
                    'expires_at' => $loyaltyPoint->expires_at,
                ]);
            }

            $pointsToRedeem -= $pointsToDeduct;
        }

        return $redemption;
    }

    public function addInteraction(string $type, string $subject, string $description = null, array $metadata = []): CustomerInteraction
    {
        return $this->interactions()->create([
            'interaction_type' => $type,
            'subject' => $subject,
            'description' => $description,
            'metadata' => $metadata,
            'interaction_date' => now(),
            'created_by' => auth()->id(),
        ]);
    }

    public function canPurchaseOnCredit(float $amount): bool
    {
        if ($this->credit_limit <= 0) {
            return false;
        }

        $currentOutstanding = $this->outstanding_amount;
        return ($currentOutstanding + $amount) <= $this->credit_limit;
    }

    public function getUpcomingEvents(): array
    {
        $events = [];
        $today = now();

        if ($this->date_of_birth) {
            $nextBirthday = $this->date_of_birth->setYear($today->year);
            if ($nextBirthday->isPast()) {
                $nextBirthday->addYear();
            }
            $events[] = [
                'type' => 'birthday',
                'date' => $nextBirthday,
                'days_until' => $today->diffInDays($nextBirthday),
            ];
        }

        if ($this->anniversary_date) {
            $nextAnniversary = $this->anniversary_date->setYear($today->year);
            if ($nextAnniversary->isPast()) {
                $nextAnniversary->addYear();
            }
            $events[] = [
                'type' => 'anniversary',
                'date' => $nextAnniversary,
                'days_until' => $today->diffInDays($nextAnniversary),
            ];
        }

        return collect($events)->sortBy('days_until')->toArray();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeVip($query)
    {
        return $query->where('customer_segment', 'vip');
    }

    public function scopeWithOutstanding($query)
    {
        return $query->whereHas('invoices', function ($q) {
            $q->whereIn('status', ['sent', 'partially_paid', 'overdue'])
              ->where('balance_amount', '>', 0);
        });
    }

    public function scopeByStatus($query, $status)
    {
        $daysCriteria = [
            'active' => ['<=', 30],
            'at_risk' => ['>', 30, '<=', 90],
            'dormant' => ['>', 90, '<=', 180],
            'lost' => ['>', 180],
        ];

        if (!isset($daysCriteria[$status])) {
            return $query;
        }

        $criteria = $daysCriteria[$status];

        if (count($criteria) === 2) {
            return $query->whereRaw('DATEDIFF(NOW(), last_purchase_date) ' . $criteria[0] . ' ?', [$criteria[1]]);
        } else {
            return $query->whereRaw('DATEDIFF(NOW(), last_purchase_date) ' . $criteria[0] . ' ? AND DATEDIFF(NOW(), last_purchase_date) ' . $criteria[2] . ' ?', [$criteria[1], $criteria[3]]);
        }
    }

    public function scopeHighValue($query, $threshold = 100000)
    {
        return $query->where('total_purchase_amount', '>=', $threshold);
    }

    public function scopeRecentPurchasers($query, $days = 30)
    {
        return $query->where('last_purchase_date', '>=', now()->subDays($days));
    }

    public function scopeWithBirthday($query, $month = null)
    {
        $query = $query->whereNotNull('date_of_birth');

        if ($month) {
            $query->whereMonth('date_of_birth', $month);
        }

        return $query;
    }

    public function scopeWithAnniversary($query, $month = null)
    {
        $query = $query->whereNotNull('anniversary_date');

        if ($month) {
            $query->whereMonth('anniversary_date', $month);
        }

        return $query;
    }

    public function scopeBySegment($query, $segment)
    {
        return $query->where('customer_segment', $segment);
    }

    public function scopeByPreference($query, $preferenceType, $preferenceValue)
    {
        return $query->whereHas('preferences', function ($q) use ($preferenceType, $preferenceValue) {
            $q->whereJsonContains($preferenceType, $preferenceValue);
        });
    }

    // Auto-generate customer code


    /**
     * Get customer's average order value
     */
    public function getAverageOrderValueAttribute(): float
    {
        $totalOrders = $this->invoices()->where('status', 'paid')->count();
        return $totalOrders > 0 ? $this->lifetime_value / $totalOrders : 0;
    }

    /**
     * Get customer's purchase frequency (orders per year)
     */
    public function getPurchaseFrequencyAttribute(): float
    {
        $firstPurchase = $this->invoices()->oldest()->first()?->created_at;
        if (!$firstPurchase) return 0;

        $yearsActive = max(1, $firstPurchase->diffInYears(now()) ?: 1);
        $totalOrders = $this->invoices()->where('status', 'paid')->count();

        return $totalOrders / $yearsActive;
    }

    /**
     * Get customer segment based on purchase behavior
     */
    public function getCustomerSegmentAttribute(): string
    {
        $lifetimeValue = $this->lifetime_value;
        $frequency = $this->purchase_frequency;
        $recency = $this->last_purchase_date ? $this->last_purchase_date->diffInDays(now()) : 999;

        // VIP: High value, frequent, recent
        if ($lifetimeValue >= 100000 && $frequency >= 2 && $recency <= 90) {
            return 'VIP';
        }

        // Loyal: Good value, frequent
        if ($lifetimeValue >= 50000 && $frequency >= 1.5) {
            return 'Loyal';
        }

        // Regular: Moderate activity
        if ($lifetimeValue >= 20000 || $frequency >= 1) {
            return 'Regular';
        }

        // New: Recent customer with potential
        if ($recency <= 30) {
            return 'New';
        }

        // At Risk: Haven't purchased recently
        if ($recency > 180) {
            return 'At Risk';
        }

        return 'Occasional';
    }

    /**
     * Get customer's preferred categories
     */
    public function getPreferredCategoriesAttribute(): array
    {
        return $this->invoices()
                   ->join('invoice_items', 'invoices.id', '=', 'invoice_items.invoice_id')
                   ->join('products', 'invoice_items.product_id', '=', 'products.id')
                   ->join('categories', 'products.category_id', '=', 'categories.id')
                   ->selectRaw('categories.name, COUNT(*) as purchase_count')
                   ->groupBy('categories.id', 'categories.name')
                   ->orderByDesc('purchase_count')
                   ->limit(5)
                   ->pluck('purchase_count', 'name')
                   ->toArray();
    }

    /**
     * Calculate loyalty points earned
     */
    public function calculateLoyaltyPoints(): int
    {
        // 1 point per ₹100 spent
        return (int) floor($this->lifetime_value / 100);
    }

    /**
     * Get upcoming birthday/anniversary alerts
     */
    public function getUpcomingEventsAttribute(): array
    {
        $events = [];
        $today = now();

        if ($this->date_of_birth) {
            $nextBirthday = $this->date_of_birth->setYear($today->year);
            if ($nextBirthday->isPast()) {
                $nextBirthday->addYear();
            }

            $daysUntilBirthday = $today->diffInDays($nextBirthday);
            if ($daysUntilBirthday <= 30) {
                $events[] = [
                    'type' => 'birthday',
                    'date' => $nextBirthday,
                    'days_until' => $daysUntilBirthday
                ];
            }
        }

        if ($this->anniversary_date) {
            $nextAnniversary = $this->anniversary_date->setYear($today->year);
            if ($nextAnniversary->isPast()) {
                $nextAnniversary->addYear();
            }

            $daysUntilAnniversary = $today->diffInDays($nextAnniversary);
            if ($daysUntilAnniversary <= 30) {
                $events[] = [
                    'type' => 'anniversary',
                    'date' => $nextAnniversary,
                    'days_until' => $daysUntilAnniversary
                ];
            }
        }

        return $events;
    }

    /**
     * Generate referral code
     */
    public function generateReferralCode(): string
    {
        if ($this->referral_code) {
            return $this->referral_code;
        }

        $code = 'REF' . strtoupper(substr($this->first_name, 0, 2)) .
                strtoupper(substr($this->last_name ?? 'XX', 0, 2)) .
                str_pad($this->id, 4, '0', STR_PAD_LEFT);

        $this->update(['referral_code' => $code]);
        return $code;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($customer) {
            if (empty($customer->customer_code)) {
                $customer->customer_code = static::generateCustomerCode();
            }
            $customer->created_by = auth()->id();
        });

        static::updating(function ($customer) {
            $customer->updated_by = auth()->id();
        });

        static::created(function ($customer) {
            // Generate referral code
            $customer->generateReferralCode();

            // Initialize loyalty points
            $customer->update(['loyalty_points' => 0]);
        });
    }

    private static function generateCustomerCode(): string
    {
        $prefix = 'CUST';
        $year = now()->format('y');
        $lastCustomer = static::whereYear('created_at', now()->year)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastCustomer ? (intval(substr($lastCustomer->customer_code, -4)) + 1) : 1;

        return $prefix . $year . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
