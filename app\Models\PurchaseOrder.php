<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class PurchaseOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'po_number',
        'supplier_id',
        'location_id',
        'po_date',
        'expected_delivery_date',
        'status',
        'subtotal',
        'tax_amount',
        'total_amount',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'po_date' => 'date',
        'expected_delivery_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($purchaseOrder) {
            if (!$purchaseOrder->po_number) {
                $purchaseOrder->po_number = static::generatePONumber();
            }
            $purchaseOrder->created_by = auth()->id();
        });

        static::updating(function ($purchaseOrder) {
            // Recalculate totals when items change
            if ($purchaseOrder->isDirty(['subtotal', 'tax_amount'])) {
                $purchaseOrder->total_amount = $purchaseOrder->subtotal + $purchaseOrder->tax_amount;
            }
        });
    }

    /**
     * Relationships
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function items(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    public function goodsReceiptNotes(): HasMany
    {
        return $this->hasMany(GoodsReceiptNote::class);
    }

    public function supplierPayments(): HasMany
    {
        return $this->hasMany(SupplierPayment::class);
    }

    /**
     * Scopes
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopePartiallyReceived($query)
    {
        return $query->where('status', 'partially_received');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    public function scopeOverdue($query)
    {
        return $query->where('expected_delivery_date', '<', now())
                    ->whereIn('status', ['sent', 'confirmed', 'partially_received']);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('po_date', now()->month)
                    ->whereYear('po_date', now()->year);
    }

    /**
     * Accessors
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'draft' => 'Draft',
            'sent' => 'Sent to Supplier',
            'confirmed' => 'Confirmed',
            'partially_received' => 'Partially Received',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'draft' => 'gray',
            'sent' => 'blue',
            'confirmed' => 'yellow',
            'partially_received' => 'orange',
            'completed' => 'green',
            'cancelled' => 'red',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getIsOverdueAttribute()
    {
        return $this->expected_delivery_date &&
               $this->expected_delivery_date < now() &&
               in_array($this->status, ['sent', 'confirmed', 'partially_received']);
    }

    public function getDaysOverdueAttribute()
    {
        if (!$this->is_overdue) {
            return 0;
        }

        return $this->expected_delivery_date->diffInDays(now());
    }

    public function getCompletionPercentageAttribute()
    {
        $totalOrdered = $this->items->sum('quantity_ordered');
        $totalReceived = $this->items->sum('quantity_received');

        if ($totalOrdered == 0) {
            return 0;
        }

        return round(($totalReceived / $totalOrdered) * 100, 2);
    }

    public function getPendingAmountAttribute()
    {
        $totalPaid = $this->supplierPayments()
                         ->where('status', 'completed')
                         ->sum('amount');

        return max(0, $this->total_amount - $totalPaid);
    }

    public function getCanCancelAttribute()
    {
        return in_array($this->status, ['draft', 'sent']);
    }

    public function getCanConfirmAttribute()
    {
        return $this->status === 'sent';
    }

    public function getCanReceiveAttribute()
    {
        return in_array($this->status, ['confirmed', 'partially_received']);
    }

    /**
     * Business Logic Methods
     */
    public static function generatePONumber()
    {
        $prefix = 'PO';
        $year = now()->format('y');
        $month = now()->format('m');

        $lastPO = static::whereYear('created_at', now()->year)
                       ->whereMonth('created_at', now()->month)
                       ->orderBy('id', 'desc')
                       ->first();

        $sequence = $lastPO ? (int) substr($lastPO->po_number, -4) + 1 : 1;

        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function calculateTotals()
    {
        $this->subtotal = $this->items->sum('total_price');
        $this->tax_amount = $this->subtotal * 0.18; // Assuming 18% GST
        $this->total_amount = $this->subtotal + $this->tax_amount;
        $this->save();

        return $this;
    }

    public function sendToSupplier()
    {
        if ($this->status !== 'draft') {
            throw new \Exception('Only draft purchase orders can be sent');
        }

        $this->update(['status' => 'sent']);

        // Log activity
        activity()
            ->performedOn($this)
            ->log('Purchase order sent to supplier');

        return $this;
    }

    public function confirm()
    {
        if ($this->status !== 'sent') {
            throw new \Exception('Only sent purchase orders can be confirmed');
        }

        $this->update(['status' => 'confirmed']);

        // Log activity
        activity()
            ->performedOn($this)
            ->log('Purchase order confirmed by supplier');

        return $this;
    }

    public function cancel($reason = null)
    {
        if (!$this->can_cancel) {
            throw new \Exception('This purchase order cannot be cancelled');
        }

        $this->update(['status' => 'cancelled']);

        // Log activity
        activity()
            ->performedOn($this)
            ->withProperties(['reason' => $reason])
            ->log('Purchase order cancelled');

        return $this;
    }

    public function updateReceiptStatus()
    {
        $totalOrdered = $this->items->sum('quantity_ordered');
        $totalReceived = $this->items->sum('quantity_received');

        if ($totalReceived == 0) {
            $status = in_array($this->status, ['draft', 'sent']) ? $this->status : 'confirmed';
        } elseif ($totalReceived >= $totalOrdered) {
            $status = 'completed';
        } else {
            $status = 'partially_received';
        }

        $this->update(['status' => $status]);

        return $this;
    }

    public function addItem($productId, $quantity, $unitPrice, $notes = null)
    {
        $product = Product::findOrFail($productId);

        $item = $this->items()->create([
            'product_id' => $productId,
            'quantity_ordered' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $quantity * $unitPrice,
            'notes' => $notes,
        ]);

        $this->calculateTotals();

        return $item;
    }

    public function removeItem($itemId)
    {
        $item = $this->items()->findOrFail($itemId);
        $item->delete();

        $this->calculateTotals();

        return $this;
    }

    public function updateItem($itemId, $quantity = null, $unitPrice = null, $notes = null)
    {
        $item = $this->items()->findOrFail($itemId);

        $updates = [];
        if ($quantity !== null) $updates['quantity_ordered'] = $quantity;
        if ($unitPrice !== null) $updates['unit_price'] = $unitPrice;
        if ($notes !== null) $updates['notes'] = $notes;

        if (!empty($updates)) {
            if (isset($updates['quantity_ordered']) || isset($updates['unit_price'])) {
                $updates['total_price'] = ($quantity ?? $item->quantity_ordered) * ($unitPrice ?? $item->unit_price);
            }

            $item->update($updates);
            $this->calculateTotals();
        }

        return $item;
    }

    public function createGRN($receivedItems, $receivedDate = null, $notes = null)
    {
        $grn = GoodsReceiptNote::create([
            'grn_number' => GoodsReceiptNote::generateGRNNumber(),
            'purchase_order_id' => $this->id,
            'supplier_id' => $this->supplier_id,
            'location_id' => $this->location_id,
            'received_date' => $receivedDate ?: now(),
            'notes' => $notes,
            'created_by' => auth()->id(),
        ]);

        foreach ($receivedItems as $itemData) {
            $poItem = $this->items()->findOrFail($itemData['po_item_id']);

            $grn->items()->create([
                'purchase_order_item_id' => $poItem->id,
                'product_id' => $poItem->product_id,
                'quantity_received' => $itemData['quantity_received'],
                'unit_price' => $poItem->unit_price,
                'total_price' => $itemData['quantity_received'] * $poItem->unit_price,
                'quality_rating' => $itemData['quality_rating'] ?? null,
                'notes' => $itemData['notes'] ?? null,
            ]);

            // Update PO item received quantity
            $poItem->increment('quantity_received', $itemData['quantity_received']);
        }

        // Update PO status
        $this->updateReceiptStatus();

        return $grn;
    }

    public function getSummary()
    {
        return [
            'po_number' => $this->po_number,
            'supplier' => $this->supplier->name,
            'location' => $this->location->name,
            'po_date' => $this->po_date->format('Y-m-d'),
            'expected_delivery' => $this->expected_delivery_date?->format('Y-m-d'),
            'status' => $this->status_display,
            'total_amount' => $this->total_amount,
            'pending_amount' => $this->pending_amount,
            'completion_percentage' => $this->completion_percentage,
            'is_overdue' => $this->is_overdue,
            'days_overdue' => $this->days_overdue,
            'total_items' => $this->items->count(),
            'created_by' => $this->createdBy->name,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
