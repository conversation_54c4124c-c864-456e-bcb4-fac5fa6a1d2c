<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use App\Models\ApiLog;
use App\Models\Integration;
use App\Models\Webhook;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

class IntegrationController extends Controller
{
    /**
     * Integration dashboard
     */
    public function index()
    {
        $integrationStats = [
            'total_api_keys' => ApiKey::count(),
            'active_api_keys' => ApiKey::where('is_active', true)->count(),
            'total_api_calls_today' => ApiLog::whereDate('created_at', today())->count(),
            'total_integrations' => Integration::count(),
            'active_integrations' => Integration::where('status', 'active')->count(),
            'total_webhooks' => Webhook::count(),
            'failed_webhooks_today' => Webhook::where('status', 'failed')
                                            ->whereDate('created_at', today())
                                            ->count(),
        ];

        $recentApiCalls = ApiLog::with('user')
                               ->orderByDesc('created_at')
                               ->limit(10)
                               ->get();

        $topApiEndpoints = ApiLog::selectRaw('endpoint, COUNT(*) as call_count')
                                ->whereDate('created_at', '>=', now()->subDays(7))
                                ->groupBy('endpoint')
                                ->orderByDesc('call_count')
                                ->limit(10)
                                ->get();

        return view('admin.integrations.index', compact(
            'integrationStats', 'recentApiCalls', 'topApiEndpoints'
        ));
    }

    /**
     * API Keys management
     */
    public function apiKeys(Request $request)
    {
        $query = ApiKey::with('user');

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('key_prefix', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $apiKeys = $query->orderByDesc('created_at')->paginate(20);

        $apiKeyStats = [
            'total_keys' => ApiKey::count(),
            'active_keys' => ApiKey::where('is_active', true)->count(),
            'expired_keys' => ApiKey::where('expires_at', '<', now())->count(),
            'keys_created_this_month' => ApiKey::whereMonth('created_at', now()->month)->count(),
        ];

        return view('admin.integrations.api-keys', compact('apiKeys', 'apiKeyStats'));
    }

    /**
     * Create new API key
     */
    public function createApiKey(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'permissions' => 'required|array',
            'permissions.*' => 'string',
            'expires_at' => 'nullable|date|after:today',
            'rate_limit' => 'nullable|integer|min:1|max:10000',
        ]);

        $keyValue = 'jws_' . Str::random(32);
        $keyPrefix = substr($keyValue, 0, 12) . '...';

        $apiKey = ApiKey::create([
            'name' => $request->name,
            'description' => $request->description,
            'key_hash' => Hash::make($keyValue),
            'key_prefix' => $keyPrefix,
            'permissions' => $request->permissions,
            'expires_at' => $request->expires_at,
            'rate_limit' => $request->rate_limit ?? 1000,
            'created_by' => auth()->id(),
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API key created successfully',
            'api_key' => $keyValue,
            'key_id' => $apiKey->id,
            'warning' => 'Please save this API key securely. It will not be shown again.',
        ]);
    }

    /**
     * Revoke API key
     */
    public function revokeApiKey(ApiKey $apiKey)
    {
        $apiKey->update(['is_active' => false, 'revoked_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => 'API key revoked successfully',
        ]);
    }

    /**
     * API Logs
     */
    public function apiLogs(Request $request)
    {
        $query = ApiLog::with(['user', 'apiKey']);

        // Filters
        if ($request->filled('endpoint')) {
            $query->where('endpoint', 'like', '%' . $request->endpoint . '%');
        }

        if ($request->filled('method')) {
            $query->where('method', $request->method);
        }

        if ($request->filled('status_code')) {
            $query->where('response_code', $request->status_code);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $logs = $query->orderByDesc('created_at')->paginate(50);

        $logStats = [
            'total_calls' => ApiLog::count(),
            'calls_today' => ApiLog::whereDate('created_at', today())->count(),
            'successful_calls' => ApiLog::whereBetween('response_code', [200, 299])->count(),
            'failed_calls' => ApiLog::where('response_code', '>=', 400)->count(),
            'average_response_time' => ApiLog::avg('response_time') ?? 0,
        ];

        return view('admin.integrations.api-logs', compact('logs', 'logStats'));
    }

    /**
     * Webhooks management
     */
    public function webhooks(Request $request)
    {
        $query = Webhook::query();

        if ($request->filled('event_type')) {
            $query->where('event_type', $request->event_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $webhooks = $query->orderByDesc('created_at')->paginate(20);

        $webhookStats = [
            'total_webhooks' => Webhook::count(),
            'successful_webhooks' => Webhook::where('status', 'success')->count(),
            'failed_webhooks' => Webhook::where('status', 'failed')->count(),
            'pending_webhooks' => Webhook::where('status', 'pending')->count(),
        ];

        return view('admin.integrations.webhooks', compact('webhooks', 'webhookStats'));
    }

    /**
     * Create webhook
     */
    public function createWebhook(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'required|url',
            'event_types' => 'required|array',
            'event_types.*' => 'string',
            'secret' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        $webhook = Webhook::create([
            'name' => $request->name,
            'url' => $request->url,
            'event_types' => $request->event_types,
            'secret' => $request->secret,
            'is_active' => $request->boolean('is_active', true),
            'created_by' => auth()->id(),
        ]);

        return redirect()->route('admin.integrations.webhooks')
                        ->with('success', 'Webhook created successfully');
    }

    /**
     * Test webhook
     */
    public function testWebhook(Webhook $webhook)
    {
        try {
            $testPayload = [
                'event' => 'webhook.test',
                'data' => [
                    'message' => 'This is a test webhook',
                    'timestamp' => now()->toISOString(),
                ],
                'webhook_id' => $webhook->id,
            ];

            $response = $this->sendWebhook($webhook, $testPayload);

            return response()->json([
                'success' => true,
                'message' => 'Webhook test sent successfully',
                'response_code' => $response['status_code'],
                'response_time' => $response['response_time'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Webhook test failed: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Third-party integrations
     */
    public function thirdPartyIntegrations()
    {
        $integrations = Integration::orderBy('name')->get();

        $availableIntegrations = [
            'payment_gateways' => [
                'razorpay' => [
                    'name' => 'Razorpay',
                    'description' => 'Accept online payments',
                    'status' => $this->getIntegrationStatus('razorpay'),
                ],
                'payu' => [
                    'name' => 'PayU',
                    'description' => 'Payment gateway integration',
                    'status' => $this->getIntegrationStatus('payu'),
                ],
            ],
            'sms_providers' => [
                'twilio' => [
                    'name' => 'Twilio',
                    'description' => 'SMS notifications',
                    'status' => $this->getIntegrationStatus('twilio'),
                ],
                'msg91' => [
                    'name' => 'MSG91',
                    'description' => 'SMS service provider',
                    'status' => $this->getIntegrationStatus('msg91'),
                ],
            ],
            'email_providers' => [
                'mailgun' => [
                    'name' => 'Mailgun',
                    'description' => 'Email delivery service',
                    'status' => $this->getIntegrationStatus('mailgun'),
                ],
                'sendgrid' => [
                    'name' => 'SendGrid',
                    'description' => 'Email marketing platform',
                    'status' => $this->getIntegrationStatus('sendgrid'),
                ],
            ],
            'accounting' => [
                'tally' => [
                    'name' => 'Tally ERP',
                    'description' => 'Accounting software integration',
                    'status' => $this->getIntegrationStatus('tally'),
                ],
                'quickbooks' => [
                    'name' => 'QuickBooks',
                    'description' => 'Cloud accounting software',
                    'status' => $this->getIntegrationStatus('quickbooks'),
                ],
            ],
        ];

        return view('admin.integrations.third-party', compact('integrations', 'availableIntegrations'));
    }

    /**
     * Configure integration
     */
    public function configureIntegration(Request $request, $provider)
    {
        $request->validate([
            'settings' => 'required|array',
            'is_active' => 'boolean',
        ]);

        $integration = Integration::updateOrCreate(
            ['provider' => $provider],
            [
                'name' => $this->getProviderName($provider),
                'settings' => $request->settings,
                'status' => $request->boolean('is_active') ? 'active' : 'inactive',
                'configured_by' => auth()->id(),
                'configured_at' => now(),
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Integration configured successfully',
        ]);
    }

    /**
     * API documentation
     */
    public function apiDocumentation()
    {
        $endpoints = [
            'products' => [
                'GET /api/products' => 'List all products',
                'GET /api/products/{id}' => 'Get product details',
                'POST /api/products' => 'Create new product',
                'PUT /api/products/{id}' => 'Update product',
                'DELETE /api/products/{id}' => 'Delete product',
                'GET /api/products/search' => 'Search products',
                'GET /api/products/{id}/stock' => 'Get product stock',
            ],
            'customers' => [
                'GET /api/customers' => 'List all customers',
                'GET /api/customers/{id}' => 'Get customer details',
                'POST /api/customers' => 'Create new customer',
                'PUT /api/customers/{id}' => 'Update customer',
                'DELETE /api/customers/{id}' => 'Delete customer',
                'GET /api/customers/search' => 'Search customers',
                'GET /api/customers/{id}/analytics' => 'Get customer analytics',
            ],
            'orders' => [
                'GET /api/orders' => 'List all orders',
                'GET /api/orders/{id}' => 'Get order details',
                'POST /api/orders' => 'Create new order',
                'PUT /api/orders/{id}' => 'Update order',
                'POST /api/orders/{id}/cancel' => 'Cancel order',
            ],
            'inventory' => [
                'GET /api/inventory' => 'List inventory items',
                'PUT /api/inventory/{id}' => 'Update inventory',
                'GET /api/inventory/low-stock' => 'Get low stock items',
            ],
        ];

        return view('admin.integrations.api-documentation', compact('endpoints'));
    }

    // Helper Methods
    private function sendWebhook(Webhook $webhook, array $payload)
    {
        $startTime = microtime(true);
        
        $headers = [
            'Content-Type: application/json',
            'User-Agent: JewelSoft-Webhook/1.0',
        ];

        if ($webhook->secret) {
            $signature = hash_hmac('sha256', json_encode($payload), $webhook->secret);
            $headers[] = 'X-Webhook-Signature: sha256=' . $signature;
        }

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $webhook->url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
        ]);

        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        $responseTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds

        if ($error) {
            throw new \Exception($error);
        }

        return [
            'status_code' => $statusCode,
            'response' => $response,
            'response_time' => $responseTime,
        ];
    }

    private function getIntegrationStatus($provider): string
    {
        $integration = Integration::where('provider', $provider)->first();
        return $integration ? $integration->status : 'not_configured';
    }

    private function getProviderName($provider): string
    {
        $names = [
            'razorpay' => 'Razorpay',
            'payu' => 'PayU',
            'twilio' => 'Twilio',
            'msg91' => 'MSG91',
            'mailgun' => 'Mailgun',
            'sendgrid' => 'SendGrid',
            'tally' => 'Tally ERP',
            'quickbooks' => 'QuickBooks',
        ];

        return $names[$provider] ?? ucfirst($provider);
    }
}
