<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceItem extends Model
{
    protected $fillable = [
        'invoice_id',
        'product_id',
        'quantity',
        'unit_price',
        'discount_percentage',
        'discount_amount',
        'making_charges',
        'stone_charges',
        'taxable_amount',
        'gst_rate',
        'cgst_amount',
        'sgst_amount',
        'igst_amount',
        'total_amount',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'making_charges' => 'decimal:2',
        'stone_charges' => 'decimal:2',
        'taxable_amount' => 'decimal:2',
        'gst_rate' => 'decimal:2',
        'cgst_amount' => 'decimal:2',
        'sgst_amount' => 'decimal:2',
        'igst_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    // Relationships
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // Accessors
    public function getSubtotalAttribute()
    {
        return ($this->unit_price * $this->quantity) + $this->making_charges + $this->stone_charges;
    }

    public function getDiscountedAmountAttribute()
    {
        return $this->subtotal - $this->discount_amount;
    }

    public function getTotalGstAttribute()
    {
        return $this->cgst_amount + $this->sgst_amount + $this->igst_amount;
    }

    public function getFormattedTotalAttribute()
    {
        return '₹' . number_format($this->total_amount, 2);
    }

    // Methods
    public function calculateGst($customerState = null, $companyState = null)
    {
        $gstRate = $this->product->gst_rate ?? 3; // Default 3% for jewelry
        $taxableAmount = $this->discounted_amount;

        if ($customerState && $companyState && $customerState === $companyState) {
            // Same state - CGST + SGST
            $cgstAmount = ($taxableAmount * $gstRate) / 200; // Half of GST rate
            $sgstAmount = ($taxableAmount * $gstRate) / 200; // Half of GST rate
            $igstAmount = 0;
        } else {
            // Different state - IGST
            $cgstAmount = 0;
            $sgstAmount = 0;
            $igstAmount = ($taxableAmount * $gstRate) / 100;
        }

        $this->update([
            'gst_rate' => $gstRate,
            'taxable_amount' => $taxableAmount,
            'cgst_amount' => $cgstAmount,
            'sgst_amount' => $sgstAmount,
            'igst_amount' => $igstAmount,
            'total_amount' => $taxableAmount + $cgstAmount + $sgstAmount + $igstAmount,
        ]);
    }
}
