<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Category;
use App\Models\Metal;
use App\Models\InventoryItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view_analytics');
        $this->middleware('permission:export_analytics')->only(['export', 'exportReport']);
    }

    /**
     * Main analytics dashboard
     */
    public function index(Request $request)
    {
        $dateRange = $request->get('date_range', '30'); // Default 30 days
        $startDate = now()->subDays($dateRange);
        $endDate = now();

        // Key Performance Indicators
        $kpis = [
            'total_revenue' => $this->getTotalRevenue($startDate, $endDate),
            'total_orders' => $this->getTotalOrders($startDate, $endDate),
            'average_order_value' => $this->getAverageOrderValue($startDate, $endDate),
            'total_customers' => Customer::count(),
            'new_customers' => Customer::where('created_at', '>=', $startDate)->count(),
            'total_products' => Product::where('is_active', true)->count(),
            'low_stock_items' => $this->getLowStockCount(),
            'inventory_value' => $this->getTotalInventoryValue(),
        ];

        // Revenue Trends
        $revenueTrends = $this->getRevenueTrends($dateRange);

        // Top Performing Products
        $topProducts = $this->getTopProducts($startDate, $endDate);

        // Sales by Category
        $categoryStats = $this->getCategoryStats($startDate, $endDate);

        // Customer Insights
        $customerInsights = $this->getCustomerInsights($startDate, $endDate);

        // Inventory Analytics
        $inventoryAnalytics = $this->getInventoryAnalytics();

        // Geographic Analysis (if location data available)
        $geographicData = $this->getGeographicAnalysis($startDate, $endDate);

        return view('admin.analytics.index', compact(
            'kpis', 'revenueTrends', 'topProducts', 'categoryStats',
            'customerInsights', 'inventoryAnalytics', 'geographicData', 'dateRange'
        ));
    }

    /**
     * Sales analytics dashboard
     */
    public function sales(Request $request)
    {
        $period = $request->get('period', 'month'); // day, week, month, quarter, year
        $comparison = $request->get('comparison', 'previous'); // previous, year_ago

        $salesData = [
            'overview' => $this->getSalesOverview($period),
            'trends' => $this->getSalesTrends($period),
            'performance' => $this->getSalesPerformance($period),
            'forecasting' => $this->getSalesForecasting($period),
            'conversion' => $this->getConversionMetrics($period),
        ];

        return view('admin.analytics.sales', compact('salesData', 'period', 'comparison'));
    }

    /**
     * Product analytics dashboard
     */
    public function products(Request $request)
    {
        $timeframe = $request->get('timeframe', '90'); // days
        $startDate = now()->subDays($timeframe);

        $productAnalytics = [
            'performance' => $this->getProductPerformance($startDate, now()),
            'inventory_turnover' => $this->getInventoryTurnover($startDate, now()),
            'profitability' => $this->getProductProfitability($startDate, now()),
            'lifecycle' => $this->getProductLifecycle(),
            'recommendations' => $this->getProductRecommendations(),
        ];

        return view('admin.analytics.products', compact('productAnalytics', 'timeframe'));
    }

    /**
     * Customer analytics dashboard
     */
    public function customers(Request $request)
    {
        $segment = $request->get('segment', 'all'); // all, vip, loyal, new, at_risk
        
        $customerAnalytics = [
            'segmentation' => $this->getCustomerSegmentation(),
            'behavior' => $this->getCustomerBehavior(),
            'lifetime_value' => $this->getCustomerLifetimeValue(),
            'retention' => $this->getCustomerRetention(),
            'acquisition' => $this->getCustomerAcquisition(),
        ];

        return view('admin.analytics.customers', compact('customerAnalytics', 'segment'));
    }

    /**
     * Inventory analytics dashboard
     */
    public function inventory(Request $request)
    {
        $view = $request->get('view', 'overview'); // overview, turnover, valuation, alerts

        $inventoryAnalytics = [
            'overview' => $this->getInventoryOverview(),
            'turnover_analysis' => $this->getInventoryTurnoverAnalysis(),
            'valuation' => $this->getInventoryValuation(),
            'aging' => $this->getInventoryAging(),
            'alerts' => $this->getInventoryAlerts(),
        ];

        return view('admin.analytics.inventory', compact('inventoryAnalytics', 'view'));
    }

    /**
     * Financial analytics dashboard
     */
    public function financial(Request $request)
    {
        $period = $request->get('period', 'monthly'); // daily, weekly, monthly, quarterly, yearly

        $financialAnalytics = [
            'profitability' => $this->getProfitabilityAnalysis($period),
            'cash_flow' => $this->getCashFlowAnalysis($period),
            'expenses' => $this->getExpenseAnalysis($period),
            'ratios' => $this->getFinancialRatios(),
            'trends' => $this->getFinancialTrends($period),
        ];

        return view('admin.analytics.financial', compact('financialAnalytics', 'period'));
    }

    /**
     * Custom reports builder
     */
    public function reports(Request $request)
    {
        $reportType = $request->get('type', 'sales'); // sales, products, customers, inventory, financial
        $filters = $request->only(['start_date', 'end_date', 'category', 'metal', 'location']);

        $availableMetrics = $this->getAvailableMetrics($reportType);
        $reportData = null;

        if ($request->has('generate')) {
            $reportData = $this->generateCustomReport($reportType, $filters, $request->get('metrics', []));
        }

        return view('admin.analytics.reports', compact('reportType', 'availableMetrics', 'reportData', 'filters'));
    }

    // Helper Methods for Data Retrieval

    private function getTotalRevenue($startDate, $endDate)
    {
        return Invoice::where('status', 'paid')
                     ->whereBetween('created_at', [$startDate, $endDate])
                     ->sum('total_amount');
    }

    private function getTotalOrders($startDate, $endDate)
    {
        return Invoice::whereBetween('created_at', [$startDate, $endDate])->count();
    }

    private function getAverageOrderValue($startDate, $endDate)
    {
        return Invoice::where('status', 'paid')
                     ->whereBetween('created_at', [$startDate, $endDate])
                     ->avg('total_amount') ?? 0;
    }

    private function getLowStockCount()
    {
        return InventoryItem::where('quantity', '<=', 5)->count();
    }

    private function getTotalInventoryValue()
    {
        return InventoryItem::join('products', 'inventory_items.product_id', '=', 'products.id')
                           ->selectRaw('SUM(inventory_items.quantity * products.total_amount) as total_value')
                           ->value('total_value') ?? 0;
    }

    private function getRevenueTrends($days)
    {
        $data = Invoice::selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue')
                      ->where('status', 'paid')
                      ->where('created_at', '>=', now()->subDays($days))
                      ->groupBy('date')
                      ->orderBy('date')
                      ->get();

        return [
            'labels' => $data->pluck('date')->map(function ($date) {
                return Carbon::parse($date)->format('M d');
            })->toArray(),
            'data' => $data->pluck('revenue')->toArray(),
        ];
    }

    private function getTopProducts($startDate, $endDate)
    {
        return InvoiceItem::join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
                         ->join('products', 'invoice_items.product_id', '=', 'products.id')
                         ->where('invoices.status', 'paid')
                         ->whereBetween('invoices.created_at', [$startDate, $endDate])
                         ->selectRaw('products.name, products.sku, SUM(invoice_items.quantity) as total_sold, SUM(invoice_items.total_amount) as total_revenue')
                         ->groupBy('products.id', 'products.name', 'products.sku')
                         ->orderByDesc('total_revenue')
                         ->limit(10)
                         ->get();
    }

    private function getCategoryStats($startDate, $endDate)
    {
        return InvoiceItem::join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
                         ->join('products', 'invoice_items.product_id', '=', 'products.id')
                         ->join('categories', 'products.category_id', '=', 'categories.id')
                         ->where('invoices.status', 'paid')
                         ->whereBetween('invoices.created_at', [$startDate, $endDate])
                         ->selectRaw('categories.name, SUM(invoice_items.total_amount) as revenue, COUNT(*) as items_sold')
                         ->groupBy('categories.id', 'categories.name')
                         ->orderByDesc('revenue')
                         ->get();
    }

    private function getCustomerInsights($startDate, $endDate)
    {
        return [
            'new_customers' => Customer::whereBetween('created_at', [$startDate, $endDate])->count(),
            'repeat_customers' => Customer::whereHas('invoices', function ($q) use ($startDate, $endDate) {
                $q->whereBetween('created_at', [$startDate, $endDate]);
            }, '>=', 2)->count(),
            'average_customer_value' => Customer::avg('total_purchase_amount'),
            'top_customers' => Customer::orderByDesc('total_purchase_amount')->limit(5)->get(),
        ];
    }

    private function getInventoryAnalytics()
    {
        return [
            'total_items' => InventoryItem::sum('quantity'),
            'total_value' => $this->getTotalInventoryValue(),
            'low_stock_items' => $this->getLowStockCount(),
            'out_of_stock_items' => InventoryItem::where('quantity', '<=', 0)->count(),
            'categories_breakdown' => $this->getInventoryByCategory(),
        ];
    }

    private function getInventoryByCategory()
    {
        return InventoryItem::join('products', 'inventory_items.product_id', '=', 'products.id')
                           ->join('categories', 'products.category_id', '=', 'categories.id')
                           ->selectRaw('categories.name, SUM(inventory_items.quantity) as total_quantity, SUM(inventory_items.quantity * products.total_amount) as total_value')
                           ->groupBy('categories.id', 'categories.name')
                           ->orderByDesc('total_value')
                           ->get();
    }

    private function getGeographicAnalysis($startDate, $endDate)
    {
        // This would be enhanced with actual geographic data
        return [
            'sales_by_location' => [],
            'customer_distribution' => [],
        ];
    }

    // Placeholder methods for advanced analytics (would be implemented based on specific requirements)
    private function getSalesOverview($period) { return []; }
    private function getSalesTrends($period) { return []; }
    private function getSalesPerformance($period) { return []; }
    private function getSalesForecasting($period) { return []; }
    private function getConversionMetrics($period) { return []; }
    private function getProductPerformance($startDate, $endDate) { return []; }
    private function getInventoryTurnover($startDate, $endDate) { return []; }
    private function getProductProfitability($startDate, $endDate) { return []; }
    private function getProductLifecycle() { return []; }
    private function getProductRecommendations() { return []; }
    private function getCustomerSegmentation() { return []; }
    private function getCustomerBehavior() { return []; }
    private function getCustomerLifetimeValue() { return []; }
    private function getCustomerRetention() { return []; }
    private function getCustomerAcquisition() { return []; }
    private function getInventoryOverview() { return []; }
    private function getInventoryTurnoverAnalysis() { return []; }
    private function getInventoryValuation() { return []; }
    private function getInventoryAging() { return []; }
    private function getInventoryAlerts() { return []; }
    private function getProfitabilityAnalysis($period) { return []; }
    private function getCashFlowAnalysis($period) { return []; }
    private function getExpenseAnalysis($period) { return []; }
    private function getFinancialRatios() { return []; }
    private function getFinancialTrends($period) { return []; }
    private function getAvailableMetrics($reportType) { return []; }
    private function generateCustomReport($reportType, $filters, $metrics) { return []; }
}
