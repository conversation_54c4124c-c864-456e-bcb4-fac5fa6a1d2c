<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class POSTransactionItem extends Model
{
    use HasFactory;

    protected $table = 'pos_transaction_items';

    protected $fillable = [
        'pos_transaction_id',
        'product_id',
        'quantity',
        'unit_price',
        'discount_amount',
        'total_price',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total_price = ($item->quantity * $item->unit_price) - $item->discount_amount;
        });
    }

    /**
     * Relationships
     */
    public function posTransaction(): BelongsTo
    {
        return $this->belongsTo(POSTransaction::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Accessors
     */
    public function getDiscountPercentageAttribute()
    {
        $originalPrice = $this->quantity * $this->unit_price;
        if ($originalPrice > 0) {
            return round(($this->discount_amount / $originalPrice) * 100, 2);
        }
        return 0;
    }

    public function getNetUnitPriceAttribute()
    {
        if ($this->quantity > 0) {
            return ($this->total_price / $this->quantity);
        }
        return 0;
    }

    /**
     * Business Logic Methods
     */
    public function updateQuantity($newQuantity)
    {
        $this->update([
            'quantity' => $newQuantity,
            'total_price' => ($newQuantity * $this->unit_price) - $this->discount_amount,
        ]);

        return $this;
    }

    public function updatePrice($newPrice)
    {
        $this->update([
            'unit_price' => $newPrice,
            'total_price' => ($this->quantity * $newPrice) - $this->discount_amount,
        ]);

        return $this;
    }

    public function applyDiscount($discountAmount)
    {
        $this->update([
            'discount_amount' => $discountAmount,
            'total_price' => ($this->quantity * $this->unit_price) - $discountAmount,
        ]);

        return $this;
    }

    public function getSummary()
    {
        return [
            'id' => $this->id,
            'product_name' => $this->product->name,
            'product_code' => $this->product->product_code,
            'quantity' => $this->quantity,
            'unit_price' => $this->unit_price,
            'discount_amount' => $this->discount_amount,
            'discount_percentage' => $this->discount_percentage,
            'total_price' => $this->total_price,
            'net_unit_price' => $this->net_unit_price,
            'notes' => $this->notes,
        ];
    }
}
