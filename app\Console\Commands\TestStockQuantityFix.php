<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;

class TestStockQuantityFix extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:stock-quantity-fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the stock_quantity column fix';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing stock_quantity column fix');
        $this->newLine();

        try {
            // Test 1: Try the original query that was failing
            $this->info('Test 1: Testing featured products query (original failing query)');
            $featuredProducts = Product::with(['category', 'images', 'inventory'])
                                     ->where('is_active', true)
                                     ->where('is_featured', true)
                                     ->whereHas('inventory', function ($query) {
                                         $query->where('quantity_available', '>', 0);
                                     })
                                     ->limit(8)
                                     ->get();
            
            $this->info("✅ Featured products query successful! Found {$featuredProducts->count()} products");

        } catch (\Exception $e) {
            $this->error("❌ Featured products query failed: " . $e->getMessage());
        }

        try {
            // Test 2: Test the stock_quantity accessor
            $this->info('Test 2: Testing stock_quantity accessor');
            $product = Product::with('inventory')->first();
            
            if ($product) {
                $stockQuantity = $product->stock_quantity;
                $this->info("✅ stock_quantity accessor works! Product '{$product->name}' has stock: {$stockQuantity}");
            } else {
                $this->warn("⚠️  No products found to test accessor");
            }

        } catch (\Exception $e) {
            $this->error("❌ stock_quantity accessor failed: " . $e->getMessage());
        }

        try {
            // Test 3: Test new arrivals query
            $this->info('Test 3: Testing new arrivals query');
            $newArrivals = Product::with(['category', 'images', 'inventory'])
                                 ->where('is_active', true)
                                 ->whereHas('inventory', function ($query) {
                                     $query->where('quantity_available', '>', 0);
                                 })
                                 ->orderBy('created_at', 'desc')
                                 ->limit(6)
                                 ->get();
            
            $this->info("✅ New arrivals query successful! Found {$newArrivals->count()} products");

        } catch (\Exception $e) {
            $this->error("❌ New arrivals query failed: " . $e->getMessage());
        }

        try {
            // Test 4: Test search query
            $this->info('Test 4: Testing search query');
            $searchResults = Product::with(['category', 'images', 'inventory'])
                                   ->where('is_active', true)
                                   ->whereHas('inventory', function ($query) {
                                       $query->where('quantity_available', '>', 0);
                                   })
                                   ->where(function ($q) {
                                       $q->where('name', 'like', "%test%")
                                         ->orWhere('description', 'like', "%test%");
                                   })
                                   ->limit(5)
                                   ->get();
            
            $this->info("✅ Search query successful! Found {$searchResults->count()} products");

        } catch (\Exception $e) {
            $this->error("❌ Search query failed: " . $e->getMessage());
        }

        $this->newLine();
        $this->info('🎉 Stock quantity fix test completed!');
        
        return 0;
    }
}