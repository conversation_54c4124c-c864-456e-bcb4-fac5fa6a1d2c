<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MetalRate extends Model
{
    protected $fillable = [
        'metal_id',
        'rate_per_gram',
        'effective_date',
        'source',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'rate_per_gram' => 'decimal:2',
        'effective_date' => 'datetime',
    ];

    // Relationships
    public function metal(): BelongsTo
    {
        return $this->belongsTo(Metal::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeCurrent($query)
    {
        return $query->where('effective_date', '<=', now())
                    ->orderBy('effective_date', 'desc');
    }

    public function scopeForMetal($query, $metalId)
    {
        return $query->where('metal_id', $metalId);
    }

    public function scopeForDate($query, $date)
    {
        return $query->where('effective_date', '<=', $date);
    }

    // Accessors
    public function getFormattedRateAttribute()
    {
        return '₹' . number_format($this->rate_per_gram, 2);
    }

    public function getSourceDisplayAttribute()
    {
        $sources = [
            'manual' => 'Manual Entry',
            'api' => 'External API',
            'import' => 'Data Import',
            'system' => 'System Generated',
        ];

        return $sources[$this->source] ?? ucfirst($this->source);
    }
}
